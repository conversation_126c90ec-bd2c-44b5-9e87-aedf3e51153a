<template>
  <div class="column relative-position storage-card">
    <div class="flex">
      <div class="text-primary text-huangyou fs-30 title">存储概览</div>
    </div>
    <div ref="chartDom" class="col chart"></div>
  </div>
</template>

<script setup>
import { useEchart } from 'composables/echart.js'
import TableCard from 'components/TableCard.vue'
const props = defineProps({
  saving:{
    type:Object,
    default:()=>({ //默认值，父组件不传时生效
      used:5,
      rest:1
    })
  },
})
console.log("props",props)
const { chartDom } = useEchart({
  tooltip: {
    trigger: 'item',
  },
  series: [
    {
      type: 'pie',
      roseType: 'radius',
      label: {
        formatter: '{b|{b}}{d|{d}%}',
        rich: {
          b: {
            fontSize: 14,
            padding: [0, 6, 0, 0],
          },
          d: {
            color: '#FF9000',
            fontSize: 18,
            fontWeight: '800',
          },
        },
      },
      labelLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.6)',
        },
      },
      itemStyle: {
        color: '#FFA800',
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 200,
      },
      data: [
        { value: props.saving.used, name: '用户上传文件' },
        { value: props.saving.rest, name: '剩余空间' },
      ].sort(function (a, b) {
        return a.value - b.value
      }),
    },
  ],
})

</script>

<style lang="scss" scoped>
.storage-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 118px;
  height: 46px;
  border-right: 4px solid $primary;
  border-bottom: 4px solid $primary;
}

.title {
  padding: 6px 6px 6px 16px;
  border-left: 4px solid $primary;
  border-top: 4px solid $primary;
}
</style>
