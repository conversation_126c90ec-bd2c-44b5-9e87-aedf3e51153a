[{"name": "bc_batch_size", "description": "Batch size of training process.", "abbreviation": "bbs", "type": "int", "default": 256}, {"name": "bc_epoch", "description": "Number of epcoh for the training process", "abbreviation": "bep", "type": "int", "default": 500}, {"name": "policy_hidden_features", "description": "Number of neurons per layer of the policy network.", "abbreviation": "phf", "type": "int", "default": 256}, {"name": "policy_hidden_layers", "description": "Depth of policy network.", "abbreviation": "phl", "type": "int", "default": 4, "search_mode": "grid", "search_values": [3, 4, 5]}, {"name": "policy_backbone", "description": "Backbone of policy network.", "abbreviation": "pb", "type": "str", "default": "res", "search_mode": "grid", "search_values": ["mlp", "res"]}, {"name": "g_lr", "description": "Initial learning rate of the training process.", "type": "float", "default": 0.0001, "search_mode": "continuous", "search_values": [1e-06, 0.001]}, {"name": "loss_type", "description": "Bc support different loss function(\"log_prob\", \"mae\", \"mse\").", "type": "str", "default": "log_prob"}]