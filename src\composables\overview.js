import { ref } from 'vue'
import { api } from 'boot/axios'

export function useOverview() {
  const bytePerGB = 1073741824
  const bytePerTB = 1073741824 * 1024

  const loading = ref(false)
  const indicator = ref(null)
  const resource = ref(null)
  const saving = ref(null)

  getOverview()

  function getOverview() {
    loading.value = true
    api
      .get('backend/overview/')
      .then(res => {
        console.log(".then!")
        indicator.value = res.data.overall
        saving.value = res.data.saving

        const capacity = res.data.resource.capacity
        const allocatable = res.data.resource.allocatable
        const usageRatio = res.data.resource.usage_ratio

        resource.value = {
          gpu: {
            capacity: capacity.gpu.toFixed(),
            allocatable: allocatable.gpu.toFixed(),
            used: capacity.gpu - allocatable.gpu,
            usedRatio: (usageRatio.gpu * 100).toFixed(),
          },
          cpu: {
            capacity: capacity.cpu.toFixed(),
            allocatable: allocatable.cpu.toFixed(),
            used: capacity.cpu - allocatable.cpu,
            usedRatio: (usageRatio.cpu * 100).toFixed(),
          },
          memory: {
            capacity: (capacity.memory / bytePerGB).toFixed(2),
            allocatable: (allocatable.memory / bytePerGB).toFixed(2),
            used: ((capacity.memory - allocatable.memory) / bytePerGB).toFixed(2),
            usedRatio: (usageRatio.memory * 100).toFixed(),
          },
          disk: {
            capacity: (capacity.disk / bytePerTB).toFixed(2),
            allocatable: (allocatable.disk / bytePerTB).toFixed(2),
            used: ((capacity.disk - allocatable.disk) / bytePerTB).toFixed(2),
            usedRatio: (usageRatio.disk * 100).toFixed(),
          },
        }
        loading.value = false
      })
      .catch(() => {
        loading.value = false
      })
  }

  return {
    loading,
    indicator,
    resource,
    saving,
  }
}
