<template>
  <div class="flex flex-center window-height">
    <ThemeToggle class="absolute-top-right q-ma-lg" />

    <div class="column items-center q-py-lg">
      <!-- <SvgTitle /> -->
      <div class="text-h3 text-weight-bold q-mb-md">{{ title }}</div>
      <SvgLoginLine />
      <q-form @submit="login" class="q-gutter-y-md q-my-lg login-form">
        <q-input
          v-model.trim="account"
          :rules="[val => !!val || '请输入用户名']"
          placeholder="请输入用户名"
          maxlength="50"
          lazy-rules
          outlined
        >
          <template v-slot:prepend>
            <q-icon name="person" />
          </template>
        </q-input>
        <q-input
          v-model.trim="password"
          :type="showPwd ? 'text' : 'password'"
          :rules="[val => !!val || '请输入密码']"
          placeholder="请输入密码"
          maxlength="50"
          autocomplete
          lazy-rules
          outlined
        >
          <template v-slot:prepend>
            <q-icon name="lock" />
          </template>
          <template v-slot:append>
            <q-btn @click="showPwd = !showPwd" :icon="showPwd ? 'visibility_off' : 'visibility'" round flat />
          </template>
        </q-input>
        <q-btn :loading="loading" type="submit" label="登录" color="primary" text-color="black" class="full-width" />
      </q-form>
      <SvgLoginLine style="transform: rotateX(180deg)" />
      <!-- <SvgPolixir height="40" /> -->
      <div class="text-h5 text-weight-bold" height="40">{{ company }}</div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { api } from 'boot/axios'
import { usePlugin } from 'composables/plugin.js'
import ThemeToggle from 'components/ThemeToggle.vue'
import SvgTitle from 'assets/images/svg/title.svg'  
import SvgLoginLine from 'assets/images/svg/login-line.svg'
import SvgPolixir from 'assets/images/svg/polixir.svg'

const title = import.meta.env.VITE_TITLE;
const company = import.meta.env.VITE_COMPANY;

const { notify, localStorage } = usePlugin()
const router = useRouter()

const account = ref('')
const password = ref('')
const showPwd = ref(false)
const loading = ref(false)

function login() {
  loading.value = true
  api
    .post('user/login/', {
      username: account.value,
      password: password.value,
    })
    .then(res => {
      loading.value = false
      localStorage.set('token', res.access)
      localStorage.set('username', res.user)
      router.push('/')
    })
    .catch(() => {
      loading.value = false
      notify('账号或密码错误')
    })
}
</script>

<style lang="scss" scoped>
.login-form {
  width: 300px;
}
</style>
