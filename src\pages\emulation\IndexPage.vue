<template>
  <div class="flex no-wrap emulation">
    <img src="~assets/images/warship.jpeg" height="150" />
    <div class="q-px-md column justify-center">
      <div class="text-bold fs-20">{{ emulator.name }}
        <q-btn @click="changeEmulater" class="text-bold fs-16 q-ml-sm" color="primary" flat>
          切换
        </q-btn>
      </div>
      <div class="q-mt-sm desc">
        {{ emulator.desc }}
      </div>
    </div>
  </div>
  <ScenarioCard v-if="emulator.id" :emulator="emulator" />
</template>

<script setup>
import { ref, watch } from 'vue'
import { date } from 'quasar'
import { usePlugin } from 'composables/plugin'
import { api } from 'boot/axios'
import ScenarioCard from './ScenarioCard.vue'

const { notify } = usePlugin()

const emulators = ref([])
const emulator = ref({})


getEmulaters()

function getEmulaters() {
  api
    .get('backend/emulators/')
    .then(res => {
      emulators.value = (res?.results ?? []).map(item => {
        const { id, name, desc, creater, emulator_id: emulatorId, config_file: configFile, base_image: baseImage, create_time: createTime } = item
        return {
          id,
          emulatorId,
          name,
          desc,
          creater,
          configFile,
          baseImage,
          createTime: date.formatDate(createTime, 'YYYY-MM-DD HH:mm:ss'),
        }
      })
      emulator.value = emulators.value[0]
    })
    .catch((err) => {
      console.error(err)
      notify("获取仿真器列表失败")
    })
}

function changeEmulater() {
  const currentIndex = emulators.value.findIndex((e) => e.id === emulator.value.id);
  const nextIndex = (currentIndex + 1) % emulators.value.length;
  emulator.value = emulators.value[nextIndex];
}

</script>

<style lang="scss" scoped>
.emulation {
  background-color: #eaeaea;

  .desc {
    color: rgba(0, 0, 0, 0.6);
    line-height: 20px;
  }
}

.body--dark {
  .emulation {
    background-color: rgba(255, 255, 255, 0.2);

    .desc {
      color: rgba(255, 255, 255, 0.6);
    }
  }
}
</style>
