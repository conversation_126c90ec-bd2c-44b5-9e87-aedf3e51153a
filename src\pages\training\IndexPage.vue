<template>
  <TableCard :columns="columns" title="训练列表" api-url="backend/tasks/" :interval="10" ref="table">
    <template #action>
      <q-btn
        @click="addTraining"
        label="添加训练"
        icon="add"
        color="primary"
        text-color="black"
        padding="8px 40px"
        class="skew-both"
      />
    </template>

    <template #body-cell-status="props">
      <q-td :props="props">
        <span :class="`text-${trainingStatus[props.value]?.color}`" class="fs-16">
          {{ trainingStatus[props.value]?.label }}
        </span>
      </q-td>
    </template>

    <template #body-cell-action="props">
      <q-td :props="props">
        <q-btn
          @click="showLog(props.row.id)"
          label="日志"
          color="primary"
          class="q-ml-md"
          flat
          dense
        />
        <template v-if="props.row.status == 2">
          <q-btn
            @click="suspendTraining(props.row)"
            label="中断"
            color="primary"
            class="q-ml-md"
            flat
            dense
          />
        </template>
        <template v-if="props.row.status == 11">
          <q-btn
            v-if="props.row.status == 11"
            @click="resumeTraining(props.row, '恢复')"
            label="恢复"
            color="primary"
            class="q-ml-md"
            flat
            dense
          />
        </template>
        <q-btn
          v-if="props.row.status == 3"
          @click="resumeTraining(props.row)"
          label="恢复"
          color="primary"
          class="q-ml-md"
          flat
          dense
        />
        <q-btn
          v-if="props.row.status < 4"
          @click="onRelease(props.row.name)"
          label="释放"
          color="primary"
          class="q-ml-md"
          flat
          dense
        />
        <q-btn :to="`/training/detail/${props.row.id}`" label="详情" color="primary" class="q-ml-md" flat dense />
        <q-btn @click="onDelete(props.row.id)" label="删除" color="primary" class="q-ml-md" flat dense />
      </q-td>
    </template>
  </TableCard>
</template>

<script setup>
import { ref } from 'vue'
import { date } from 'quasar'
import { api } from 'boot/axios'
import { usePlugin } from 'composables/plugin'
import TableCard from 'components/TableCard.vue'
import TrainingDialog from './TrainingDialog.vue'
import LogDialog from './LogDialog.vue'
import { trainingStatus } from 'assets/const'
import { convertSecondsToHMS } from 'assets/utils'

const table = ref(null)

const { customComponentDialog, dialog, notify } = usePlugin()

const columns = [
  {
    name: 'name',
    field: 'name',
    label: '训练名称',
    align: 'center',
  },
  {
    name: 'allocation',
    label: '资源配置',
    format: (val, row) =>
      `${row.actor_num * row.actor_per_cpu + row.learner_num * row.learner_per_cpu} CPU,
      ${row.actor_num * row.actor_per_gpu + row.learner_num * row.learner_per_gpu} GPU
      ${row.actor_num * row.actor_per_memory + row.learner_num * row.learner_per_memory}G 内存`,
    align: 'center',
  },
  {
    name: 'creator',
    field: 'creater_name',
    label: '创建人员',
    align: 'center',
  },
  {
    name: 'startTime',
    field: 'start_time',
    format: val => date.formatDate(val, 'YYYY-MM-DD HH:mm:ss'),
    sortable: true,
    label: '开始时间',
    align: 'center',
  },
  {
    name: 'runningTime',
    field: 'running_time',
    format: val => convertSecondsToHMS(val),
    sortable: true,
    label: '训练时长',
    align: 'center',
  },
  {
    name: 'status',
    field: 'status',
    sortable: true,
    label: '训练状态',
    align: 'center',
    slot: true,
  },
  {
    name: 'action',
    label: '操作栏',
    align: 'center',
    slot: true,
  },
]

function addTraining() {
  customComponentDialog(TrainingDialog).onOk(table.value.getRows)
}

function pauseTraining(row, label) {
  dialog(`确认${label}吗？`).onOk(() => {
    api
     .post(`backend/tasks/suspending/`, {
        task_id: row.task_id,
      })
     .then(() => {
        notify('操作成功', 'positive')
        table.value.getRows()
      })
     .catch(() => {
        notify('操作失败')
      })
    })
}

function resumeTraining(row, label) {
  dialog(`确认${label}吗？`).onOk(() => {
    api
     .post(`backend/tasks/resume/`, {
        task_id: row.task_id,
      })
     .then(() => {
        notify('操作成功', 'positive')
        table.value.getRows()
      })
     .catch(() => {
        notify('操作失败')
      })
  })
}

function updateTrainingStatus(row, status, label) {
  dialog(`确认${label}吗？`).onOk(() => {
    api
      .patch(`backend/tasks/${row.id}/`, {
        name: row.name,
        status,
      })
      .then(() => {
        notify('操作成功', 'positive')
        table.value.getRows()
      })
      .catch(() => {
        notify('操作失败')
      })
  })
}

function suspendTraining(row) {
  dialog('确认中断吗？').onOk(() => {
    api
      .post(`backend/tasks/suspending/`, {
        task_id: row.task_id,
      })
      .then(() => {
        notify('中断成功', 'positive')
        table.value.getRows()
      })
      .catch(() => {
        notify('中断失败')
      })
  })
}

function showLog(rowId) {
  customComponentDialog(LogDialog, {id: rowId})
}

function onRelease(name) {
  dialog('确认释放吗？').onOk(() => {
    api
      .post(`backend/tasks/release/`, {
        name,
      })
      .then(() => {
        notify('释放成功', 'positive')
        table.value.getRows()
      })
      .catch(() => {
        notify('释放失败')
      })
  })
}

function onDelete(id) {
  dialog('确认删除吗？').onOk(() => {
    api
      .delete(`backend/tasks/${id}/`)
      .then(() => {
        notify('删除成功', 'positive')
        table.value.getRows()
      })
      .catch(() => {
        notify('删除失败')
      })
  })
}
</script>
