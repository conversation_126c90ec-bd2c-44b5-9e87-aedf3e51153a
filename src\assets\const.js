const colors = ['#FF9000', '#7DFFD0', '#FF80EB', '#B7B7B7']

const trainingStatus = [
  { color: 'info', label: '队列中' },
  { color: 'info', label: '初始化' },
  { color: 'info', label: '运行中' },
  { color: 'warning', label: '暂停' },
  { color: 'warning', label: '终止' },
  { color: 'warning', label: '耗尽' },
  { color: 'positive', label: '成功' },
  { color: 'negative', label: '失败' },
  { color: 'negative', label: '异常' },
  { color: 'positive', label: '完成' },
  { color: 'info', label: '恢复' },
  { color: 'info', label: '暂停中...' },
  { color: 'info', label: '恢复中...' },
  { color: 'info', label: '终止中...' },
  { color: 'info', label: '删除中...' },
]

const deductionStatus = [
  { color: 'info', label: '初始化' },
  { color: 'info', label: '队列中' },
  { color: 'info', label: '进行中' },
  { color: 'positive', label: '成功' },
  { color: 'negative', label: '失败' }, 
]

const emulatorOptions = [
  {
    value: 1,
    label: '海军军事演练仿真器',
  },
]

export { colors, trainingStatus, deductionStatus, emulatorOptions }
