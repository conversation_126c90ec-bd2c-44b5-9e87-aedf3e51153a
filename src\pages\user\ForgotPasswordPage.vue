<template>
  <div class="flex flex-center window-height" style="gap: 48px;">
    <!-- Left Side -->
    <div class="rounded-borders shadow-2 flex flex-center" style="width: 28%; min-width: 500px; min-height: 500px;">
      <q-img
        :src="smartModelImage"
        style="width: 100%; height: 100%; object-fit: contain;"
        contain
      />
    </div>

    <!-- Right Side Password Reset Form -->
    <q-card flat bordered class="q-pa-lg shadow-2" style="width: 30%; max-width: 500px">
      <q-card-section class="text-center">
        <div class="labelColor">找回密码</div>
      </q-card-section>

      <q-card-section>
        <q-input v-model="username" label="账号" outlined class="q-mb-sm" />
        <q-input v-model="idCard" label="身份证" outlined class="q-mb-sm"
                 mask="##################" :rules="[val => val.length === 18 || '身份证号必须为18位']" />
        <q-input v-model="phone" label="电话" outlined class="q-mb-sm"
                 mask="###########" :rules="[val => val.length === 11 || '手机号必须为11位']" />
        <q-input v-model="newPassword" label="新密码" type="password" outlined class="q-mb-sm" />
        <q-input v-model="confirmPassword" label="确认密码" type="password" outlined class="q-mb-sm" />

        <div class="row items-center q-mb-sm">
          <q-space />
          <q-btn flat label="返回登录" size="sm" @click="backToLogin" class="text-primary" />
        </div>

        <q-btn :loading="loading" label="重置密码" color="primary" class="full-width" @click="resetPassword" />
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { api } from 'boot/axios'
import { usePlugin } from 'composables/plugin.js'

const { notify } = usePlugin()
const router = useRouter()

import smartModelImage from '../../assets/images/smart_model_image.png'

const username = ref('')
const idCard = ref('')
const phone = ref('')
const newPassword = ref('')
const confirmPassword = ref('')
const loading = ref(false)

function resetPassword() {
  if (!username.value || !idCard.value || !phone.value || !newPassword.value || !confirmPassword.value) {
    notify('请填写完整信息')
    return
  }

  if (idCard.value.length !== 18) {
    notify('请输入正确的身份证号码')
    return
  }

  if (phone.value.length !== 11) {
    notify('请输入正确的手机号码')
    return
  }

  if (newPassword.value !== confirmPassword.value) {
    notify('两次输入的密码不一致')
    return
  }

  loading.value = true
  api
    .post('user/reset_password/', {
      username: username.value,
      id_card: idCard.value,
      phone: phone.value,
      new_password: newPassword.value,
    })
    .then(() => {
      loading.value = false
      notify('密码重置成功，请使用新密码登录', 'positive')
      router.push('/user/login')
    })
    .catch(error => {
      loading.value = false
      notify(error.response?.data?.msg || '密码重置失败，请检查输入信息是否正确')
    })
}

function backToLogin() {
  router.push('/user/login')
}
</script>

<style scoped>
.text-primary {
  color: #409eff;
}

.t{
  font-size: .125rem
}
</style>
