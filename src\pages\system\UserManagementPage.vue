<template>
  <div class="q-pa-md" style="padding-left: 0;padding-right: 0;">
    <!-- 整体卡片，包含标题、左侧菜单、右侧内容 -->
    <q-card flat bordered class="q-mb-md">
      <!-- 标题 -->
      <q-card-section class="borderBot">
        <div class="labelColor">用户管理</div>
      </q-card-section>

      <!-- 主体内容：左右结构 -->
      <q-card-section class="q-pt-none noPading">
        <div class="row">
          <!-- 左侧单位菜单（expansion-item风格） -->
          <div style="width:3.5rem; min-width:2.75rem;" class="leftMenu">
            <q-card flat bordered class="noBorder">
              <q-card-section class="row items-center q-pb-none" style="margin-bottom: 16px;">
                <div class="labelColor">部门列表</div>
              </q-card-section>

              <q-card-section class="q-pt-none q-pb-none" style="margin-bottom: 20px;">
                <q-input v-model="deptSearch" placeholder="搜索单位" dense outlined clearable 
                  @keyup="onDeptSearchKeyup" @input="onDeptSearch" @clear="handleClearSearch">
                  <template v-slot:append>
                    <q-btn flat dense icon="search" @click="onDeptSearch" />
                  </template>
                </q-input>
              </q-card-section>

              <q-list class="q-pt-none">
                <q-expansion-item class="nav-item" v-for="node in filteredDeptTree" :key="node.id" :label="node.label"
                  expand-separator>
                  <q-list>
                    <q-item v-for="child in node.children" :key="child.id" clickable class="sub-nav-link"
                      :class="{ active: selectedDeptId === child.id }" @click="selectedDeptId = child.id">
                      <q-item-section>{{ child.label }}</q-item-section>
                    </q-item>
                  </q-list>
                </q-expansion-item>
                <!-- 无搜索结果提示 -->
                <div v-if="deptSearch && filteredDeptTree.length === 0" class="no-results">
                  无匹配部门
                </div>
              </q-list>
            </q-card>
          </div>
          <!-- 右侧主内容 -->
          <div class="col">
            <!-- 第一行：用户名、手机号、用户状态、查询、重置 -->
            <div class="row q-col-gutter-md items-center pading16" style="padding-right: 40px !important;">
              <div class="flexbox customFlexBox">
                <div class="labelT">用户名称</div>
                <q-input style="flex: 1;" v-model="searchName" :label="searchName ? '' : '请输入'" outlined dense />
              </div>
              <div class="flexbox customFlexBox">
                <div class="labelT">手机号码</div>
                <q-input style="flex: 1;" v-model="searchPhone" :label="searchPhone ? '' : '请输入'" outlined dense />
              </div>
              <div class="flexbox customFlexBox">
                <div class="labelT">用户状态</div>
                <q-select style="flex: 1;" v-model="userStatus" :options="statusOptions"
                  :label="userStatus ? '' : '请选择'" outlined dense emit-value map-options clearable />
              </div>
              <div class="flexbox" style="justify-content: start;flex:0 0 auto;">
                <q-btn class="roundBox" style="margin-right: 15px;" color="primary" label="查询" @click="queryUsers" />
                <q-btn style="margin-left: auto;" class="roundBox grayBox" outline color="primary" label="重置"
                  @click="resetFilters" />
              </div>
            </div>
            <!-- 第二行：右侧新增用户、导出用户按钮 -->
            <div class="row" style="padding-right: 40px;">
              <div class="col-12 flexCenter" style="justify-content: flex-end;">
                <q-btn color="primary" label="刷新" class="roundBox" style="margin-right: 10px;" />
                <q-btn color="primary" label="新增用户" @click="addUser('add')" class="roundBox"
                  style="margin-right: 10px;" />
                <q-btn label="导出用户" @click="exportUsers" class="roundBox grayBox" />
              </div>
            </div>
            <!-- 用户表格 -->
            <q-card-section>
              <q-table :rows="userList" :columns="columns" row-key="id" selection="multiple" v-model:selected="selected"
                :pagination="pagination.value" :loading="loading" :rows-per-page-options="[0]" hide-selection-column>
                <template v-slot:body-cell-role="props">
                  <q-td class="text-center">
                    <q-badge class="purpleBox" color="purple" v-if="props.row.is_superuser">超级管理员</q-badge>
                    <q-badge class="organgeBox" color="blue" v-else-if="props.row.is_staff">管理员</q-badge>
                    <q-badge class="greyBox" color="grey" v-else>普通用户</q-badge>
                  </q-td>
                </template>
                <template v-slot:body-cell-actions="props">
                  <q-td style="display: flex; justify-content: space-between;align-items: center;">
                    <div class="imgEl edit" @click="addUser('edit', props.row)">
                      <q-tooltip anchor="top middle" :offset="[0, 40]" style="background-color: rgba(0,0,0,.7) !important;
                        color: white;border-radius: 5px;
                        font-size: .175rem;">
                        编辑
                      </q-tooltip>
                    </div>
                    <div class="imgEl user" @click="assignRole(props.row)">
                      <q-tooltip anchor="top middle" :offset="[0, 40]" style="background-color: rgba(0,0,0,.7) !important;
                        color: white;border-radius: 5px;
                         font-size: .175rem;">
                        分配角色
                      </q-tooltip>
                    </div>
                    <div class="imgEl refresh" @click="resetPassword(props.row)">
                      <q-tooltip anchor="top middle" :offset="[0, 40]" style="background-color: rgba(0,0,0,.7) !important;
                        color: white;border-radius: 5px;font-size: .175rem;">
                        重置密码
                      </q-tooltip>
                    </div>
                    <div class="imgEl delete" @click="confirmDeleteUser(props.row)">
                      <q-tooltip anchor="top middle" :offset="[0, 40]" style="background-color: rgba(0,0,0,.7) !important;
                        color: white;border-radius: 5px;font-size: .175rem;">
                        删除用户
                      </q-tooltip>
                    </div>
                  </q-td>
                </template>
                <template v-slot:bottom>
                  <div class="row items-center full-width paginationEl">
                    <div style="margin-right: 20px;">总计 {{ pagination.total }} 条</div>
                    <div>
                      <q-select class="unsetHeight" v-model="pagination.rowsPerPage" :options="[5, 10,]" outlined dense
                        options-dense emit-value map-options style="min-width: 100px"
                        @update:model-value="onRowsPerPageChange">
                        <template v-slot:selected>
                          {{ pagination.rowsPerPage }}/页
                        </template>
                      </q-select>
                    </div>
                    <q-pagination class="unsetHeight" style="margin-left:1rem;margin-right: 1rem;"
                      v-model="pagination.page" :max="Math.ceil(pagination.total / pagination.rowsPerPage)"
                      :max-pages="5" boundary-numbers direction-links />
                    <div class="flexbox">
                      <div style="margin-right:.125rem;">跳到</div>
                      <div class="roundBox">
                        <q-input class="dynamic-label-input" v-model="jumpText" style="width:.625rem;" dense
                          @keyup.enter="goJump">
                        </q-input>
                      </div>
                      <q-btn class="custom-btn" label="跳转" @click="goJump" />
                    </div>
                  </div>
                </template>
              </q-table>
            </q-card-section>
          </div>
        </div>
      </q-card-section>

      <!-- 删除确认对话框 -->
      <q-dialog v-model="deleteDialog" persistent>
        <q-card class="blackCard">
          <q-card-section class="row items-center pad12">
            删除用户
          </q-card-section>
          <q-card-section class="row items-center">
            确认删除用户？
          </q-card-section>

          <q-card-actions align="right">
            <q-btn flat label="确定" color="primary" class="blueBtn" @click="deleteUser" />
            <q-btn flat label="取消" color="primary" class="grayBox" v-close-popup />
          </q-card-actions>
        </q-card>
      </q-dialog>

      <!-- 状态切换确认对话框 -->
      <q-dialog v-model="statusDialog" persistent>
        <q-card>
          <q-card-section class="row items-center">
            <q-avatar :icon="userToToggle?.is_active ? 'block' : 'check_circle'"
              :color="userToToggle?.is_active ? 'negative' : 'positive'" text-color="white" />
            <span class="q-ml-md labelColor">确定要{{ userToToggle?.is_active ? '禁用' : '启用' }}该用户吗？</span>
          </q-card-section>

          <q-card-actions align="right">
            <q-btn flat label="取消" color="primary" v-close-popup />
            <q-btn flat :label="userToToggle?.is_active ? '禁用' : '启用'"
              :color="userToToggle?.is_active ? 'negative' : 'positive'" @click="confirmToggleStatus" />
          </q-card-actions>
        </q-card>
      </q-dialog>

      <!-- 新增用户对话框 -->
      <q-dialog v-model="addUserDialog" persistent>
        <q-card class="blackCard">
          <q-card-section class="row items-center pad12">
            <div class="labelColor">{{ isEditMode ? '编辑用户' : '新增用户' }}</div>
            <q-space />
            <q-btn class="closeBtn" icon="close" flat round dense v-close-popup />
          </q-card-section>

          <q-card-section>
            <q-form @submit="submitNewUser" class="q-gutter-md">

              <div class="flexItem">
                <div class="labelColor labelWidth">名称</div>
                <q-input class="flex1" v-model="newUser.real_name" :label="newUser.real_name ? '' : '请输入'"
                  :rules="[val => !!val || '请输入姓名']" outlined dense />
              </div>

              <div class="flexItemEnd">
                <div class="labelColor" style="width: 1.25rem;font-size: .175rem;">用户头像</div>
                <div class="avatar-container">
                  <el-upload 
                    class="avatar-uploader" 
                    action=""
                    :auto-upload="false"
                    :show-file-list="false" 
                    :on-change="handleAvatarChange"
                    accept="image/jpeg,image/png,image/gif">
                    <img v-if="imageUrl" :src="imageUrl" class="avatar" />
                    <el-icon v-else class="avatar-uploader-icon">
                      <Plus />
                    </el-icon>
                  </el-upload>
                  
                  <!-- 预览弹窗 -->
                  <div v-if="imageUrl" class="preview-actions">
                    <q-btn style="width: 50%;" flat  dense color="primary" icon="search" @click="openPreview" />
                    <q-btn style="width: 50%;" flat  dense color="negative" icon="delete" @click="removeAvatar" />
                  </div>
                </div>
                
                <!-- 图片预览对话框 -->
                <q-dialog v-model="previewVisible">
                  <q-card class="preview-dialog">
                    <q-card-section class="row items-center">
                      <div class="">头像预览</div>
                      <q-space />
                      <q-btn icon="close" flat round dense v-close-popup />
                    </q-card-section>
                    <q-card-section class="preview-image-container">
                      <img :src="imageUrl" class="preview-image" />
                    </q-card-section>
                  </q-card>
                </q-dialog>
              </div>

              <div class="flexItem">
                <div class="labelColor labelWidth">所属部门</div>
                <q-select ref="deptSelectRef" class="flex1" v-model="newUser.company" :options="deptOptions"
                  :label="newUser.company ? '' : '请选择所属部门'" :rules="[val => !!val || '请选择所属部门']" outlined dense
                  emit-value map-options option-value="value" option-label="label">
                  <!-- 一级 -->
                  <template v-slot:option="scope">
                    <q-item :clickable="!!(scope.opt.children && scope.opt.children.length > 0)"
                      @click="toggleExpandDept(scope.opt.value)" :active="expandedDept === scope.opt.value"
                      style="color: #000;">
                      <q-item-section>
                        <div class="dept-option-item">
                          <q-icon v-if="scope.opt.children && scope.opt.children.length > 0" name="expand_more"
                            class="dept-expand-icon" :class="{ 'rotated': expandedDept === scope.opt.value }" />
                          <span class="dept-label">{{ scope.opt.label }}</span>
                        </div>
                      </q-item-section>
                    </q-item>
                    <!-- 子部门选项 -->
                    <template
                      v-if="scope.opt.children && scope.opt.children.length > 0 && expandedDept === scope.opt.value">
                      <q-item v-for="child in scope.opt.children" :key="child.value" clickable
                        :class="{ 'dept-child-item': true, 'dept-child-selected': newUser.company === child.value }"
                        @click.stop="selectDept(child.value)" style="color: #000;">
                        <q-item-section>
                          <div class="dept-child-option">
                            <span class="dept-child-label">{{ child.label }}</span>
                          </div>
                        </q-item-section>
                      </q-item>
                    </template>
                  </template>
                </q-select>
              </div>

              <div class="flexItem">
                <div class="labelColor labelWidth">工作邮箱</div>
                <q-input class="flex1" v-model="newUser.email" :label="newUser.email ? '' : '请输入'"
                  :rules="[val => !!val || '请输入工作邮箱', val => /^\S+@\S+\.\S+$/.test(val) || '请输入有效的邮箱地址']" outlined
                  dense />
              </div>

              <div class="flexItem">
                <div class="labelColor labelWidth">手机号码</div>
                <q-input class="flex1" v-model="newUser.phone" :label="newUser.phone ? '' : '手机号码'"
                  :rules="[
                    val => !!val || '请输入手机号',
                    val => /^1[3-9]\d{9}$/.test(val) || '请输入正确的11位手机号',
                    val => val.length === 11 || '手机号必须是11位'
                  ]" 
                  outlined dense maxlength="11" />
              </div>

              <div class="flexItem">
                <div class="labelColor labelWidth">用户昵称</div>
                <q-input class="flex1" v-model="newUser.username" :label="newUser.username ? '' : '账号'"
                  :rules="[val => !!val || '请输入账号']" outlined dense clearable autocomplete="off" />
              </div>

              <div class="flexItem" v-if="!isEditMode">
                <div class="labelColor labelWidth">用户密码</div>
                <q-input class="flex1" v-model="newUser.password"
                  :label="newUser.password ? '' : (isEditMode ? '留空则不修改密码' : '密码')" type="password"
                  :rules="[val => !!val || '请输入密码', val => val.length >= 8 || '密码长度至少8位']" clearable outlined dense />
              </div>

              <div class="flexItem">
                <div class="labelColor labelNormal" style="width: 1.25rem;font-size: .175rem;">用户性别</div>
                <q-radio v-model="newUser.gender" val="male" label="男" />
                <q-radio v-model="newUser.gender" val="female" label="女" />
                <q-radio v-model="newUser.gender" val="secret" label="保密" />
              </div>

              <div class="flexItem">
                <div class="labelColor labelWidth">用户岗位</div>
                <q-select class="flex1" v-model="newUser.position" :options="positionOptions"
                  :label="newUser.position ? '' : '用户岗位'" :rules="[val => !!val || '请选择用户岗位']" outlined dense emit-value
                  map-options />
              </div>

              <div class="flexItem">
                <div class="labelColor labelWidth">用户状态</div>
                <q-btn-toggle v-model="newUser.is_active" push glossy toggle-color="primary" :options="[
                  { label: '启用', value: true },
                  { label: '禁用', value: false },
                ]" />
              </div>

              <div class="flexItemEnd">
                <div class="labelColor labelNormal " style="width: 1.25rem;font-size: .175rem;">用户备注</div>
                <q-input class="flex1 userNote" v-model="newUser.Notes" filled autogrow type="textarea"
                  style="overflow: hidden;height: auto;" :label="newUser.Notes ? '' : '请输入'" />
              </div>

              <div class="row justify-end q-mt-md">
                <q-btn class="roundBox minW88" :label="isEditMode ? '更新' : '确定'" type="submit" color="primary"
                  :loading="submitting" />
                <q-btn style="margin-left: 8px;" class="grayBox roundBox minW88" label="取消" color="primary" flat
                  v-close-popup />
              </div>
            </q-form>
          </q-card-section>
        </q-card>
      </q-dialog>

      <!-- 重置密码 -->
      <q-dialog v-model="resetDialog" persistent>
        <q-card class="blackCard">
          <q-card-section class="row items-center pad12">
            <div class="labelColor">重置密码</div>
            <q-space />
            <q-btn class="closeBtn" icon="close" flat round dense v-close-popup />
          </q-card-section>

          <q-form @submit="resetSubMit">
            <q-card-section>
              <div class="flexItem mb12">
                <div class="labelColor labelWidth">新密码</div>
                <q-input class="flex1 " v-model="resetPwdForm.newPassword" type="password"
                  :label="resetPwdForm.newPassword ? '' : '请输入'" :rules="[val => !!val || '请输入新密码']" outlined dense />
              </div>

              <div class="flexItem">
                <div class="labelColor labelWidth">确认密码</div>
                <q-input class="flex1 " v-model="resetPwdForm.confirmPassword" type="password"
                  :label="resetPwdForm.confirmPassword ? '' : '请输入'" :rules="[val => !!val || '请再次输入新密码']" outlined
                  dense />
              </div>
            </q-card-section>

            <q-card-section align="right" style="padding-top: 0;">
              <q-btn flat label="确定" class="blueBtn roundBox" type="submit" :loading="resetLoading" />
              <q-btn flat label="取消" color="primary" class="grayBox roundBox" style="margin-left: 8px;" v-close-popup />
            </q-card-section>
          </q-form>
        </q-card>
      </q-dialog>

      <!-- 分配角色 -->
      <q-dialog v-model="roleDialog" persistent>
        <q-card class="blackCard">
          <q-card-section class="row items-center pad12">
            <div class="labelColor">分配角色</div>
            <q-space />
            <q-btn class="closeBtn" icon="close" flat round dense v-close-popup />
          </q-card-section>
          <q-form @submit="changeRole">
            <q-card-section style="padding-bottom: 0;">
              <div class="flexItem mb12">
                <div class="labelColor labelWidth">用户名称</div>
                <q-input class="flex1 " v-model="updateRole.Name" :label="updateRole.Name ? '' : '请输入'"
                  :rules="[val => !!val || '请输入用户名称']" outlined dense />
              </div>

              <div class="flexItem mb12">
                <div class="labelColor labelWidth">用户昵称</div>
                <q-input class="flex1 " v-model="updateRole.nickName" :label="updateRole.nickName ? '' : '请输入'"
                  :rules="[val => !!val || '请输入用户昵称']" outlined dense />
              </div>

              <div class="flexItem mb12">
                <div class="labelColor labelWidth">用户角色</div>
                <q-input class="flex1 " v-model="updateRole.role" :label="updateRole.role ? '' : '请输入'"
                  :rules="[val => !!val || '请输入用户角色']" outlined dense />
              </div>
            </q-card-section>
            <q-card-section align="right" style="padding-top: 0;">
              <q-btn flat label="确定" class="blueBtn roundBox minW88" type="submit" :loading="roleLoading" />
              <q-btn flat label="取消" color="primary" class="grayBox roundBox minW88" style="margin-left: 8px;"
                v-close-popup />
            </q-card-section>
          </q-form>
        </q-card>
      </q-dialog>

    </q-card>
  </div>
</template>
<script setup>
import WorkFlow from '../workFlow/WorkFlow.vue'
import { ref, reactive, onMounted, watch, computed } from 'vue'
import { api } from 'boot/axios'
import { usePlugin } from 'composables/plugin.js'
import { date, useQuasar, Dialog } from 'quasar'



const { notify } = usePlugin()
const $q = useQuasar()
const loading = ref(false)
const selected = ref([])  // 选中的行
const selectedDept = ref('') //单位
const searchName = ref('') //用户名
const searchPhone = ref('')//手机号
const userStatus = ref('')//用户状态
const pagination = ref({
  rowsPerPage: 5,//每页几条数据
  page: 1,
  total: 0,
})


const roleDialog = ref(false)//分配角色

const jumpText = ref('')//跳转

let imageUrl = ref('')

// 删除对话框相关
const deleteDialog = ref(false)
const userToDelete = ref(null)
const deptSelectRef = ref(null)
const expandedDept = ref(null) // 当前展开的一级部门 value


const currentUserForReset = ref(null); // 存储当前点击的用户

// 重置密码 弹出框
let initResetData = { newPassword: '', confirmPassword: '' }
let resetPwdForm = ref({
  newPassword: '', //新密码
  confirmPassword: '' //确认密码
})
const resetDialog = ref(false)//重置密码对话框
let resetLoading = ref(false)

const resetPassword = (row) => {
  console.log('重置密码', row)
  currentUserForReset.value = row; // 存储用户数据
  resetPwdForm.value = { ...initResetData };
  resetDialog.value = true
}
// 重置密码
const resetSubMit = () => {
  console.log('重置密码,确定~')
  if (!currentUserForReset.value) return
  if (resetPwdForm.value.newPassword !== resetPwdForm.value.confirmPassword) {
    notify('两次输入的密码不一致', 'negative');
    return;
  }
  let id = currentUserForReset.value.id;
  resetLoading.value = true;
  api.post(`/user/users/${id}/change_password/`, {
    new_password: resetPwdForm.value.newPassword,
    confirm_password: resetPwdForm.value.confirmPassword
  }).then(res => {
    notify('修改成功', 'positive')
    resetDialog.value = false
  }).catch(error => {
    console.log(2)
    notify('重置失败', 'negative')
    resetDialog.value = false
  }).finally(()=>{
    console.log('end')
    resetLoading.value = false
  })
}

// 分配角色
let updateRole = ref({
  Name: '',
  nickName: '',
  role: '',
})
let roleLoading = ref(false)
const changeRole = () => {
  console.log('分配角色,确定~')
}

// 状态切换相关
const statusDialog = ref(false)
const userToToggle = ref(null)

// 使用动态生成的单位选项
const statusOptions = [
  { label: '启用', value: 'true' },
  { label: '禁用', value: 'false' }
]

// 新增用户相关
const addUserDialog = ref(false)
const submitting = ref(false)
const isEditMode = ref(false) // 添加编辑模式标识
const currentEditUser = ref(null) // 当前编辑的用户数据
const newUser = reactive({
  real_name: '',
  avatar: '',//头像
  avatarFile: null, // 保存上传的文件对象
  company: '',//单位
  email: '',//邮箱
  phone: '',//手机号
  username: '',
  password: '',
  gender: '1',//用户性别
  position: '', //用户岗位
  is_active: true,//用户状态
  Notes: '',//用户备注
})

const positionOptions = [
  { label: '前端开发岗位', value: 'front' },
  { label: '后端开发岗位', value: 'back' },
  { label: 'UI设计岗位', value: 'ui' },
  { label: '产品岗位', value: 'product' },
]

const userList = ref([])

const columns = [
  { name: 'id', label: '用户编号', field: 'id', align: 'center', sortable: false },
  { name: 'real_name', label: '用户名称', field: 'real_name', align: 'center', sortable: false },
  { name: 'username', label: '用户昵称', field: 'username', align: 'center', sortable: false },
  { name: 'company', label: '归属单位', field: 'company', align: 'center', sortable: false },
  { name: 'phone', label: '手机号码', field: 'phone', align: 'center', sortable: false },
  // { name: 'mobile', label: '电话', field: 'mobile', align: 'center', sortable: false },
  { name: 'role', label: '用户权限', field: 'role', align: 'center', sortable: false },
  // { name: 'status', label: '状态', field: 'is_active', align: 'center', sortable: false },
  { name: 'date_joined', label: '创建时间', field: 'date_joined', align: 'center', sortable: false, format: val => date.formatDate(val, 'YYYY-MM-DD HH:mm:ss') },
  { name: 'actions', label: '操作', field: 'actions', align: 'center', }
]


// 预览对话框控制
const previewVisible = ref(false)

// 处理头像选择
const handleAvatarChange = (uploadFile) => {
  // 如果有选择文件
  if (uploadFile && uploadFile.raw) {
    // 验证文件类型
    if (!/image\/(jpeg|png|gif)/.test(uploadFile.raw.type)) {
      notify('头像图片只能是 JPG/PNG/GIF 格式!', 'warning')
      return
    }
    
    // 验证文件大小
    if (uploadFile.raw.size / 1024 / 1024 > 2) {
      notify('头像图片大小不能超过 2MB!', 'warning')
      return
    }
    
    // 显示选择的图片
    imageUrl.value = URL.createObjectURL(uploadFile.raw);
    console.log('图片URL:', imageUrl.value);
    
    // 直接存储File对象用于表单提交
    newUser.avatarFile = uploadFile.raw;
    console.log('File对象:', uploadFile.raw);
  }
}

// 打开预览
const openPreview = () => {
  previewVisible.value = true
}

// 移除头像
const removeAvatar = () => {
  imageUrl.value = ''
  newUser.avatarFile = null
  newUser.avatar = ''
  console.log('头像已移除')
}

// 获取用户列表数据
async function fetchUsers() {
  loading.value = true

  try {
    const params = {
      page: pagination.value.page,
      page_size: pagination.value.rowsPerPage
    }

    // 只添加有值的过滤条件
    if (selectedDept.value) {
      params.company = selectedDept.value
    }

    if (searchName.value) {
      params.real_name = searchName.value
    }

    if (searchPhone.value) {
      params.phone = searchPhone.value
    }

    if (userStatus.value) {
      params.is_active = userStatus.value
    }

    const response = await api.get('/user/users/', { params })
    console.log('API响应:', response) // 调试输出

    // 安全地访问响应数据
    if (response && response.results) {
      userList.value = response.results || []
      // userList.value.sort((a,b)=>a.id-b.id)
      pagination.value.total = response.count || 0
    } else {
      console.error('API响应格式不符合预期:', response)
      notify('获取用户数据格式错误')
      userList.value = []
      pagination.value.total = 0
    }
  } catch (error) {
    console.error('获取用户列表失败', error)
    notify('获取用户列表失败：' + (error.response?.data?.message || error.message))
    userList.value = []
    pagination.value.total = 0
  } finally {
    loading.value = false
  }
}

const onRowsPerPageChange = (e) => {
  console.log('每页条数变化:', e)
  pagination.value.rowsPerPage = e
  pagination.value.page = 1 // 重置到第一页
  fetchUsers()
}

// 跳转（支持回车键）
const goJump = () => {
  console.log("跳转到页面:", jumpText.value)
  const targetPage = Number(jumpText.value)
  if (targetPage > 0 && targetPage <= Math.ceil(pagination.value.total / pagination.value.rowsPerPage)) {
    pagination.value.page = targetPage
    fetchUsers()
  } else {
    notify('页码超出范围', 'warning')
  }
}

function addUser(type, data) {
  console.log('类型', type, '传入数据>>>', data)
  if (type == 'add') {
    // 重置图片URL，确保不回显
    imageUrl.value = ''
    
    // Reset form data for new user
    Object.assign(newUser, {
      real_name: '',
      avatar: '',//头像
      avatarFile: null, // 清空头像文件
      company: '',//单位
      email: '',//邮箱
      phone: '',//手机号
      username: '',
      password: '',
      gender: '',//用户性别
      position: '', //用户岗位
      is_active: true,//用户状态
      Notes: '',//用户备注
    })
    isEditMode.value = false
    currentEditUser.value = null
  } else {
    // 编辑模式
    if (data) {
      // 先重置图片URL，然后根据需要设置
      imageUrl.value = ''
      
      // 将用户数据填充到表单中
      Object.assign(newUser, {
        real_name: data.real_name || '',
        avatar: data.avatar || '',
        avatarFile: null, // 清空文件对象
        company: data.company || '',
        email: data.email || '',
        phone: data.phone || '',
        username: data.username || '',
        password: '', // 编辑时不显示密码
        gender: data.gender || '',
        position: data.position || '',
        is_active: data.is_active !== undefined ? data.is_active : true,
        Notes: data.Notes || ''
      })
      
      // 如果有头像URL，可以在这里设置，但现在不设置以避免回显
      // if (data.avatar) {
      //   imageUrl.value = data.avatar
      // }
      
      isEditMode.value = true
      currentEditUser.value = data
    }
  }

  addUserDialog.value = true
}

function exportUsers() {
  if (selected.value.length === 0) {
    notify('请选择要导出的用户', 'warning')
    return
  }

  // Get all users data
  api.get('/user/users/', {
    params: {
      page_size: 1000,
      id: selected.value.map(user => user.id).join(',')
    }
  })
    .then(response => {
      if (!response || !response.results) {
        notify('导出失败', 'negative')
        return
      }

      // Convert users data to CSV format
      const headers = ['用户编号', '账号', '姓名', '单位', '手机号', '电话', '用户权限', '状态', '创建时间']
      const rows = response.results.map(user => [
        user.id,
        user.username,
        user.real_name || '',
        user.company || '',
        user.phone || '',
        user.mobile || '',
        user.is_superuser ? '超级管理员' : (user.is_staff ? '管理员' : '普通用户'),
        user.is_active ? '启用' : '禁用',
        user.date_joined
      ])

      // Create CSV content
      const csvContent = [
        headers.join(','),
        ...rows.map(row => row.join(','))
      ].join('\n')

      // Create and download the file
      const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `用户列表_${date.formatDate(new Date(), 'YYYY-MM-DD_HH-mm')}.csv`)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      notify('导出成功', 'positive')
    })
    .catch(error => {
      notify('导出失败', 'negative')
    })
}

function queryUsers() {
  // 重置到第一页
  pagination.value.page = 1
  fetchUsers()
}

function resetFilters() {
  selectedDept.value = ''
  searchName.value = ''
  searchPhone.value = ''
  userStatus.value = ''

  // 重置后自动查询
  queryUsers()
}



// 分配角色
const assignRole = (row) => {
  console.log('分配角色', row)
  roleDialog.value = true
}




function toggleUserStatus(user) {
  userToToggle.value = user
  statusDialog.value = true
}

async function confirmToggleStatus() {
  try {
    await api.patch(`/user/users/${userToToggle.value.id}/`, {
      is_active: !userToToggle.value.is_active
    })

    $q.notify({
      message: `用户${userToToggle.value.is_active ? '禁用' : '启用'}成功`,
      color: 'green',
      icon: 'check_circle',
      position: 'top',
      timeout: 2000
    })

    userToToggle.value.is_active = !userToToggle.value.is_active
    // 刷新用户列表以确保数据同步
    fetchUsers()
  } catch (error) {
    $q.notify({
      message: `操作失败：${error.response?.data?.message || error.message}`,
      color: 'red',
      icon: 'warning',
      position: 'top',
      timeout: 2000
    })
  } finally {
    statusDialog.value = false
    userToToggle.value = null
  }
}

// 批量修改用户状态
async function batchToggleStatus(status) {
  try {
    // 显示确认对话框
    Dialog.create({
      title: '确认',
      message: `确定要${status ? '启用' : '禁用'}选中的 ${selected.value.length} 个用户吗？`,
      cancel: true,
      persistent: true
    }).onOk(async () => {
      loading.value = true
      try {
        // 并行处理所有请求
        await Promise.all(
          selected.value.map(user =>
            api.patch(`/user/users/${user.id}/`, { is_active: status })
          )
        )

        $q.notify({
          message: `批量${status ? '启用' : '禁用'}成功`,
          color: 'green',
          icon: 'check_circle',
          position: 'top',
          timeout: 2000
        })

        // 清空选择并刷新列表
        selected.value = []
        fetchUsers()
      } catch (error) {
        $q.notify({
          message: `操作失败：${error.response?.data?.message || error.message}`,
          color: 'red',
          icon: 'warning',
          position: 'top',
          timeout: 2000
        })
      } finally {
        loading.value = false
      }
    })
  } catch (error) {
    console.error('批量操作失败:', error)
  }
}

function confirmDeleteUser(user) {
  userToDelete.value = user
  deleteDialog.value = true
}

function deleteUser() {
  if (!userToDelete.value) return

  api.delete(`/user/users/${userToDelete.value.id}/`)
    .then(() => {
      notify('用户删除成功','positive')
      // 重新加载数据
      fetchUsers()
    })
    .catch(error => {
      notify(`删除失败：${error.response?.data?.message || error.message}`)
    })
    .finally(() => {
      deleteDialog.value = false
      userToDelete.value = null
    })
}

async function submitNewUser() {
  console.log(isEditMode.value ? '编辑用户,确定~' : '新增用户,确定~')
  submitting.value = true
  try {
    // 使用FormData处理文件上传
    const formData = new FormData()
    
    // 添加基本信息
    formData.append('username', newUser.username)
    formData.append('real_name', newUser.real_name)
    formData.append('company', newUser.company)
    formData.append('phone', newUser.phone)
    formData.append('email', newUser.email)
    formData.append('gender', newUser.gender)
    formData.append('position', newUser.position)
    formData.append('is_active', newUser.is_active)
    formData.append('Notes', newUser.Notes)
    
    // 只有在新增模式下才添加密码，或者在编辑模式下用户输入了密码
    if (!isEditMode.value || (isEditMode.value && newUser.password)) {
      formData.append('password', newUser.password)
    }
    
    // 如果有上传新头像，添加到表单
    if (newUser.avatarFile) {
      // 直接使用File对象
      formData.append('avatar', newUser.avatarFile);
      console.log('添加到表单的File对象:', newUser.avatarFile);
    }

    // 设置请求头，支持文件上传
    const config = {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }
    
    if (isEditMode.value) {
      await api.put(`/user/users/${currentEditUser.value.id}/`, formData, config)
      addUserDialog.value = false
      $q.notify({
        message: '用户更新成功！',
        color: 'positive',
        icon: 'check_circle',
        position: 'top',
        timeout: 2000
      })
    } else {
      await api.post('/user/users/', formData, config)
      addUserDialog.value = false
      $q.notify({
        message: '用户创建成功！',
        color: 'positive',
        icon: 'check_circle',
        position: 'top',
        timeout: 2000
      })
    }

    // 刷新用户列表
    fetchUsers()
  } catch (error) {
    Dialog.create({
      title: '错误',
      message: (isEditMode.value ? '更新失败：' : '创建失败：') + (error.response?.data?.message || error.message),
      ok: {
        label: '确定',
        color: 'negative'
      },
      persistent: false,
      color: 'negative'
    })
  } finally {
    submitting.value = false
  }
}

// 监听分页变化，自动查询
watch(() => pagination.value.page, () => {
  fetchUsers()
})

// 监听每页条数变化，自动查询
watch(() => pagination.value.rowsPerPage, () => {
  fetchUsers()
})

// 页面初始化时加载数据
onMounted(() => {
  fetchUsers()
})

// 单位树搜索
const deptSearch = ref('')

// 添加过滤后的部门树计算属性
const filteredDeptTree = computed(() => {
  const keyword = deptSearch.value.trim().toLowerCase()
  
  // 如果没有搜索关键词，返回原始部门树
  if (!keyword) {
    return deptTree
  }
  
  // 过滤部门树
  return deptTree.map(dept => {
    // 检查部门名称是否匹配关键词
    const isDeptMatch = dept.label.toLowerCase().includes(keyword)
    
    // 过滤子部门
    const filteredChildren = dept.children.filter(child => 
      child.label.toLowerCase().includes(keyword)
    )
    
    // 如果部门名称匹配或者有匹配的子部门，则保留该部门
    if (isDeptMatch || filteredChildren.length > 0) {
      return {
        ...dept,
        // 如果父部门匹配，则显示所有子部门，否则只显示匹配的子部门
        children: isDeptMatch ? dept.children : filteredChildren
      }
    }
    
    // 否则返回null，后续会过滤掉
    return null
  }).filter(Boolean) // 过滤掉null项
})

// 搜索部门
const onDeptSearch = () => {
  // 搜索功能现在由计算属性自动处理
  console.log('搜索部门:', deptSearch.value)
}

// 键盘回车触发搜索
const onDeptSearchKeyup = (e) => {
  if (e.key === 'Enter') {
    onDeptSearch()
  }
}

// 添加处理清空搜索的函数
const handleClearSearch = () => {
  deptSearch.value = '' // 确保搜索值被清空
  console.log('搜索已清空')
  // 手动触发一次onDeptSearch确保UI更新
  onDeptSearch()
}

// 单位树结构（expansion-item风格）
const deptTree = [
  {
    id: 'A',
    label: '207所',
    children: [
      { id: 'A1', label: '七部' },
      { id: 'A2', label: '五部' },
      { id: 'A3', label: '二部' },
      { id: 'A4', label: '六部' },
    ]
  },
  {
    id: 'B',
    label: '528厂',
    children: [
      { id: 'B1', label: '一部' },
    ]
  }
]

const selectedDeptId = ref(null)

// 部门选择器相关变量
const deptOptions = ref([
  {
    label: '207所',
    value: '207所',
    children: [
      {
        label: '七部',
        value: '207所-七部'
      },
      {
        label: '五部',
        value: '207所-五部'
      },
      {
        label: '二部',
        value: '207所-二部'
      },
      {
        label: '六部',
        value: '207所-六部'
      }
    ]
  },
  {
    label: '528厂',
    value: '528厂',
    children: [
      {
        label: '一部',
        value: '528厂-一部'
      }
    ]
  }
])

// 选择部门
const selectDept = (value) => {
  newUser.company = value
  deptSelectRef.value.hidePopup && deptSelectRef.value.hidePopup()
}
const toggleExpandDept = (deptValue) => {
  expandedDept.value = expandedDept.value === deptValue ? null : deptValue
}


</script>

<style lang="scss" scoped>
.q-page {
  background: #f5f7fa;
}

::v-deep.q-field--auto-height.q-field--labeled .q-field__control-container {
  padding-top: 0;
}

::v-deep.q-field--labeled .q-field__native {
  padding-top: 0;
  padding-bottom: 0;
}

::v-deep.q-placeholder {
  background: pink !important;
}

.flexbox {
  display: flex;
  align-items: center;
}

.labelT {
  font-size: .175rem;
  font-weight: 500;
  color: var(--q-slideText);
  line-height: .275rem;
  margin-right: .125rem;
}

.flexCenter {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}

.paginationEl {
  display: flex;
  align-items: center;
  justify-content: center;
}


.custom-btn {
  width: fit-content;
  background-color: #396ea4;
  margin-left: .875rem;
  border-radius: .0625rem;
}

/* 有值或聚焦时隐藏 label */
.dynamic-label-input.q-field--with-content :deep(.q-field__label),
.dynamic-label-input.q-field--focused :deep(.q-field__label) {
  display: none;
}

.dynamic-label-input :deep(.q-field__inner) {
  border: none;
  /* 设置圆角的 必须单独取消 */
}

/* 单独设置圆角和边框色 */
.dynamic-label-input :deep(.q-field__inner) {
  border-radius: .1rem;
}

/* 文字居中 */
.dynamic-label-input :deep(.q-field__native) {
  text-align: center;
}

/* 分页器 */
:deep(.q-pagination .q-btn) {
  color: white !important;
}

:deep(.q-pagination .q-btn--standard) {
  background: #396ea4 !important;
}

:deep(.q-pagination .q-btn:hover) {
  background: #396ea4 !important;
}

:deep(.q-table__container) {
  border: none !important;
  min-height: 7.5rem !important; //最小高度600px
}

.borderBot {
  border-bottom: 1px solid #2a3f63 !important;
}

.noBorder {
  border: none !important;
}

.noPading {
  padding: 0px !important;
}

.pading16 {
  padding: 16px !important;
}

.leftMenu {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 5%;
    right: 0;
    width: 1px;
    height: 90%;
    background: linear-gradient(180deg, #87a6be, #3e68a4)
  }
}

// 菜单展开箭头为纯白色
:deep(.q-expansion-item__toggle-icon) {
  color: #fff !important;
}

// 二级菜单左侧缩进28px
:deep(.q-expansion-item .q-item) {
  // padding-left: 28px !important;
  // background: rgba(0,89,130,.15) !important;
  color: #fff !important;
}

// 选中状态背景色为#ffa363，文字纯白
:deep(.q-expansion-item__container .q-item.active),
:deep(.q-expansion-item__container .q-item.active) {
  background: rgba(255, 163, 99, .8) !important;
  color: #fff !important;
}

// hover时背景色可与选中一致或略浅
:deep(.q-expansion-item__container .q-item:hover) {
  background: rgba(255, 163, 99, .6) !important;
  color: #fff !important;
}

.sub-nav-link {
  padding-left: 28px !important;
  background: rgba(0, 89, 130, .15) !important;
  color: #fff !important;
}

.sub-nav-link.active {
  background: rgba(255, 163, 99, .8) !important;
  color: #fff !important;
}

.sub-nav-link:hover {
  background: rgba(255, 163, 99, .6) !important;
  color: #fff !important;
}

// 一级菜单（部门/单位）无论收起还是展开，背景色始终为透明
:deep(.q-expansion-item),
:deep(.q-expansion-item--expanded),
:deep(.q-expansion-item.nav-item),
:deep(.nav-header) {
  background: transparent !important; // 一级菜单背景透明
}

.customFlexBox {
  // width: 28%;
  flex: 1;
}

.blackCard {
  min-width: 5.375rem;
  background: rgba(0, 0, 0, .9) !important;
  border: none !important;
  border-radius: .0625rem !important;
  // overflow: hidden !important;
}

.q-field--with-bottom {
  padding-bottom: 0px !important;
}

.flex.inline {
  display: block;
  text-align: center;
}

// 用户
.greyBox {
  width: 1.1rem;
  line-height: .4rem;
  border-radius: .0625rem !important;
  background: #bdbdbd !important;
  color: white;
  font-size: 1em;
}

// 普管
.organgeBox {
  width: 1.1rem;
  line-height: .4rem;
  border-radius: .0625rem !important;
  background: #ffa363 !important;
  color: white;
  font-size: 1em;
}

// 超管
.purpleBox {
  width: 1.1rem;
  line-height: .4rem;
  border-radius: .0625rem !important;
  color: white;
  font-size: 1em;
  background: #ffb640 !important;
}

.chip {
  width: 50px;
  display: flex;
  justify-content: center;
}

:deep(.chip .q-chip__content) {
  flex: 0 0 auto !important;
}

.flexItem {
  display: flex;
  align-items: center;

}

.flexItemEnd {
  display: flex;
}

.labelWidth {
  width: 1.25rem;
  position: relative;
  font-size: .175rem;

  &::before {
    position: absolute;
    content: '*';
    left: -0.1rem;
    top: 0;
    color: red;
  }
}

.labelNormal {
  width: 1.25rem;
}

.flex1 {
  flex: 1;
}

// 默认状态radio
:deep(.q-radio__inner) {
  color: white !important;
}

// 选中状态radio
:deep(.q-radio__inner--truthy) {
  color: orange !important;
}

// 按钮组
:deep(.q-btn-group) {
  border: 1px solid rgba(255, 255, 255, .5);
  border-radius: 3px;
}

:deep(.q-btn-group .q-btn) {
  background: $inputBG;
  background-image: none !important;
}

:deep(.q-btn-group .bg-primary) {
  background: #ffa363 !important;
}

.avatar-container {
  position: relative;
  display: inline-block;
}

.preview-actions {
  position: absolute;
  left: 0;
  bottom: 0;
  display: flex;
  width: 1.1rem;
  background: rgba(0,0,0,0.5);
  overflow: hidden;
}
.q-focus-helper{
  border-radius: none !important;
}

.avatar-uploader .avatar {
  width: 1.1rem;
  height: 1.1rem;
  display: block;
  object-fit: cover;
  border-radius: .0625rem;
}

.avatar-uploader .el-upload {
  border-radius: .0625rem;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  background: $inputBG;
  font-size: .35rem;
  width: 1.1rem;
  height:  1.1rem;
  text-align: center;
  border:.0125rem solid #8c939d;
  border-radius: .0625rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-dialog {
  min-width: 4rem;
  background: #1e1e1e;
}

.preview-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: .2rem;
}

.preview-image {
  max-width: 100%;
  max-height: 5rem;
  object-fit: contain;
}

.imgEl {
  width: .3rem;
  height:.3rem;
  margin-right: .0625rem;
}

.custom-tooltip {
  background: rgba(0, 0, 0, 1) !important;
  color: rgba(255, 255, 255, 0.9);
  border-radius: .0625rem !important;
  font-size: .25rem !important;
}

.edit {
  background: url("../../assets/images/edit.png")no-repeat;
}

.edit:hover {
  background: url("../../assets/images/edit-active.png")no-repeat !important;
}

.user {
  background: url("../../assets/images/user.png")no-repeat;

  &:hover {
    background: url("../../assets/images/user-active.png")no-repeat !important;
  }
}

.refresh {
  background: url("../../assets/images/refresh.png")no-repeat;

  &:hover {
    background: url("../../assets/images/refresh-active.png") no-repeat !important;
  }
}

.delete {
  background: url("../../assets/images/delete.png")no-repeat;
}

.pad12 {
  padding:.15rem;
  border-bottom: .0125rem solid $slideText !important;
}

// 弹窗 确定按钮
.blueBtn {
  background-color: #396ea4;
}

.mb12 {
  margin-bottom: 20px;
}



// 部门选择器样式
.dept-option-item {
  display: flex;
  align-items: center;
  padding: 8px 0;

  .dept-expand-icon {
    margin-right: 8px;
    font-size: 16px;
    color: #000 !important;
  }

  .dept-label {
    flex: 1;
    color: #000 !important;
    font-size: 14px;
    font-weight: 500;
  }

  .dept-check-icon {
    margin-left: 8px;
    font-size: 16px;
  }
}

.dept-child-item {
  padding-left: 24px !important;
  background: #fff !important; //default

  &:hover {
    background: #ddd !important;
  }

  // 子菜单选中
  &.dept-child-selected {
    background: #e4eaf1 !important;
  }
}

.dept-child-option {
  display: flex;
  align-items: center;
  padding: 6px 0;

  .dept-child-label {
    flex: 1;
    color: #000 !important;
    font-size: 13px;
  }

  .dept-check-icon {
    margin-left: 8px;
    font-size: 14px;
  }
}

// 下拉菜单样式
:deep(.q-select__dropdown) {
  background: rgba(0, 0, 0, 0.9) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 8px !important;

  .q-item {
    color: rgba(255, 255, 255, 0.9) !important;

    &:hover {
      background-color: rgba(255, 163, 99, 0.1) !important;
    }

    &.q-item--active {
      background-color: rgba(255, 163, 99, 0.2) !important;
      color: #ffa363 !important;
    }
  }

  // 确保所有文字都是白色
  .q-item-section {
    color: rgba(255, 255, 255, 0.9) !important;
  }

  // 选项文字颜色
  .q-item__label {
    color: rgba(255, 255, 255, 0.9) !important;
  }
}

// 样式：展开icon旋转
.dept-option-item .dept-expand-icon.rotated {
  transform: rotate(180deg);
  transition: transform 0.2s;
}

// 输入框start
.q-field {
  font-size: .175rem;
  height: .5rem;
}

:deep(.q-field--dense .q-field__control) {
  //输入高度
  height: .5rem !important;
}

:deep(.q-field--dense .q-field__label) {
  font-size: .175rem !important;
  top: .125rem;
}

:deep(.q-field__label) {
  line-height: .25rem !important;
  font-size: .2rem;
}

.userNote {
  :deep(.q-field__label) {
    top: .1rem !important;
  }
}

// 输入框end

:deep(.q-table__bottom) {
  font-size: .15rem;
}

:deep(.q-btn__content) {
  font-size: .175rem;
}

:deep(.q-btn) {
  padding: .05rem .2rem;
  height: .45rem;
}

:deep(.q-btn .q-icon) {
  font-size: .3rem !important;
}

:deep(.q-field--dense .q-field__marginal) {
  height: .5rem !important;
}

:deep(.q-item__label) {
  font-size: .2rem !important;
}

:deep(.q-item__section--main) {
  font-size: .2rem !important;
}

:deep(.q-table__container .q-table thead th) {
  font-size: .175rem !important;
}

:deep(.q-table__container .q-table tbody td) {
  font-size: .175rem !important;
}

:deep(.q-table td) {
  padding: .0875rem .2rem !important;
}

:deep(.q-table tbody td) {
  height: .6rem !important;
}


.blackCard {

  // 表单提示项
  :deep(.q-field--dense .q-field__bottom) {
    min-height: .25rem !important;
    font-size: .1375rem !important;

  }

  :deep(.q-gutter-md > *) {
    margin-top: .25rem;
    margin-left: .25rem;
  }
}


:deep(.q-pagination__middle .q-btn) {
  width: .35rem;
  height: .35rem;
}

// 添加无搜索结果的样式
.no-results {
  padding: .2rem;
  text-align: center;
  color: #8c8c8c;
  font-size: .175rem;
}
</style>
