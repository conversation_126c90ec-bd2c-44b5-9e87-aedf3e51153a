<template>
  <div class="flex q-gutter-x-sm">
    <div v-for="(item, idx) in items" :key="idx" class="col">
      <div class="q-pa-md top">
        <div class="flex">
          <div class="bg-image" :style="{ backgroundImage: `url(${item.image})` }"></div>
          <div class="text-primary text-huangyou fs-24 q-ml-sm">{{ item.name }}</div>
        </div>

        <div class="q-mt-md q-px-md progress">
          <div class="fit inner">
            <div class="full-height bg-primary" :style="{ width: `${item.usedRatio}%` }"></div>
          </div>
        </div>
      </div>
      <div class="flex items-center justify-between q-pa-md bottom">
        <div>
          <div class="name">总量</div>
          <div>
            <span class="text-italic text-roboto text-bold fs-20">{{ item.capacity }}</span>
            <span class="q-ml-xs fs-16 unit">{{ item.unit }}</span>
          </div>
        </div>
        <div class="q-mx-sm">
          <div class="name">已用</div>
          <div>
            <span class="text-italic text-roboto text-bold fs-20">{{ item.used }}</span>
            <span class="q-ml-xs fs-16 unit">{{ item.unit }}</span>
          </div>
        </div>
        <div class="text-bold text-right text-digital percent">{{ item.usedRatio }}%</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import ImgGpu from 'assets/images/gpu.png'
import ImgCpu from 'assets/images/cpu.png'
import ImgMemory from 'assets/images/memory.png'
import ImgStorage from 'assets/images/storage.png'

const props = defineProps({
  resource: Object,
})

const items = computed(() => [
  {
    name: 'GPU',
    unit: '卡',
    image: ImgGpu,
    capacity: props.resource?.gpu?.capacity,
    used: props.resource?.gpu?.used,
    usedRatio: props.resource?.gpu?.usedRatio,
  },
  {
    name: 'CPU',
    unit: '核',
    image: ImgCpu,
    capacity: props.resource?.cpu?.capacity,
    used: props.resource?.cpu?.used,
    usedRatio: props.resource?.cpu?.usedRatio,
  },
  {
    name: '内存',
    unit: 'GB',
    image: ImgMemory,
    capacity: props.resource?.memory?.capacity,
    used: props.resource?.memory?.used,
    usedRatio: props.resource?.memory?.usedRatio,
  },
  {
    name: '存储',
    unit: 'TB',
    image: ImgStorage,
    capacity: props.resource?.disk?.capacity,
    used: props.resource?.disk?.used,
    usedRatio: props.resource?.disk?.usedRatio,
  },
])
</script>

<style lang="scss" scoped>
.top {
  background-color: #fff;

  .bg-image {
    height: 90px;
    width: 160px;
    background-size: cover;
  }

  .progress {
    background-color: #dedede;
    height: 14px;

    .inner {
      transform: skewX(-35deg);
      border-left: 3px solid #fff;
      border-right: 3px solid #fff;
    }
  }
}

.bottom {
  background-color: #dadada;

  .name {
    color: rgba(0, 0, 0, 0.5);
  }

  .unit {
    color: rgba(0, 0, 0, 0.6);
  }

  .percent {
    font-size: 50px;
  }
}

.body--dark {
  .top {
    background-color: rgba(0, 0, 0, 0.8);

    .progress {
      background-color: #2d2d2d;

      .inner {
        border-left: 3px solid #000;
        border-right: 3px solid #000;
      }
    }
  }

  .bottom {
    background-color: rgba(30, 30, 30, 0.8);

    .name {
      color: rgba(255, 255, 255, 0.5);
    }

    .unit {
      color: rgba(255, 255, 255, 0.6);
    }
  }
}
</style>
