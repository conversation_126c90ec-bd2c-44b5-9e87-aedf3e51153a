<template>
  <div class="q-pa-md">
    <!-- 面包屑导航 -->
    <!-- <div class="text-caption q-mb-md">
      <q-breadcrumbs>
        <q-breadcrumbs-el label="算法模型" />
        <q-breadcrumbs-el label="强化学习" />
      </q-breadcrumbs>
    </div> -->

    <!-- 模型与算法编辑 -->
    <q-card flat bordered class="q-mb-md">
      <q-card-section>
        <div class="form-row-aligned">
          <div class="form-title">模型与算法编辑</div>
          <div class="form-group">
            <div class="form-label">版本选择</div>
            <q-select outlined dense v-model="algorithm.version" :options="algorithmVersions" class="form-field-unified" />
          </div>
          <div class="form-group centerEl">
            <q-btn class="purpleBtn font14" outlined label="编辑神经网络" color="primary" style="flex: 0 0 auto; margin: 0 8px;" />
          </div>
          <div class="form-group">
            <div class="form-label">强化学习算法选取</div>
            <q-select outlined dense v-model="algorithm.rlType" :options="rlAlgorithmTypes" class="form-field-unified" />
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- 仿真与数据集选择 -->
    <q-card flat bordered class="q-mb-md">
      <q-card-section>
        <div class="form-row-aligned">
          <div class="form-title">仿真与数据集选择</div>
          <div class="form-group">
            <div class="form-label">数据来源</div>
            <q-select outlined dense v-model="simulation.dataSource" :options="dataSourceOptions" class="form-field-unified" />
          </div>
          <div class="form-group centerEl">
            <div class="">是否接入外部仿真环境</div>
            <el-switch v-model="simulation.useExternalEnv" inline-prompt active-text="是" inactive-text="否" style="--el-switch-on-color: #165dff; margin: 0 8px;" />
          </div>
          <div class="form-group">
            <div class="form-label">外部仿真IP及端口</div>
            <q-input outlined dense v-model="simulation.externalEnvAddress" placeholder="http://" :disable="!simulation.useExternalEnv" class="form-field-unified" />
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- 更多设置 - 智能体设置、超参数设置、集群设置 -->
    <q-card flat bordered class="q-mb-md">
      <q-card-section>
        <div class="row justify-between items-center">
          <div class="labelColor ">更多设置</div>
          <q-btn class="labelColor" flat round icon="keyboard_arrow_down" v-model="showMoreSettings"
            @click="showMoreSettings = !showMoreSettings" />
        </div>

        <div v-show="showMoreSettings">
          <!-- 智能体设置 -->
          <div class=" q-mt-md q-mb-sm labelColor font20">智能体设置</div>
          <div class="row q-col-gutter-md items-center">
            <div class="col-1">
              <div class=" labelColor font14 textRight">采样时间(s)</div>
            </div>
            <div class="col-3">
              <q-input outlined dense v-model="agent.sampleTime" placeholder="请输入" />
            </div>
            <div class="col-1">
              <div class=" labelColor font14 textRight">动作空间</div>
            </div>
            <div class="col-3">
              <q-select outlined dense v-model="agent.actionSpace" :options="actionSpaceOptions" />
            </div>
            <div class="col-1">
              <div class="labelColor font14 textRight">观测空间</div>
            </div>
            <div class="col-3">
              <q-select outlined dense v-model="agent.observationSpace" :options="observationSpaceOptions" />
            </div>
          </div>

          <div class="row q-col-gutter-md q-mt-sm items-center">
            <div class="col-1">
              <div class="labelColor font14 textRight">奖励函数</div>
            </div>
            <div class="col-3">
              <q-select outlined dense v-model="agent.rewardFunction" :options="rewardFunctionOptions" />
            </div>
          </div>

          <!-- 超参数设置 -->
          <div class=" q-mt-md q-mb-sm labelColor font20">超参数设置</div>
          <div class="row q-col-gutter-md items-center">
            <div class="col-1">
              <div class="labelColor font14 textRight">学习率</div>
            </div>
            <div class="col-3">
              <q-input outlined dense v-model="hyperParams.learningRate" placeholder="1e-5" />
            </div>
            <div class="col-1">
              <div class="labelColor font14 textRight">训练轮数</div>
            </div>
            <div class="col-3">
              <q-input outlined dense v-model="hyperParams.epochs" placeholder="50" />
            </div>
            <div class="col-1">
              <div class=" labelColor font14 textRight">批处理大小</div>
            </div>
            <div class="col-3">
              <div class="row items-center customAdd">
                <q-btn flat dense icon="remove" size="sm" @click="decreaseBatchSize" />
                <div class="q-mx-md">{{ hyperParams.batchSize }}</div>
                <q-btn flat dense icon="add" size="sm" @click="increaseBatchSize" />
              </div>
            </div>
          </div>

          <div class="row q-col-gutter-md q-mt-sm items-center">
            <div class="col-1">
              <div class=" labelColor font14 textRight">学习率策略</div>
            </div>
            <div class="col-3">
              <q-select outlined dense v-model="hyperParams.learningRateStrategy" :options="learningRateStrategies" />
            </div>
            <div class="col-1">
              <div class=" labelColor font14 textRight">计算类型</div>
            </div>
            <div class="col-3">
              <q-select outlined dense v-model="hyperParams.computeType" :options="computeTypes" />
            </div>
            <div class="col-1">
              <div class=" labelColor font14 textRight">验证集比例</div>
            </div>
            <div class="col-3">
              <div class="row items-center customAdd">
                <q-btn flat dense icon="remove" size="sm" @click="decreaseValidationRatio" />
                <div class="q-mx-md">{{ hyperParams.validationRatio }}</div>
                <q-btn flat dense icon="add" size="sm" @click="increaseValidationRatio" />
              </div>
            </div>
          </div>

          <!-- 集群设置 -->
          <div class=" q-mt-md q-mb-sm labelColor font20">集群设置</div>
          <div class="row q-col-gutter-md items-center">
            <div class="col-1">
              <div class=" labelColor font14 textRight">CPU个数</div>
            </div>
            <div class="col-3">
              <q-input outlined dense v-model="cluster.cpuCount" placeholder="4" />
            </div>
            <div class="col-1">
              <div class=" labelColor font14 textRight">GPU个数</div>
            </div>
            <div class="col-3">
              <q-input outlined dense v-model="cluster.gpuCount" placeholder="1" />
            </div>
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- 输出路径设置 -->
    <q-card flat bordered class="q-mb-md">
      <q-card-section>
        <div class=" q-mb-md labelColor font20">输出路径设置</div>
        <div class="row q-col-gutter-md items-center">
          <div class="col-1">
            <div class="labelColor font14 textRight">模型保存路径</div>
          </div>
          <div class="col-5">
            <q-input outlined dense v-model="output.savePath" placeholder="请输入" />
          </div>
          <div class="col-1">
            <div class="labelColor font14 textRight">模型保存名称</div>
          </div>
          <div class="col-5">
            <q-input outlined dense v-model="output.saveName" placeholder="请输入" />
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- 训练过程可视化 -->
    <q-card flat bordered class="q-mb-md">
      <q-card-section>
        <div class=" q-mb-md labelColor">
          训练过程可视化
          <q-chip v-if="dataUpdateInterval" color="green" text-color="white" size="sm">
            <q-avatar icon="sync" />
            实时更新中
          </q-chip>
        </div>
        <div class="row q-col-gutter-md">
          <div class="col-12 col-md-6">
            <q-card flat class="visualization-card">
              <q-card-section class="q-pa-none">
                <div ref="lossChartRef" style="height: 4.5rem; width: 100%;"></div>
                <q-inner-loading :showing="loading">
                  <q-spinner-gears size="50px" color="primary" />
                  <div class="q-mt-md">加载训练数据中...</div>
                </q-inner-loading>
              </q-card-section>
            </q-card>
          </div>
          <div class="col-12 col-md-6">
            <q-card flat class="visualization-card">
              <q-card-section class="q-pa-none">
                <div ref="resourceChartRef" style="height: 4.5rem; width: 100%;"></div>
                <q-inner-loading :showing="loading">
                  <q-spinner-gears size="50px" color="primary" />
                  <div class="q-mt-md">加载资源数据中...</div>
                </q-inner-loading>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- 操作按钮 -->
    <div class="row q-gutter-md justify-center q-mt-lg">
      <q-btn class="roundBox reinBtn" color="primary" label="保存训练配置" :loading="loading" @click="saveTrainingConfig" />
      <q-btn class="roundBox reinBtn" color="primary" label="批量导入配置" :loading="loading"
        @click="() => uploaderRef?.pickFiles()">
        <q-uploader ref="uploaderRef" style="display: none;" accept=".json" max-files="1"
          @added="(files) => importBatchConfig(files[0])" hide-upload-btn />
      </q-btn>
      <q-btn class="roundBox reinBtn" color="secondary" label="开始训练" :loading="loading" @click="startTraining" />
      <q-btn class="roundBox reinBtn" color="grey" label="取消训练" :loading="loading" @click="stopTraining" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import * as echarts from 'echarts'
import { api } from 'boot/axios'
import { Notify } from 'quasar'

// 算法版本数据
const algorithmVersions = [
  'torch-1.8.1',
  'torch-1.9.0',
  'torch-1.10.0',
  'torch-1.11.0'
]

// 强化学习算法类型
const rlAlgorithmTypes = [
  'Q-Learning',
  'DQN',
  'PPO',
  'DDPG',
  'A3C',
  'SAC'
]

// 数据来源选项
const dataSourceOptions = [
  '内置仿真环境',
  '真实环境数据',
  '外部仿真环境',
  '自定义环境'
]

// 动作空间选项
const actionSpaceOptions = [
  '离散动作空间',
  '连续动作空间',
  '混合动作空间'
]

// 观测空间选项
const observationSpaceOptions = [
  '离散观测空间',
  '连续观测空间',
  '图像观测空间'
]

// 奖励函数选项
const rewardFunctionOptions = [
  '标准奖励函数',
  '距离奖励函数',
  '自定义奖励函数'
]

// 学习率策略选项
const learningRateStrategies = [
  '余弦衰减',
  '线性衰减',
  '阶梯衰减',
  '固定学习率'
]

// 计算类型选项
const computeTypes = [
  'bf16',
  'fp16',
  'fp32',
  'int8'
]

// 算法配置
const algorithm = ref({
  version: 'torch-1.8.1',
  rlType: 'PPO'
})

// 仿真环境配置
const simulation = ref({
  dataSource: '内置仿真环境',
  useExternalEnv: false,
  externalEnvAddress: ''
})

// 智能体设置
const agent = ref({
  sampleTime: '',
  actionSpace: '离散动作空间',
  observationSpace: '连续观测空间',
  rewardFunction: '标准奖励函数'
})

// 超参数配置
const hyperParams = ref({
  learningRate: '1e-5',
  epochs: '50',
  batchSize: 0,
  learningRateStrategy: '余弦衰减',
  computeType: 'fp32',
  validationRatio: 0
})

// 集群设置
const cluster = ref({
  cpuCount: '4',
  gpuCount: '1'
})

// 输出设置
const output = ref({
  savePath: '',
  saveName: ''
})

// 显示更多设置
const showMoreSettings = ref(false)

// 图表引用
const lossChartRef = ref(null)
const resourceChartRef = ref(null)
let lossChart = null
let resourceChart = null

// 训练数据状态 - 使用默认模拟数据
const trainingData = ref({
  episodes: Array.from({ length: 100 }, (_, i) => i * 50),
  rewards: Array.from({ length: 100 }, () => Math.random() * 10 - 5).map((val, idx) => {
    return val + idx * 0.2 * (1 - Math.exp(-idx / 30))
  }),
  losses: Array.from({ length: 100 }, () => Math.random() * 0.5 + 1).map((val, idx) => val * Math.exp(-idx / 25))
})

// 资源利用率数据状态 - 使用默认模拟数据
const resourceData = ref({
  timestamps: Array.from({ length: 100 }, (_, i) => `${String(10 + Math.floor(i / 6)).padStart(2, '0')}:${String((i % 6) * 10).padStart(2, '0')}`),
  cpuUsage: Array.from({ length: 100 }, () => Math.random() * 30 + 50),
  npuUsage: Array.from({ length: 100 }, () => Math.random() * 40 + 60),
  memoryUsage: Array.from({ length: 100 }, () => Math.random() * 20 + 70)
})

// 数据加载状态
const loading = ref(false)
const dataUpdateInterval = ref(null)

// 文件上传引用
const uploaderRef = ref(null)

/**
 * 后端API接口说明:
 * 
 * 获取训练数据:
 * GET /backend/training/rl/{trainingId}/metrics
 * 返回格式: {
 *   success: boolean,
 *   data: {
 *     episodes: number[],           // 训练回合数组
 *     cumulativeRewards: number[],  // 累积奖励数组
 *     policyLosses: number[]        // 策略损失数组
 *   }
 * }
 * 
 * 获取资源利用率:
 * GET /backend/training/rl/{trainingId}/resources
 * 返回格式: {
 *   success: boolean,
 *   data: {
 *     timestamps: string[],         // 时间戳数组
 *     cpuUtilization: number[],     // CPU利用率数组
 *     npuUtilization: number[],     // NPU利用率数组
 *     memoryUtilization: number[]   // 内存利用率数组
 *   }
 * }
 * 
 * 开始训练:
 * POST /backend/training/rl/start
 * 请求体: 训练配置对象
 * 返回格式: {
 *   success: boolean,
 *   data: {
 *     trainingId: string            // 训练任务ID
 *   }
 * }
 * 
 * 停止训练:
 * POST /backend/training/rl/{trainingId}/stop
 * 
 * 保存配置:
 * POST /backend/training/rl/config
 * 
 * 导入配置:
 * POST /backend/training/rl/config/import
 * Content-Type: multipart/form-data
 */

// API调用函数
/**
 * 获取训练过程数据
 * @param {string} trainingId - 训练任务ID
 */
async function fetchTrainingData(trainingId = 'default') {
  try {
    loading.value = true
    const response = await api.get(`/backend/training/rl/${trainingId}/metrics`)

    if (response.success) {
      // 更新训练数据
      trainingData.value = {
        episodes: response.data.episodes || [],
        rewards: response.data.cumulativeRewards || [],
        losses: response.data.policyLosses || []
      }

      // 更新图表
      updateLossChart()
    }
  } catch (error) {
    console.error('获取训练数据失败:', error)
    Notify.create({
      message: '获取训练数据失败',
      type: 'negative'
    })
  } finally {
    loading.value = false
  }
}

/**
 * 获取资源利用率数据
 * @param {string} trainingId - 训练任务ID
 */
async function fetchResourceData(trainingId = 'default') {
  try {
    const response = await api.get(`/backend/training/rl/${trainingId}/resources`)

    if (response.success) {
      // 更新资源数据
      resourceData.value = {
        timestamps: response.data.timestamps || [],
        cpuUsage: response.data.cpuUtilization || [],
        npuUsage: response.data.npuUtilization || [],
        memoryUsage: response.data.memoryUtilization || []
      }

      // 更新图表
      updateResourceChart()
    }
  } catch (error) {
    console.error('获取资源数据失败:', error)
    Notify.create({
      message: '获取资源利用率数据失败',
      type: 'negative'
    })
  }
}

/**
 * 开始训练
 */
async function startTraining() {
  try {
    loading.value = true

    const trainingConfig = {
      algorithm: algorithm.value,
      simulation: simulation.value,
      agent: agent.value,
      hyperParams: hyperParams.value,
      cluster: cluster.value,
      output: output.value
    }

    const response = await api.post('/backend/training/rl/start', trainingConfig)

    if (response.success) {
      Notify.create({
        message: '训练已开始',
        type: 'positive'
      })

      // 开始定时获取数据
      startDataPolling(response.data.trainingId)
    }
  } catch (error) {
    console.error('开始训练失败:', error)
    Notify.create({
      message: '开始训练失败: ' + (error.message || '未知错误'),
      type: 'negative'
    })
  } finally {
    loading.value = false
  }
}

/**
 * 停止训练
 */
async function stopTraining(trainingId = 'default') {
  try {
    // 停止数据轮询
    stopDataPolling()

    // 如果有正在进行的训练，调用后端接口停止
    if (dataUpdateInterval.value) {
      const response = await api.post(`/backend/training/rl/${trainingId}/stop`)

      if (response.success) {
        Notify.create({
          message: '训练已停止',
          type: 'positive'
        })
      }
    } else {
      // 没有正在进行的训练
      Notify.create({
        message: '当前没有正在进行的训练',
        type: 'warning'
      })
    }
  } catch (error) {
    console.error('停止训练失败:', error)
    // 即使后端调用失败，也要停止前端的轮询和状态
    stopDataPolling()
    Notify.create({
      message: '训练已强制停止（后端通信失败）',
      type: 'warning'
    })
  }
}

/**
 * 保存训练配置
 */
async function saveTrainingConfig() {
  try {
    loading.value = true

    const config = {
      algorithm: algorithm.value,
      simulation: simulation.value,
      agent: agent.value,
      hyperParams: hyperParams.value,
      cluster: cluster.value,
      output: output.value
    }

    const response = await api.post('/backend/training/rl/config', config)

    if (response.success) {
      Notify.create({
        message: '配置保存成功',
        type: 'positive'
      })
    }
  } catch (error) {
    console.error('保存配置失败:', error)
    Notify.create({
      message: '保存配置失败',
      type: 'negative'
    })
  } finally {
    loading.value = false
  }
}

/**
 * 批量导入配置
 */
async function importBatchConfig(file) {
  try {
    loading.value = true

    const formData = new FormData()
    formData.append('config', file)

    const response = await api.post('/backend/training/rl/config/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    if (response.success) {
      // 更新本地配置
      const importedConfig = response.data
      algorithm.value = importedConfig.algorithm || algorithm.value
      simulation.value = importedConfig.simulation || simulation.value
      agent.value = importedConfig.agent || agent.value
      hyperParams.value = importedConfig.hyperParams || hyperParams.value
      cluster.value = importedConfig.cluster || cluster.value
      output.value = importedConfig.output || output.value

      Notify.create({
        message: '配置导入成功',
        type: 'positive'
      })
    }
  } catch (error) {
    console.error('导入配置失败:', error)
    Notify.create({
      message: '导入配置失败',
      type: 'negative'
    })
  } finally {
    loading.value = false
  }
}

/**
 * 开始数据轮询
 */
function startDataPolling(trainingId = 'default') {
  // 先获取一次数据
  fetchTrainingData(trainingId)
  fetchResourceData(trainingId)

  // 设置定时器，每10秒更新一次数据
  dataUpdateInterval.value = setInterval(() => {
    fetchTrainingData(trainingId)
    fetchResourceData(trainingId)
  }, 10000)
}

/**
 * 停止数据轮询
 */
function stopDataPolling() {
  if (dataUpdateInterval.value) {
    clearInterval(dataUpdateInterval.value)
    dataUpdateInterval.value = null
  }
}

// 初始化损失曲线图
function initLossChart() {
  if (lossChartRef.value) {
    lossChart = echarts.init(lossChartRef.value)
    const option = {
      title: {
        text: '训练曲线',
        left: 'center',
        textStyle: {
          color: '#fff'
        }
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['累积奖励', '策略损失'],
        bottom: 0,
        textStyle: {
          color: '#fff',
          fontSize: window.screen.width <= 1536 ? 12:16,
        }
      },
      grid: {
        left: '8%',
        right: '8%',
        bottom: '15%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: trainingData.value.episodes,
        name: '回合数',
        nameLocation: 'middle',
        nameGap: 30,
        nameTextStyle: {
          fontSize: window.screen.width <= 1536 ? 12:16,
        },
        axisLabel: {
          color: '#fff',
          fontSize: window.screen.width <= 1536 ? 12:16,
        },
        axisLine: {
          show: false,
          lineStyle: {
            type: 'solid',
            color: '#fff'
          }
        },
      },
      yAxis: [
        {
          type: 'value',
          name: '奖励',
          position: 'left',
          nameLocation: 'middle',
          nameRotate: 0,
          nameGap: 40,
          nameTextStyle: {
            fontSize: window.screen.width <= 1536 ? 12:16,  // 文字大小
          },
          axisLabel: {
            fontSize: window.screen.width <= 1536 ? 12:16,
            color: '#fff',
            formatter: '{value}'
          },
          axisLine: {
            show: false,
            lineStyle: {
              type: 'solid',
              color: '#fff'
            }
          }
        },
        {
          type: 'value',
          name: '损失',
          position: 'right',
          nameLocation: 'middle',
          nameRotate: 0,
          nameGap: 40,
          nameTextStyle: {
            fontSize: window.screen.width <= 1536 ? 12:16,  // 文字大小
          },
          axisLabel: {
            fontSize: window.screen.width <= 1536 ? 12:16,
            color: '#fff',
            formatter: '{value}'
          },
          axisLine: {
            show: false,
            lineStyle: {
              type: 'solid',
              color: '#fff'
            }
          }
        },

      ],
      series: [
        {
          name: '累积奖励',
          type: 'line',
          data: trainingData.value.rewards,
          symbolSize: 6,
          sampling: 'lttb',
          itemStyle: {
            color: '#4CAF50'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(76, 175, 80, 0.5)' },
              { offset: 1, color: 'rgba(76, 175, 80, 0.1)' }
            ])
          },
          smooth: true,
          yAxisIndex: 0
        },
        {
          name: '策略损失',
          type: 'line',
          data: trainingData.value.losses,
          symbolSize: 6,
          sampling: 'lttb',
          itemStyle: {
            color: '#FF5722'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(255, 87, 34, 0.5)' },
              { offset: 1, color: 'rgba(255, 87, 34, 0.1)' }
            ])
          },
          smooth: true,
          yAxisIndex:1
        }
      ]
    }
    lossChart.setOption(option)
  }
}

// 更新损失曲线图
function updateLossChart() {
  if (lossChart && trainingData.value) {
    const option = {
      xAxis: {
        data: trainingData.value.episodes
      },
      series: [
        {
          data: trainingData.value.rewards
        },
        {
          data: trainingData.value.losses
        }
      ]
    }
    lossChart.setOption(option)
  }
}

// 初始化资源利用率图
function initResourceChart() {
  if (resourceChartRef.value) {
    resourceChart = echarts.init(resourceChartRef.value)
    const option = {
      title: {
        text: '集群资源利用率',
        left: 'center',
        textStyle: {
          color: '#fff',
          // fontSize:18
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        }
      },
      legend: {
        data: ['CPU利用率', 'NPU利用率', '内存利用率'],
        bottom: 0,
        textStyle: {
          color: '#fff',
          fontSize:window.screen.width <= 1536 ? 12:16
        }
      },
      grid: {
        left: '10%',
        right: '4%',
        bottom: '15%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: resourceData.value.timestamps,
        name: '时间',
        nameLocation: 'middle',
        nameGap: 30,
        nameTextStyle: {
          fontSize: window.screen.width <= 1536 ? 12:16,
        },
        axisLabel: {
          formatter: function (value) {
            // 如果是完整的ISO时间戳，格式化为简洁的时间显示
            if (value && value.includes('T')) {
              const date = new Date(value)
              return date.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
              })
            }
            // 如果已经是简单格式，直接返回
            return value
          },
          fontSize: window.screen.width <= 1536 ? 12:16,
        },
        axisLine: {
          show: false,
          lineStyle: {
            type: 'solid',
            color: '#fff'
          }
        }
      },
      yAxis: {
        type: 'value',
        name: '利用率(%)',
        min: 0,
        max: 100,
        nameLocation: 'middle',
        nameRotate:0,
        nameGap: 40,
        nameTextStyle: {
          fontSize: window.screen.width <= 1536 ? 12:16,
        },
        axisLabel: {
          fontSize: window.screen.width <= 1536 ? 12:16,
        },
        axisLine: {
          show: false,
          lineStyle: {
            type: 'solid',
            color: '#fff'
          }
        }
      },
      series: [
        {
          name: 'CPU利用率',
          type: 'line',
          data: resourceData.value.cpuUsage,
          symbolSize: 6,
          sampling: 'lttb',
          itemStyle: {
            color: '#4CAF50'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(76, 175, 80, 0.5)' },
              { offset: 1, color: 'rgba(76, 175, 80, 0.1)' }
            ])
          },
          smooth: true
        },
        {
          name: 'NPU利用率',
          type: 'line',
          data: resourceData.value.npuUsage,
          symbolSize: 6,
          sampling: 'lttb',
          itemStyle: {
            color: '#FF9800'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(255, 152, 0, 0.5)' },
              { offset: 1, color: 'rgba(255, 152, 0, 0.1)' }
            ])
          },
          smooth: true
        },
        {
          name: '内存利用率',
          type: 'line',
          data: resourceData.value.memoryUsage,
          symbolSize: 6,
          sampling: 'lttb',
          itemStyle: {
            color: '#9C27B0'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(156, 39, 176, 0.5)' },
              { offset: 1, color: 'rgba(156, 39, 176, 0.1)' }
            ])
          },
          smooth: true
        }
      ]
    }
    resourceChart.setOption(option)
  }
}

// 更新资源利用率图
function updateResourceChart() {
  if (resourceChart && resourceData.value) {
    const option = {
      xAxis: {
        data: resourceData.value.timestamps
      },
      series: [
        {
          data: resourceData.value.cpuUsage
        },
        {
          data: resourceData.value.npuUsage
        },
        {
          data: resourceData.value.memoryUsage
        }
      ]
    }
    resourceChart.setOption(option)
  }
}

// 窗口大小改变时重置图表大小
function resizeCharts() {
  lossChart?.resize()
  resourceChart?.resize()
}

// 增减批处理大小
function increaseBatchSize() {
  hyperParams.value.batchSize++
}

function decreaseBatchSize() {
  if (hyperParams.value.batchSize > 0) {
    hyperParams.value.batchSize--
  }
}

// 增减验证集比例
function increaseValidationRatio() {
  if (hyperParams.value.validationRatio < 1) {
    hyperParams.value.validationRatio += 0.1
    hyperParams.value.validationRatio = parseFloat(hyperParams.value.validationRatio.toFixed(1))
  }
}

function decreaseValidationRatio() {
  if (hyperParams.value.validationRatio > 0) {
    hyperParams.value.validationRatio -= 0.1
    hyperParams.value.validationRatio = parseFloat(hyperParams.value.validationRatio.toFixed(1))
  }
}

// 生命周期钩子
onMounted(() => {
  // 初始化图表
  initLossChart()
  initResourceChart()

  // 监听窗口大小变化
  window.addEventListener('resize', resizeCharts)
})

onBeforeUnmount(() => {
  // 移除事件监听
  window.removeEventListener('resize', resizeCharts)

  // 停止数据轮询
  stopDataPolling()

  // 销毁图表实例
  lossChart?.dispose()
  resourceChart?.dispose()
})

// 监听训练数据变化，自动更新图表
watch(trainingData, () => {
  updateLossChart()
}, { deep: true })

watch(resourceData, () => {
  updateResourceChart()
}, { deep: true })
</script>

<style lang="scss" scoped>
.visualization-card {
  border: 1px solid #eaeaea;
  border-radius: 4px;
}

::v-deep .q-field--auto-height.q-field--dense.q-field--labeled .q-field__control-container {
  padding-top: 0;
}

.normal {
  padding-left: 0 !important;
}

/* 编辑神经网络 按钮 */
.purpleBtn {
  background-color: #4a2e66 !important;
  border: 1px solid #8962ff;
}

/* :deep(.q-toggle__inner--truthy){
  color:#165dff !important;
} */

.padleft {
  padding-left: 0 !important;
}

.reinBtn {
  height: .45rem;
}

:deep(.q-btn__content) {
  font-size: .175rem;
}

.font20 {
  font-size: .225rem;
}

.font14 {
  font-size: .175rem;
}
:deep(.el-switch){
  font-size: .175rem;
  line-height: .25rem;
  height: .4rem;
}
:deep(.el-switch__core){
  height: .25rem;
  min-width: .5rem;
  border-radius: .125rem;
}
:deep(.el-switch.is-checked .el-switch__core .el-switch__inner){
  padding: 0 .225rem 0 .05rem;
  height: .2rem;
}
:deep(.el-switch__core .el-switch__inner .is-text){
  font-size:.15rem !important;
}
.form-field-unified {
  // min-width: 160px;
  // max-width: 240px;
  flex: 1 1 0;
  margin-right: 16px;
}
.form-row-aligned {
  display: flex;
  align-items: center;
  gap: 0;
}
.form-title {
  width: 2rem;
  min-width: 2rem;
  max-width: 2rem;
  font-size: .225rem;
  color: #fff;
  white-space: nowrap;
  margin-right: 0.2rem;
}
.form-label {
  min-width: 1.5rem;
  max-width: 2rem;
  font-size: .175rem;
  color: #fff;
  white-space: nowrap;
  margin-right: .1rem;
  text-align: right;
}
.form-group {
  display: flex;
  align-items: center;
  // margin-right: 16px;
  flex: 1;
}
.centerEl{
  justify-content: center;
}
.textRight{
  text-align: right;
}
</style>
