<template>
  <div class="full-height column">
    <template v-if="!hideTop">
      <slot name="top">
        <div class="flex items-center">
          <div class="q-py-xs text-huangyou fs-28">{{ title }}</div>
          <q-input
            v-model.trim="filter"
            @update:model-value="getRowsFormApi"
            debounce="500"
            class="q-ml-auto q-mr-xl search-input"
            borderless
            dense
            clearable
          >
            <template v-slot:prepend>
              <q-icon name="search" color="primary" size="24px" />
            </template>
          </q-input>
          <slot name="action" />
        </div>
        <q-separator color="primary" size="2px" class="q-mb-md" />
      </slot>

      <slot name="header" />
    </template>

    <div class="col">
      <q-table
        @request="getRowsFormApi"
        v-model:pagination="pagination"
        :columns="columns"
        :rows="apiUrl ? rowsFormApi : rows"
        :loading="loading"
        :rows-per-page-options="[0]"
        card-class="bg-transparent"
        :hide-pagination="rowsPerPage == 0"
        separator="none"
        binary-state-sort
        flat
      >
        <template v-for="slot in slots" #[slot]="props">
          <slot :name="slot" v-bind="props" />
        </template>
      </q-table>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onUnmounted, onMounted } from 'vue'
import { api } from 'boot/axios'
import _ from 'lodash'

const props = defineProps({
  title: String,
  columns: Array,
  rows: Array,
  apiUrl: String,
  params: {
    Object,
    default: () => {},
  },
  filterKey: {
    type: String,
    default: 'name',
  },
  resultKey: {
    type: String,
    default: 'results',
  },
  hideTop: Boolean,
  interval: Number,
  rowsPerPage: {
    type: Number,
    default: 10,
  },
})

defineExpose({ getRows: getRowsFormApi })

const slots = computed(() => props.columns.filter(item => item.slot).map(item => `body-cell-${item.name}`))

const rowsFormApi = ref([])
const loading = ref(false)
const filter = ref('')
const pagination = ref({
  page: 1,
  rowsPerPage: props.rowsPerPage,
  rowsNumber: 0,
})

let timer

if (props.interval) {
  timer = setInterval(getRowsFormApi(), props.interval * 1000)
} else {
  if (props.apiUrl && props.params) {
    const hasNonUndefinedValue = Object.values(props.params).some(value => value !== undefined);
    if (hasNonUndefinedValue) {
      getRowsFormApi()
    }
  } else {
    props.apiUrl && getRowsFormApi()
  }
}

onUnmounted(() => {
  timer && clearInterval(timer)
})

watch(() => props.params, getRowsFormApi)

function getRowsFormApi(params) {
  const { page, rowsPerPage } = params?.pagination || pagination.value

  loading.value = true

  api
    .get(props.apiUrl, {
      params: {
        page,
        page_size: rowsPerPage,
        [props.filterKey]: filter.value || null,
        ...props.params,
      },
    })
    .then(res => {
      rowsFormApi.value = _.get(res, props.resultKey) ?? []
      pagination.value.page = page
      pagination.value.rowsNumber = res.count
      loading.value = false
    })
    .catch(() => {
      loading.value = false
    })

  return getRowsFormApi
}
</script>

<style lang="scss" scoped>
.search-input {
  border-bottom: 6px solid #000;
  transform: translateY(3.5px);
}

.body--dark {
  .search-input {
    border-bottom: 6px solid #fff;
  }
}
</style>
