<template>
  <TableCard :columns="columns" title="模型列表" api-url="backend/models/" ref="table">
    <template #body-cell-action="props">
      <q-td :props="props">
        <q-btn v-if="props.row.address" @click="openModelURL(props.row.address)" label="下载" color="primary" flat dense />
        <q-btn v-if="props.row.address" @click="showSimulationDialog(props.row.id)" label="推演" color="primary" class="q-ml-md" flat dense />
        <q-btn :to="`/model/detail/${props.row.id}`" label="详情" color="primary" class="q-ml-md" flat dense />
        <q-btn @click="onDelete(props.row.id)" label="删除" color="primary" class="q-ml-md" flat dense />
      </q-td>
    </template>
  </TableCard>
</template>

<script setup>
import { ref } from 'vue'
import { date } from 'quasar'
import { api } from 'boot/axios'
import { usePlugin } from 'composables/plugin'
import TableCard from 'components/TableCard.vue'
import { formatFileSize } from 'assets/utils'
import { useQuasar } from 'quasar'
import SimulationDialog from './SimulationDialog.vue'

const { dialog, notify } = usePlugin()
const $q = useQuasar()

const columns = [
  {
    name: 'name',
    field: 'name',
    label: '模型名称',
    align: 'center',
  },
  {
    name: 'task',
    field: 'task_name',
    label: '关联训练',
    align: 'center',
  },
  {
    name: 'size',
    field: 'size',
    format: val => formatFileSize(val),
    label: '文件大小',
    align: 'center',
  },
  {
    name: 'creator',
    field: 'creater_name',
    label: '创建人员',
    align: 'center',
  },
  {
    name: 'createTime',
    field: 'model_create_time',
    sortable: true,
    format: val => date.formatDate(val, 'YYYY-MM-DD HH:mm:ss'),
    label: '生成时间',
    align: 'center',
  },
  {
    name: 'action',
    label: '操作栏',
    align: 'center',
    slot: true,
  },
]

const table = ref(null)

function openModelURL(address) {
  if (address) {
    window.open(import.meta.env.VITE_API + address)
  }
}

function showSimulationDialog(modelId) {
  $q.dialog({
    component: SimulationDialog,
    componentProps: {
      modelId: modelId
    }
  }).onOk(() => {
    // 可以在这里添加推演成功后的刷新逻辑
  })
}

function onDelete(id) {
  dialog('确认删除吗？').onOk(() => {
    api
      .delete(`backend/models/${id}/`)
      .then(() => {
        notify('删除成功', 'positive')
        table.value.getRows()
      })
      .catch(() => {
        notify('删除失败')
      })
  })
}
</script>
