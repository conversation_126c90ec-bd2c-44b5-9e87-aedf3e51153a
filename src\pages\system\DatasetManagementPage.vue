<template>
  <div class="q-pa-md">
    <!-- <div class="text-h5 q-mb-md labelColor">数据集管理</div> -->

    <div class="row q-col-gutter-lg">
      <!-- 左侧：数据集文件管理 -->
      <div class="col-12 col-md-8">
        <q-card flat bordered class="q-mb-md">
          <q-card-section>
            <div class="row q-col-gutter-md">
              <div class="col-md-4 col-sm-6 col-12 flexbox">
                <div class="labelT">上传人</div>
                <q-input style="flex: 1;" outlined dense :label="filterOptions.creator ? '' : '请输入'"
                  v-model="filterOptions.creator" clearable>
                  <template v-slot:append>
                    <q-icon name="person" />
                  </template>
                </q-input>
              </div>

              <div class="col-md-4 col-sm-6 col-12 flexbox">
                <div class="labelT">数据集名称</div>
                <q-input style="flex: 1;" outlined dense :label="filterOptions.name ? '' : '请输入'"
                  v-model="filterOptions.name" clearable>
                  <template v-slot:append>
                    <q-icon name="search" />
                  </template>
                </q-input>
              </div>

              <div class="col-md-4 col-sm-6 col-12 flexbox">
                <div class="labelT">上传时间</div>
                <!-- element plus 时间选择器 -->
                <el-date-picker class="datePicker" style="flex: 1;" size="large" v-model="filterOptions.dateVal"
                  type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                  value-format="YYYY-MM-DD" @change="dateChange" />
              </div>

              <!-- 注释掉数据集类型选择器 -->
              <!-- <div class="col-md-3 col-sm-6 col-12 flexbox">
                <div class="labelT">数据集类型</div>
                <q-select style="flex: 1;" outlined dense :label="filterOptions.dataset_type ? '' : '请输入'"
                  v-model="filterOptions.dataset_type" :options="datasetTypeOptions" emit-value map-options clearable>
                  <template v-slot:append>
                    <q-icon name="category" />
                  </template>
                </q-select>
              </div> -->

              <!-- 注释掉数据集状态选择器 -->
              <!-- <div class="col-md-3 col-sm-6 col-12 flexbox">
                <div class="labelT">数据集状态</div>
                <q-select style="flex: 1;" outlined dense :label="filterOptions.dataset_status ? '' : '请输入'"
                  v-model="filterOptions.dataset_status" :options="statusOptions" emit-value map-options clearable>
                  <template v-slot:append>
                    <q-icon name="check_circle" />
                  </template>
                </q-select>
              </div> -->

              <!-- 注释掉日期选择器 -->
              <!-- <div class="col-md-7 col-12">
                <div class="row q-col-gutter-sm">
                  <div class="col-5">
                    <q-input outlined dense v-model="filterOptions.start_date"
                      :label="filterOptions.start_date ? '' : '开始日期'" type="date" clearable />
                  </div>
                  <div class="text">至</div>
                  <div class="col-5 inputel">
                    <q-input outlined dense v-model="filterOptions.end_date" :label="filterOptions.end_date ? '' : '结束日期'"
                      type="date" clearable />
                  </div>
                </div>
              </div> -->

              <div class="col-12" style="display: flex;justify-content: center;">
                <div class="flexCenter">
                  <q-btn color="primary" label="查询" class="q-mr-sm roundBox" @click="handleSearch" />
                  <q-btn outline color="primary" class="grayBox roundBox" label="重置" @click="resetSearch" />
                </div>
              </div>
            </div>
          </q-card-section>
        </q-card>

        <q-table flat bordered :rows="datasetFiles" :columns="columns" row-key="id" selection="multiple"
          v-model:selected="selected" :pagination="pagination.value" :loading="loading" :rows-per-page-options="[0]"
          hide-selection-column>
          <template v-slot:body-cell-status="props">
            <q-td :props="props">
              <q-chip :color="getStatusColor(props.row.dataset_status_display)" text-color="white" dense
                :label="props.row.dataset_status_display || '未知'" />
            </q-td>
          </template>

          <template v-slot:body-cell-operations="props">
            <q-td :props="props">
              <q-btn class="customText" flat dense size="sm" color="primary" label="下载"
                @click="downloadDataset(props.row)" v-if="props.row.file" />
              <q-btn class="customText" flat dense size="sm" color="primary" label="查看"
                @click="viewDataset(props.row)" />
              <q-btn class="customText" flat dense size="sm" color="negative" label="删除"
                @click="confirmDelete(props.row)" />
            </q-td>
          </template>

          <template v-slot:bottom>
            <div class="row items-center full-width paginationEl">
              <div style="margin-right:.25rem;">总计 {{ pagination.total }} 条</div>
              <div>
                <q-select class="unsetHeight" v-model="pagination.rowsPerPage" :options="[5, 10,]" outlined dense options-dense emit-value
                  map-options style="min-width:1.25rem" @update:model-value="onRowsPerPageChange">
                  <!-- 自定义显示值 -->
                  <template v-slot:selected>
                    {{ pagination.rowsPerPage }}/页
                  </template>
                </q-select>
              </div>

              <q-pagination class="unsetHeight" style="margin-left: 1rem;margin-right: 1rem;" v-model="pagination.page"
                :max="Math.ceil(pagination.total / pagination.rowsPerPage)" :max-pages="5" boundary-numbers
                direction-links />

              <div class="flexbox">
                <div style="margin-right: .125rem;">跳到</div>
                <div class="roundBox">
                  <q-input class="dynamic-label-input" v-model="jumpText" style="width:.625rem;" dense
                    @keyup.enter="goJump">
                  </q-input>
                </div>
                <q-btn class="custom-btn" label="跳转" @click="goJump" />
              </div>

            </div>
          </template>
        </q-table>
      </div>

      <!-- 右侧：数据集统计 -->
      <div class="col-12 col-md-4">
        <q-card flat bordered>
          <q-card-section class="q-pb-none" style="padding-bottom: 0px !important;">
            <div class="labelColor">数据集统计</div>
          </q-card-section>
          <q-separator class="mt10"></q-separator>
          <q-card-section style="padding: 0px;">
            <!-- 数据集类型分布饼图 -->
            <!-- <div class="q-mb-sm labelColor">数据集类型分布</div> -->
            <div class="flex justify-center">
              <div class="model-distribution-chart">
                <div ref="pieChart" style="width: inherit; height: 3.75rem;"></div>
              </div>
            </div>
          </q-card-section>
        </q-card>

        <q-card class="mt10">
          <q-card-section class="noPadingBot" style="padding-bottom: 0px !important;">
            <div class="labelColor">存储使用量</div>
          </q-card-section>

          <q-separator class="mt10"></q-separator>

          <q-card-section style="padding-bottom: 0px !important;">
            <div class="flexReverse">
              <div class="labelColor positionLeft" style="margin-top: .25rem;flex:0 0 auto;">
                <div class="smallText">总计: {{ totalStorage }}</div>
                <div class="smallText">已使用: {{ usedStorage }}</div>
              </div>
              <div ref="gaugeChart" style="width:100%; height: 3.75rem;"></div>
            </div>
          </q-card-section>
        </q-card>

        <q-card class="mt10">
          <div class="btnCon">
            <q-btn color="primary" label="上传数据集" class="q-mr-sm roundBox btn50" @click="uploadDialog = true" />
            <q-btn color="secondary" label="导出数据集" class="roundBox btn50" @click="exportDataset" />
          </div>
        </q-card>
      </div>
    </div>

    <!-- 上传数据集弹窗 -->
    <q-dialog v-model="uploadDialog">
      <q-card class="blackCard">
        <q-card-section class="row items-center">
          <div class="labelColor">上传数据集</div>
          <q-space />
          <q-btn class="labelColor closeBtn" icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section class="q-ml-md q-pa-md">
          <q-form @submit="onSubmit" class="q-gutter-md">
            <div class="row q-col-gutter-md">
              <div class="col-12">
                <q-input v-model="newDataset.name" :label="newDataset.name ? '' : '数据集名称'" outlined dense
                  :rules="[val => !!val || '请输入数据集名称']" />
              </div>

              <div class="col-12">
                <q-input v-model="newDataset.description" :label="newDataset.description ? '' : '数据集描述'" outlined dense
                  type="textarea" autogrow />
              </div>

              <div class="col-sm-6 col-xs-12">
                <q-select v-model="newDataset.dataset_type" :options="datasetTypeOptions"
                  :label="newDataset.dataset_type ? '' : '数据集类型'" outlined dense map-options emit-value
                  :rules="[val => !!val || '请选择数据集类型']" />
              </div>

              <div class="col-sm-6 col-xs-12">
                <q-input v-model="newDataset.sample_count" :label="newDataset.sample_count != null ? '' : '样本数量'"
                  outlined dense type="number" :rules="[
                    val => val >= 0 || '样本数量必须为非负数'
                  ]" />
              </div>
            </div>

            <q-separator class="q-my-md mybr" />

            <div class="row q-col-gutter-md">
              <div class="col-12">
                <q-file v-model="uploadFiles.dataset_file" :label="uploadFiles.dataset_file ? '' : '数据集文件'" outlined
                  dense counter accept=".zip,.tar,.gz,.csv,.json,.jsonl,.parquet,.h5,.pkl,.npy"
                  :rules="[val => !!val || '请选择数据集文件']" hint="上传数据集文件">
                  <template v-slot:prepend>
                    <q-icon name="description" />
                  </template>
                </q-file>
              </div>

              <div class="col-12">
                <q-file v-model="uploadFiles.metadata_file" :label="uploadFiles.metadata_file ? '' : '元数据文件 (可选)'"
                  outlined dense counter accept=".json,.yaml,.yml,.xml,.txt" hint="上传元数据文件">
                  <template v-slot:prepend>
                    <q-icon name="settings" />
                  </template>
                </q-file>
              </div>
            </div>

            <div class="row justify-end q-mt-md">
              <q-btn label="取消" color="grey-7" v-close-popup class="q-mr-sm roundBox" />
              <q-btn label="上传" type="submit" color="primary" class="roundBox" :loading="submitting" />
            </div>
          </q-form>
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- 删除确认弹窗 -->
    <q-dialog v-model="deleteDialog" persistent>
      <q-card class="blackCard">
        <q-card-section class="row items-center">
          <q-avatar icon="delete" color="negative" text-color="white" class="delAvatar" />
          <span class="q-ml-sm delContent">确认删除此数据集?</span>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" color="primary" v-close-popup />
          <q-btn flat label="删除" color="negative" @click="deleteDataset" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 查看数据集详情对话框 -->
    <q-dialog v-model="viewDialog">
      <q-card class="blackCard">
        <q-card-section class="row items-center">
          <div class="labelColor">数据集详情</div>
          <q-space />
          <q-btn class="labelColor closeBtn" icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section>
          <div class="q-gutter-y-md">
            <div class="row labelColor">
              <div class="fontlabel">数据集名称:</div>
              <div class="font10">{{ viewingRecord.name }}</div>
            </div>
            <div class="row labelColor">
              <div class="fontlabel">数据集大小:</div>
              <div class="font10">{{ viewingRecord.size }}</div>
            </div>
            <div class="row labelColor">
              <div class="fontlabel">上传人:</div>
              <div class="font10">{{ viewingRecord.creator }}</div>
            </div>
            <div class="row labelColor">
              <div class="fontlabel">数据集类型:</div>
              <div class="font10">{{ viewingRecord.dataset_type }}</div>
            </div>
            <div class="row labelColor">
              <div class="fontlabel">文件格式:</div>
              <div class="font10">{{ viewingRecord.format }}</div>
            </div>
            <div class="row labelColor">
              <div class="fontlabel">数据集状态:</div>
              <div class="font10">{{ viewingRecord.status }}</div>
            </div>
            <div class="row labelColor">
              <div class="fontlabel">创建时间:</div>
              <div class="font10">{{ formatDate(viewingRecord.create_time) }}</div>
            </div>
            <div class="row labelColor" v-if="viewingRecord.description">
              <div class="fontlabel">描述:</div>
              <div class="font10">{{ viewingRecord.description }}</div>
            </div>
          </div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn class="roundBox grayBox" flat label="关闭" color="primary" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { usePlugin } from 'composables/plugin'
import { api } from 'boot/axios'
import { date } from 'quasar'
import { formatFileSize } from 'assets/utils'
import { useRoute } from 'vue-router'
import * as echarts from 'echarts'

const { notify, dialog } = usePlugin()
const route = useRoute()

// ECharts相关
const pieChart = ref(null)
let pieChartInstance = null

// 仪表盘相关
const gaugeChart = ref(null)
let gaugeChartInstance = null

// 查看详情相关
const viewDialog = ref(false)
const viewingRecord = ref({
  name: '',
  size: '',
  creator: '',
  dataset_type: '',
  format: '',
  status: '',
  create_time: '',
  description: ''
})

// 搜索过滤器
const filterOptions = reactive({
  creator: '',
  name: '',
  dataset_type: null,
  dataset_status: null,
  start_date: '',
  end_date: '',
  dateVal: [] // 新增日期选择器绑定
})

// 数据集类型选项
const datasetTypeOptions = [
  { label: '文本', value: 'text' },
  { label: '图片', value: 'image' },
  { label: '视频', value: 'video' }
]



// 数据集状态选项
const statusOptions = [
  { label: '启用', value: 'enabled' },
  { label: '禁用', value: 'disabled' }
]

// 表格配置
const loading = ref(false)
const selected = ref([])
const pagination = ref({
  rowsPerPage: 5,//每页几条数据
  page: 1,
  total: 0,
})

const columns = [
  { name: 'name', align: 'center', label: '数据集名称', field: 'name', sortable: true },
  {
    name: 'size', align: 'center', label: '文件大小', field: 'size', sortable: true,
    format: val => formatFileSize(val || 0)
  },
  { name: 'samples', align: 'center', label: '样本数量', field: 'sample_count', sortable: true },
  { name: 'uploader', align: 'center', label: '上传人', field: 'creator_name' },
  { name: 'dataset_type', align: 'center', label: '数据集类型', field: 'dataset_type_display' },
  { name: 'status', align: 'center', label: '状态', field: 'dataset_status_display' },
  {
    name: 'create_time', align: 'center', label: '创建时间', field: 'created_at', sortable: true,
    format: val => date.formatDate(val, 'YYYY-MM-DD HH:mm:ss')
  },
  { name: 'operations', align: 'center', label: '操作', field: 'operations' }
]

// 数据集文件数据
const datasetFiles = ref([])

// 全量数据
const allData = ref([])

// 跳转相关
const jumpText = ref('')//跳转

// 数据集类型分布数据
const datasetTypeDistribution = ref([])

// 初始化饼图
const initPieChart = () => {
  if (!pieChart.value) return;

  // 销毁旧实例
  if (pieChartInstance) {
    pieChartInstance.dispose();
  }

  // 初始化图表
  pieChartInstance = echarts.init(pieChart.value);

  // 准备数据
  const chartData = datasetTypeDistribution.value.map(item => ({
    value: item.count,
    name: item.name,
    itemStyle: { color: item.color }
  }));

  // 配置项
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: '10%',
      top: 'center',
      textStyle: {
        color: '#fff',
        fontSize: 16,
      }
    },
    series: [
      {
        name: '数据集类型',
        type: 'pie',
        radius: '70%', // 直径占比
        center: ['50%', '50%'],
        data: chartData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          show: false
        }
      }
    ]
  };

  // 设置配置项
  pieChartInstance.setOption(option);

  // 响应式调整
  window.addEventListener('resize', () => pieChartInstance.resize());
}

// 初始化仪表盘
const initGaugeChart = () => {
  if (!gaugeChart.value) return;

  if (gaugeChartInstance) {
    gaugeChartInstance.dispose();
  }

  gaugeChartInstance = echarts.init(gaugeChart.value);

  const option = {
    series: [{
      type: 'gauge',
      progress: {
        show: false,
        width: 18,
        itemStyle: {
          color: 'auto'  // 自动匹配分段颜色
        }
      },
      axisLine: {
        lineStyle: {
          width: 18,
          color: [
            [0.3, '#165DFF'],  // 0-30% 蓝色
            [0.7, '#F7BA1E'],  // 30-70% 黄色
            [1, '#F53F3F']     // 70-100% 红色
          ]
        }
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: '#fff'
        }
      },
      splitLine: {
        show: false
      },
      axisLabel: {
        show: true,
        color: '#fff'
      },
      pointer: {
        itemStyle: {
          color: 'auto'
        }
      },
      detail: {
        valueAnimation: true,
        fontSize: 24,
        offsetCenter: [0, '70%'],
        color: '#fff',
        formatter: '{value}%'
      },
      data: [{
        value: diskUsage.value
      }]
    }]
  };

  gaugeChartInstance.setOption(option);
  window.addEventListener('resize', () => gaugeChartInstance.resize());
};

// 存储使用量数据
const diskUsage = ref(65)
const totalStorage = ref('500TB')
const usedStorage = ref('325TB')

// 弹窗控制
const uploadDialog = ref(false)
const deleteDialog = ref(false)
const fileToDelete = ref(null)
const submitting = ref(false)

// 上传相关
const uploadFiles = reactive({
  dataset_file: null,
  metadata_file: null
})

const newDataset = reactive({
  name: '',
  description: '',
  dataset_type: '',
  sample_count: 0
})

// 获取数据集文件列表
function fetchDatasetFiles() {
  loading.value = true
  console.log('开始获取数据集列表 表格数据')

  // 构建查询参数
  const params = new URLSearchParams()
  if (filterOptions.creator) params.append('creator_name', filterOptions.creator)
  if (filterOptions.name) params.append('name', filterOptions.name)
  if (filterOptions.dataset_type) params.append('dataset_type', filterOptions.dataset_type)
  if (filterOptions.dataset_status) params.append('dataset_status', filterOptions.dataset_status)
  if (filterOptions.start_date) params.append('start_date', filterOptions.start_date)
  if (filterOptions.end_date) params.append('end_date', filterOptions.end_date)

  // 添加分页参数
  params.append('page', pagination.value.page.toString())
  params.append('page_size', pagination.value.rowsPerPage.toString())

  api.get(`backend/datasets/?${params.toString()}`)
    .then(res => {
      // console.log('API返回数据:', res.data)

      if (res && Array.isArray(res.results)) {
        datasetFiles.value = res.results.map(item => {
          console.log('处理数据集项:', item)

          // 处理下载链接
          let fileUrl = item.file || '';
          if (fileUrl && !fileUrl.startsWith('http')) {
            fileUrl = import.meta.env.VITE_API + fileUrl;
          }

          return {
            id: item.id,
            name: item.name || '未命名数据集',
            description: item.description,
            size: item.size,
            sample_count: item.sample_count || 0,
            creator_name: item.creator_name || '未知用户',
            dataset_type: item.dataset_type,
            dataset_type_display: item.dataset_type_display || getDatasetTypeLabel(item.dataset_type),
            dataset_status: item.dataset_status,
            dataset_status_display: item.dataset_status_display,
            created_at: item.created_at,
            updated_at: item.updated_at,
            file: fileUrl
          }
        })
        console.log('处理后的数据集列表:', datasetFiles.value)

        // 更新总数
        if (res.count !== undefined) {
          pagination.value.total = res.count
        }
      } else {
        console.error('API返回数据格式不正确:', res.data)
        notify('获取数据格式异常', 'negative')
        datasetFiles.value = []
      }

      loading.value = false

      // 更新数据集类型分布
      updateDatasetTypeDistribution()
    })
    .catch(err => {
      console.error('获取数据集文件列表失败:', err)
      notify('获取数据集文件列表失败')
      loading.value = false
      datasetFiles.value = []
    })
}

// 获取数据集类型显示标签
function getDatasetTypeLabel(value) {
  const option = datasetTypeOptions.find(opt => opt.value === value)
  return option ? option.label : value
}

// 获取状态颜色
function getStatusColor(status) {
  if (status === '启用') {
    return 'positive' // 绿色
  } else if (status === '禁用') {
    return 'negative' // 红色
  } else {
    return 'grey' // 默认灰色
  }
}

// 首次加载获取全量数据
function getEchartsData() {
  console.log('getEchartsData 表格数据')
  api.get(`backend/datasets/?`)
    .then(res => {
      if (res && Array.isArray(res.results)) {
        allData.value = res.results.map(item => {
          return {
            id: item.id,
            name: item.name || '未命名数据集',
            description: item.description,
            size: item.size,
            sample_count: item.sample_count || 0,
            creator_name: item.creator_name || '未知用户',
            dataset_type: item.dataset_type,
            dataset_type_display: item.dataset_type_display || getDatasetTypeLabel(item.dataset_type),
            dataset_status: item.dataset_status,
            dataset_status_display: item.dataset_status_display,
            created_at: item.created_at,
            updated_at: item.updated_at,
            file: item.file
          }
        })

        console.log("数据集数据 >>>", allData.value)

        // 更新总数
        if (res.count !== undefined) {
          pagination.value.total = res.count
        }
      } else {
        console.error('全量数据获取失败', res)
        allData.value = []
      }
      // 更新数据集类型分布
      updateDatasetTypeDistribution()
    })
    .catch(err => {
      console.error('全量数据获取失败:', err)
      allData.value = []
    })
}

// 更新数据集类型分布数据
function updateDatasetTypeDistribution() {
  // 创建类型计数对象
  const typeCounts = {}
  let total = 0

  // 计算各类型数量
  allData.value.forEach(file => {
    const typeKey = file.dataset_type_display || getDatasetTypeLabel(file.dataset_type);
    if (!typeCounts[typeKey]) {
      typeCounts[typeKey] = 0
    }
    typeCounts[typeKey]++
    total++
  })

  if (total === 0) return

  // 固定的颜色映射
  const typeColorMap = {
    '文本': '#1976D2',    // 蓝色
    '图片': '#26A69A',    // 青色
    '视频': '#FFC107'     // 黄色
  }

  // 其他类型的备用颜色
  const fallbackColors = ['#795548', '#9E9E9E', '#3F51B5', '#009688', '#CDDC39', '#03A9F4', '#8BC34A', '#FF5722'];

  // 生成分布数据
  const distribution = [];
  let colorIndex = 0;

  for (const type in typeCounts) {
    const color = typeColorMap[type] || fallbackColors[colorIndex % fallbackColors.length];
    colorIndex++;

    distribution.push({
      name: type,
      percentage: Math.round((typeCounts[type] / total) * 100),
      color: color,
      count: typeCounts[type]
    });
  }

  // 按百分比从大到小排序
  distribution.sort((a, b) => b.percentage - a.percentage);

  datasetTypeDistribution.value = distribution;
  console.log("datasetTypeDistribution.value", datasetTypeDistribution.value)
  initPieChart(); // 更新图表
}

// 查看数据集详情
function viewDataset(row) {
  viewingRecord.value = {
    name: row.name || '',
    size: formatFileSize(row.size) || '',
    creator: row.creator_name || '',
    dataset_type: row.dataset_type_display || getDatasetTypeLabel(row.dataset_type) || '',
    format: row.format || '',
    status: row.dataset_status_display || row.dataset_status || '',
    create_time: row.created_at || new Date().toISOString(),
    description: row.description || ''
  }
  viewDialog.value = true
}

// 下载数据集
function downloadDataset(file) {
  if (file.file) {
    window.open(file.file)
  } else {
    notify('无法下载，文件地址不存在', 'negative')
  }
}

// 删除数据集
function confirmDelete(file) {
  fileToDelete.value = file
  deleteDialog.value = true
}

function deleteDataset() {
  if (fileToDelete.value) {
    loading.value = true

    api.delete(`backend/datasets/${fileToDelete.value.id}/`)
      .then(() => {
        const index = datasetFiles.value.findIndex(f => f.id === fileToDelete.value.id)
        if (index !== -1) {
          datasetFiles.value.splice(index, 1)
        }
        notify('删除成功', 'positive')
        fileToDelete.value = null
        loading.value = false

        // 更新数据集类型分布
        updateDatasetTypeDistribution()
      })
      .catch(err => {
        console.error('删除文件失败:', err)
        notify('删除文件失败')
        loading.value = false
      })
  }
}

// 上传数据集文件
function onSubmit() {
  if (!uploadFiles.dataset_file) {
    notify('请选择数据集文件')
    return
  }

  submitting.value = true

  const formData = new FormData()
  formData.append('name', newDataset.name)
  formData.append('description', newDataset.description || newDataset.name)
  formData.append('dataset_type', newDataset.dataset_type)
  formData.append('sample_count', newDataset.sample_count)

  if (uploadFiles.dataset_file) {
    formData.append('file', uploadFiles.dataset_file)
  }

  api.post('backend/datasets/', formData)
    .then(res => {
      const newFile = {
        id: res.data.id,
        name: res.data.name,
        size: res.data.size,
        sample_count: res.data.sample_count,
        dataset_type: res.data.dataset_type,
        dataset_type_display: res.data.dataset_type_display || getDatasetTypeLabel(res.data.dataset_type),
        dataset_status: res.data.dataset_status,
        dataset_status_display: res.data.dataset_status_display,
        created_at: res.data.created_at,
        file: res.data.file
      }

      datasetFiles.value.unshift(newFile)
      uploadDialog.value = false
      notify('上传成功', 'positive')

      // 重置表单
      resetNewDatasetForm()

      fetchDatasetFiles()

    })
    .catch(err => {
      console.error('上传失败:', err)
      notify('上传失败: ' + (err.message || '未知错误'), 'negative')
    })
    .finally(() => {
      submitting.value = false
    })
}

function resetNewDatasetForm() {
  newDataset.name = ''
  newDataset.description = ''
  newDataset.dataset_type = ''
  newDataset.sample_count = 0
  uploadFiles.dataset_file = null
  uploadFiles.metadata_file = null
}

// 导出数据集
// function exportDataset() {
//   if (selected.value.length === 0) {
//     notify('请选择要导出的数据集', 'warning')
//     return
//   }

//   const ids = selected.value.map(item => item.id).join(',')

//   // 获取数据集数据
//   api.get('backend/datasets/', {
//     params: {
//       page_size: 1000,
//       ids: ids
//     }
//   })
//     .then(response => {
//       if (!response || !response.results) {
//         notify('导出失败', 'negative')
//         return
//       }

//              // 转换为CSV格式，与表格显示保持一致
//        const headers = ['数据集名称', '文件大小', '样本数量', '上传人', '数据集类型', '状态', '创建时间']
//        const rows = response.results.map(dataset => [
//          dataset.name || '',
//          formatFileSize(dataset.size || 0),
//          dataset.sample_count || 0,
//          dataset.creator_name || '',
//          dataset.dataset_type_display || getDatasetTypeLabel(dataset.dataset_type),
//          dataset.dataset_status_display || '',
//          date.formatDate(dataset.created_at, 'YYYY-MM-DD HH:mm:ss') || ''
//       ])

//       // 创建CSV内容
//       const csvContent = [
//         headers.join(','),
//         ...rows.map(row => row.join(','))
//       ].join('\n')

//       // 创建并下载文件
//       const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
//       const link = document.createElement('a')
//       const url = URL.createObjectURL(blob)
//       link.setAttribute('href', url)
//       link.setAttribute('download', `数据集列表_${date.formatDate(new Date(), 'YYYY-MM-DD_HH-mm')}.csv`)
//       document.body.appendChild(link)
//       link.click()
//       document.body.removeChild(link)
//       URL.revokeObjectURL(url)

//       notify('导出成功', 'positive')
//     })
//     .catch(error => {
//       console.error('导出失败:', error)
//       notify('导出失败', 'negative')
//     })
// }
// 导出数据集
function exportDataset() {
  if (selected.value.length === 0) {
    notify('请选择要导出的数据集', 'warning')
    return
  }

  // 对每个选中的数据集发起导出请求
  selected.value.forEach(dataset => {
    api({
      url: `backend/datasets/${dataset.id}/export/`,
      method: 'GET',
      responseType: 'blob'
    })
      .then(response => {
        console.log('Export response:', response)

        // 检查响应本身是否是 Blob
        if (response instanceof Blob) {
          const blob = response
          console.log('Using response as blob:', {
            type: blob.type,
            size: blob.size
          })
        } else if (response.data instanceof Blob) {
          const blob = response.data
          console.log('Using response.data as blob:', {
            type: blob.type,
            size: blob.size
          })
        } else {
          console.error('Invalid response format:', response)
          throw new Error('响应格式错误')
        }

        // 使用正确的 blob 对象
        const blob = response instanceof Blob ? response : response.data

        // 根据响应类型设置文件扩展名
        const fileExt = blob.type.includes('tar') ? '.tar' :
          blob.type.includes('zip') ? '.zip' :
            '.dat'
        const filename = `dataset_${dataset.id}${fileExt}`

        // 创建下载链接
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = filename
        link.style.display = 'none'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        notify('导出成功', 'positive')
      })
      .catch(error => {
        console.error('导出失败:', error)
        if (error.response?.status === 401) {
          notify('认证失败，请重新登录', 'negative')
        } else {
          notify(`导出失败: ${error.message || '未知错误'}`, 'negative')
        }
      })
  })
}


// 跳转（支持回车键）
const goJump = () => {
  console.log("回车");
  pagination.value.page = Number(jumpText.value)
}

// 每页行数变化处理
const onRowsPerPageChange = (e) => {
  console.log(8888, e)
  pagination.value.rowsPerPage = e
  // fetchDatasetFiles()
}

// 搜索功能
function handleSearch() {
  pagination.value.page = 1 // 重置到第一页
  fetchDatasetFiles()
}

function resetSearch() {
  // 重置所有过滤条件
  Object.keys(filterOptions).forEach(key => {
    if (typeof filterOptions[key] === 'boolean') {
      filterOptions[key] = null
    } else {
      filterOptions[key] = ''
    }
  })

  pagination.value.page = 1 // 重置到第一页
  fetchDatasetFiles()
}

// 添加日期格式化函数
function formatDate(dateString) {
  if (!dateString) return ''
  return date.formatDate(dateString, 'YYYY-MM-DD HH:mm:ss')
}

// 日期选择器变化时更新过滤条件
function dateChange(val) {
  if (val && val.length === 2) {
    filterOptions.start_date = val[0]
    filterOptions.end_date = val[1]
  } else {
    filterOptions.start_date = ''
    filterOptions.end_date = ''
  }
  // handleSearch()
}

// 监听分页变化
watch(() => pagination.value.rowsPerPage, (newVal, oldVal) => {
  // 当 rowsPerPage 变化时，重置页码为 1
  pagination.value.page = 1
  fetchDatasetFiles()
})

// 保留原有的 page 监听（单独处理页码变化）
watch(() => pagination.value.page, () => {
  fetchDatasetFiles()
})

// 初始加载
onMounted(() => {
  fetchDatasetFiles()

  getEchartsData()

  // 检查URL参数，如果有openUploadDialog=true则自动打开上传数据集对话框
  if (route.query.openUploadDialog === 'true') {
    uploadDialog.value = true
  }

  // 初始化ECharts
  initPieChart()
  initGaugeChart() // 初始化仪表盘
})
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
}

.model-distribution-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.mybr {
  margin-left: 0px;
}

.text {
  width: 20px;
  text-align: center;
  line-height: 40px;
  padding: 0;
  position: relative;
  top: 8px;
}

.inputel {
  margin-left: -9px;
}

.flexReverse {
  display: flex;
  justify-content: center;
}

.flexbox {
  display: flex;
  align-items: center;
}

.labelT {
  font-size: .175rem;
  font-weight: 500;
  color: var(--q-slideText);
  line-height: .275rem;
  margin-right: .125rem;
}

.flexCenter {
  width: 3rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.positionLeft {
  position: relative;
  left: .375rem;
}

@media (max-width: 599px) {
  .col-md-7 {
    margin-bottom: 16px;
  }

  .col-md-7 .row {
    margin: 0;
  }

  .col-5 {
    padding: 0 4px;
  }

  .text {
    line-height: 40px;
  }

  .flex.justify-start {
    justify-content: flex-start;
  }

  .q-btn {
    min-width: 80px;
  }
}

::v-deep.q-field--auto-height.q-field--labeled .q-field__control-container {
  padding-top: 0;
}

::v-deep.q-field--labeled .q-field__native {
  padding-top: 0;
  padding-bottom: 0;
}

::v-deep.q-textarea.q-field--dense.q-field--labeled .q-field__control-container {
  padding: 0 !important;
}

::v-deep.q-textarea.q-field--dense.q-field--labeled .q-field__native {
  height: 100% !important;
  line-height: 36px;
}

.mt10 {
  margin-top: 10px;
}

.noPadingBot {
  padding-bottom: 0;
  padding-top: 10px;
}

.btnCon {
  display: flex;
  justify-content: space-evenly;
  padding: 10px 0px;
}

.paginationEl {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 有值或聚焦时隐藏 label */
.dynamic-label-input.q-field--with-content :deep(.q-field__label),
.dynamic-label-input.q-field--focused :deep(.q-field__label) {
  display: none;
}

.dynamic-label-input :deep(.q-field__inner) {
  border: none;
  /* 设置圆角的 必须单独取消 */
}

/* 单独设置圆角和边框色 */
.dynamic-label-input :deep(.q-field__inner) {
  border-radius: 8px;
}

/* 文字居中 */
.dynamic-label-input :deep(.q-field__native) {
  text-align: center;
}

/* 分页器 */
:deep(.q-pagination .q-btn) {
  color: white !important;
}

:deep(.q-pagination .q-btn--standard) {
  background: #396ea4 !important;
}

:deep(.q-pagination .q-btn:hover) {
  background: #396ea4 !important;
}

/* 跳转按钮 */
.custom-btn {
  width: fit-content;
  background-color: #396ea4;
  margin-left: .875rem;
  border-radius: .0625rem;
  height: 0.45rem;
}

// 输入框start
.q-field {
  font-size: .175rem;
  height: .5rem;
}

:deep(.q-field--dense .q-field__control) {
  //输入高度
  height: .5rem !important;
}

:deep(.q-field--dense .q-field__label) {
  font-size: .175rem !important;
  top: .125rem;
}

:deep(.q-field__label) {
  //label
  line-height: .25rem !important;
}

// 尾部带图标输入框
:deep(.q-field__marginal) {
  font-size: .3rem !important;
}

:deep(.q-field--labeled .q-field__native) {
  line-height: 24px !important;
}

// 输入框end
:deep(.q-field--dense .q-field__marginal) {
  height: .5rem !important;
}

// element 日期选择器
:deep(.datePicker) {
  height: 0.5rem !important;
}

:deep(.el-date-editor .el-range__icon) {
  //left icon
  font-size: .175rem;
}

:deep(.el-range-editor--large .el-range-input) {
  font-size: .175rem;
}

:deep(.el-date-editor .el-range__close-icon) {
  //close 
  font-size: .175rem;
}

:deep(.el-range-editor--large .el-range-separator) {
  //至
  font-size: .175rem;
  line-height: .5rem;
}

:deep(.q-btn__content) {
  font-size: .175rem;
}

// 按钮
:deep(.q-btn) {
  padding: .05rem .2rem;
  height: .45rem;
}

// 分页器适配
:deep(.q-pagination__middle .q-btn) {
  width: .35rem;
  height: .35rem;
}

:deep(.q-table__bottom) {
  font-size: .15rem; //文字大小
}

:deep(.q-field--dense .q-field__marginal) {
  height: .5rem !important;
}

.smallText {
  font-size: .175rem;
}

// 表格适配
:deep(.q-table__container) {
  // border: none !important;
  min-height: 7.5rem !important; //最小高度600px
  box-shadow: 0 .0125rem .0625rem #0003, 0 .025rem .025rem #00000024, 0 .0375rem .0125rem -0.025rem #0000001f;
}

:deep(.q-table__container .q-table thead th) {
  font-size: .175rem !important;
}

:deep(.q-table__container .q-table tbody td) {
  font-size: .175rem !important;
}

:deep(.q-table td) {
  padding: .0875rem .2rem !important;
}

:deep(.q-table tbody td) {
  height: .6rem !important;
}

.q-chip--dense {
  border-radius: .0625rem;
  padding: 0 .05rem;
  height: .2625rem;
  margin: .05rem;
  font-size: .175rem;
}

.blackCard {
  min-width: 5.375rem;
  background: rgba(0, 0, 0, .9) !important;
  border: none !important;
  border-radius: .0625rem !important;
  // overflow: hidden !important;

  :deep(.q-btn) {
    padding: .05rem .2rem;
    height: .45rem;
    // width: 1.rem;
  }

  :deep(.q-btn__content) {
    font-size: .175rem;
  }

  .delAvatar {
    font-size: .6rem;
  }

  .delContent {
    font-size: .2rem;
  }

  .q-field--with-bottom {
    padding-bottom: 0 !important;
  }

  .q-field__bottom {
    min-height: .25rem !important;
    padding: .1rem .15rem 0 !important;
  }

  :deep(.q-field--dense .q-field__bottom) {
    font-size: .1375rem !important;
  }

  .q-col-gutter-md>* {
    padding-left: .25rem !important;
    padding-top: .25rem !important;
  }

  .q-mt-md {
    margin-top: .5rem !important;
  }
}

.fontlabel {
  min-width: 1.5rem;
  text-align: right;
  font-size: .175rem;
}

.font10 {
  font-size: .15rem;
  margin-left: .25rem;
}

.row.labelColor {
  align-items: center;
}
</style>
