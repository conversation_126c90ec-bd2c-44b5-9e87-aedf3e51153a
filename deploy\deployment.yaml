apiVersion: apps/v1
kind: Deployment
metadata:
  name: rl-platform-fronted
  namespace: rl-platform
spec:
  replicas: 1  # 根据需要调整副本数
  selector:
    matchLabels:
      app: rl-platform-fronted
  template:
    metadata:
      labels:
        app: rl-platform-fronted
    spec:
      containers:
      - name: rl-platform-fronted
        image: {{IMAGE}}
        imagePullPolicy: Always
        ports:
        - containerPort: 80

---
apiVersion: v1
kind: Service
metadata:
  name: rl-platform-fronted
  namespace: rl-platform
spec:
  selector:
    app: rl-platform-fronted
  ports:
  - protocol: TCP
    port: 80
    targetPort: 80
    nodePort: 38002
  type: NodePort  # 根据需要调整服务类型
