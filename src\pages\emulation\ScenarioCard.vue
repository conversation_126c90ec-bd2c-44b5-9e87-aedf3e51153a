<template>
  <div class="flex items-center q-mt-lg q-mb-md sticky">
    <div class="flex title">
      <div class="bg-primary text-black text-bold fs-18 q-px-lg q-py-sm skew-both">想定管理</div>
    </div>
    <ScenarioColor v-model="filterColor" @update:modelValue="selected = null" class="q-ml-lg" />
    <q-btn
      @click="onAdd"
      label="添加想定"
      icon="add"
      color="primary"
      text-color="black"
      padding="8px 40px"
      class="skew-both q-ml-auto"
    />
    <q-separator color="grey-9" size="3px" class="rounded-borders q-mx-xl" inset vertical />
    <q-btn @click="refresh" flat round>
      <SvgRefresh />
      <q-tooltip>刷新</q-tooltip>
    </q-btn>

    <q-btn @click="onEdit" :disable="!selected" class="q-ml-lg" flat round>
      <SvgEdit />
      <q-tooltip>编辑</q-tooltip>
    </q-btn>

    <q-btn @click="onDelete" :disable="!selected" class="q-ml-lg" flat round>
      <SvgDelete />
      <q-tooltip>删除</q-tooltip>
    </q-btn>
  </div>

  <div class="row q-col-gutter-xs">
    <div v-for="(item, idx) in filterList" :key="idx" class="col-3 col-xl-2">
      <div @click="onSelect(item)" :class="{ selected: selected?.id == item.id }" class="cursor-pointer item">
        <div class="q-pa-md content">
          <div class="fs-16 text-bold ellipsis">
            {{ item.name }}
            <q-tooltip>{{ item.name }}</q-tooltip>
          </div>
          <div class="q-mt-sm q-px-sm q-pt-xs desc" :style="{ backgroundColor: colors[item.color] }">
            <div v-if="item.desc" class="text-black ellipsis">
              {{ item.desc }}
              <q-tooltip>{{ item.desc }}</q-tooltip>
            </div>
          </div>
          <div class="q-mt-sm flex items-center">
            <SvgScenarioUser />
            <span class="q-ml-sm">{{ item.creator }}</span>
          </div>
          <div class="q-mt-sm flex items-center">
            <SvgClock />
            <span class="q-ml-sm">{{ item.createTime }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <q-inner-loading :showing="loading" />
</template>

<script setup>
import { ref, watch, computed, toRefs } from 'vue'
import { date } from 'quasar'
import { api } from 'boot/axios'
import { usePlugin } from 'composables/plugin'
import ScenarioColor from './ScenarioColor.vue'
import ScenarioDialog from './ScenarioDialog.vue'
import { colors } from 'assets/const'
import SvgRefresh from 'assets/images/svg/refresh.svg'
import SvgEdit from 'assets/images/svg/edit.svg'
import SvgDelete from 'assets/images/svg/delete.svg'
import SvgScenarioUser from 'assets/images/svg/scenario-user.svg'
import SvgClock from 'assets/images/svg/clock.svg'

const { customComponentDialog, dialog, notify } = usePlugin()

const props = defineProps({
  emulator: Object,
})

const loading = ref(false)
const list = ref([])
const selected = ref(null)
const filterColor = ref(null)
const filterList = computed(() => {
  return filterColor.value != null ? list.value.filter(item => item.color == filterColor.value) : list.value
})

watch(() => props.emulator, (newEmulator) => {
    console.log('Emulator changed:', newEmulator)
    getList()
});

getList()

function getList() {

  loading.value = true

  const { emulatorId } = props.emulator

  console.log(props.emulator)
  console.log(emulatorId)

  api
    .get(`backend/scenarios/?emulator_id=${emulatorId}`)
    .then(res => {
      list.value = (res?.results ?? []).map(item => {
        const { id, name, file, config, color, desc, creater_name: creator, create_time: createTime } = item
        return {
          id,
          name,
          file,
          config,
          color,
          desc,
          creator,
          createTime: date.formatDate(createTime, 'YYYY-MM-DD HH:mm:ss'),
        }
      })
      loading.value = false
    })
    .catch(() => {
      loading.value = false
    })
}

function refresh() {
  selected.value = null
  filterColor.value = null
  getList()
}

function onSelect(item) {
  selected.value = selected.value?.id == item.id ? null : item
}

function onAdd() {
  customComponentDialog(ScenarioDialog, {
    emulator: props.emulator,
  }).onOk(refresh)
}

function onEdit() {
  if (!selected.value) {
    return
  }

  customComponentDialog(ScenarioDialog, {
    params: selected.value,
  }).onOk(refresh)
}

function onDelete() {
  if (!selected.value) {
    return
  }

  dialog('确认删除吗？').onOk(() => {
    api
      .delete(`backend/scenarios/${selected.value.id}/`)
      .then(() => {
        notify('删除成功', 'positive')
        refresh()
      })
      .catch(() => {
        notify('删除失败')
      })
  })
}
</script>

<style lang="scss" scoped>
.title::before {
  content: '';
  width: 15px;
  clip-path: polygon(0 0, 100% 0, 5px 100%, 0 100%);
  transform: translateX(5px);
  background-color: rgba(0, 0, 0, 0.6);
}

.item {
  padding: 5px;
  border: 3px solid transparent;

  &:hover {
    border-color: rgba(0, 0, 0, 0.2);
  }

  &.selected {
    border-color: #000;
  }

  .content {
    background-color: #eaeaea;
    box-shadow: 0px 12px 20px 0px rgba(0, 0, 0, 0.08);
  }

  .desc {
    height: 37px;
    margin-left: -10px;
    margin-right: -10px;
    clip-path: polygon(0 0, 100% 0, 100% 30px, 30% 30px, calc(30% - 10px) 100%, 0 100%);
  }
}

.body--dark {
  .title::before {
    background-color: #fff;
  }

  .item {
    &:hover {
      border-color: rgba(255, 255, 255, 0.2);
    }

    &.selected {
      border-color: #fff;
    }

    .content {
      background-color: rgba(59, 67, 74, 0.7);
      box-shadow: none;
    }
  }
}
</style>
