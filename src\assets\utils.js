function convertSecondsToHMS(seconds) {
  seconds = parseInt(seconds)

  const hours = Math.floor(seconds / (60 * 60))
  seconds %= 60 * 60

  const minutes = Math.floor(seconds / 60)
  seconds %= 60

  let result = ''

  result += hours ? `${hours}时` : ''
  result += hours || minutes ? `${minutes}分` : ''
  result += seconds ? `${seconds}秒` : ''

  return result
}

function formatFileSize(size, fixed = 0) {
  size = Number(size)

  const KB = 1024,
    MB = KB * 1024,
    GB = MB * 1024,
    TB = GB * 1024

  if (size >= 0 && size < KB) {
    return `${size}B`
  } else if (size >= KB && size < MB) {
    return `${(size / KB).toFixed(fixed)}KB`
  } else if (size >= MB && size < GB) {
    return `${(size / MB).toFixed(fixed)}MB`
  } else if (size >= GB && size < TB) {
    return `${(size / GB).toFixed(fixed)}GB`
  } else if (size >= TB) {
    return `${(size / TB).toFixed(fixed)}TB`
  } else {
    return ''
  }
}

export { convertSecondsToHMS, formatFileSize }
