<template>
  <div class="flex q-gutter-x-sm">
    <div
      v-for="(color, idx) in colors"
      @click="onSelectColor(idx)"
      :key="idx"
      :class="{ selected: selectedColor == idx }"
      class="item"
    >
      <div class="full-height" :style="{ backgroundColor: color }"></div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { colors } from 'assets/const'

const props = defineProps({
  modelValue: Number,
  required: Boolean,
})
const emit = defineEmits(['update:modelValue'])

const selectedColor = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  },
})

function onSelectColor(colorIndex) {
  if (selectedColor.value == colorIndex) {
    if (!props.required) {
      selectedColor.value = null
    }
  } else {
    selectedColor.value = colorIndex
  }
}
</script>

<style lang="scss" scoped>
.item {
  width: 30px;
  height: 40px;
  padding: 2px;
  border: 3px solid transparent;
  transform: skewX(-20deg);

  &:hover {
    border-color: rgba(0, 0, 0, 0.2);
  }

  &.selected {
    border-color: #000;
  }
}

.body--dark {
  .item {
    &:hover {
      border-color: rgba(255, 255, 255, 0.2);
    }

    &.selected {
      border-color: #fff;
    }
  }
}
</style>
