import { ref, onMounted, onUnmounted, toValue } from 'vue'
import * as echarts from 'echarts/core'
import { <PERSON><PERSON><PERSON>, <PERSON>Chart } from 'echarts/charts'
import { CanvasRenderer } from 'echarts/renderers'
import { TooltipComponent, GridComponent, VisualMapComponent } from 'echarts/components'

echarts.use([<PERSON><PERSON>hart, LineChart, CanvasRenderer, TooltipComponent, GridComponent, VisualMapComponent])

export function useEchart(options) {
  let chartInstance = null

  const chartDom = ref(null)

  const observer = new ResizeObserver(handleWindowResize)

  onMounted(() => {
    chartInstance = echarts.init(chartDom.value)
    observer.observe(chartDom.value)
    chartInstance.setOption(toValue(options))
  })

  onUnmounted(() => {
    observer.disconnect()
  })

  function handleWindowResize() {
    chartInstance && chartInstance.resize()
  }

  return { chartDom }
}
