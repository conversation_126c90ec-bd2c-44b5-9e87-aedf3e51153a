.chart {
  width: 100%;
  > div,
  canvas {
    max-width: 100%;
  }
}

.sticky {
  position: sticky;
  top: 0;
  z-index: 1;
}

.flex-grow {
  flex-grow: 1;
}

.skew-both {
  clip-path: polygon(10px 0, 100% 0, calc(100% - 10px) 100%, 0 100%);
}

.dialog-card {
  background: rgba(0, 0, 0, 0.08);
  box-shadow: -5px 0px 20px 0px rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(15px);
  max-width: 90vw !important;

  .title {
    color: $primary;
    font-size: 22px;
    line-height: 1;
    font-weight: bold;
    display: flex;

    &::before {
      content: '';
      display: inline-block;
      width: 4px;
      margin-right: 16px;
      background-color: $primary;
    }
  }

  .q-card__actions {
    padding: 16px;
    .q-btn--rectangle {
      padding: 0 30px;
    }
  }
}

.require::after {
  content: '*';
  color: $negative;
  margin-left: 2px;
}

.markup-table {
  max-height: 400px;
  border-radius: 0;

  thead {
    position: sticky;
    top: 0;
    z-index: 1;

    tr {
      background-color: #ffffff;
    }
  }
}
