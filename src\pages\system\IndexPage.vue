<template>
  <div class="q-pa-md">
    <div class="text-h5 q-mb-md">系统管理</div>

    <div class="row q-col-gutter-md">
      <div class="col-12 col-md-4">
        <q-card class="system-card cursor-pointer" @click="$router.push('/system/users')">
          <q-card-section class="bg-primary text-white">
            <div class="text-h6">用户管理</div>
          </q-card-section>
          <q-card-section>
            <div class="text-body1">管理用户账号、权限分配、账户状态</div>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-12 col-md-4">
        <q-card class="system-card">
          <q-card-section class="bg-secondary text-white">
            <div class="text-h6">角色管理</div>
          </q-card-section>
          <q-card-section>
            <div class="text-body1">设置系统角色和相应的权限</div>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-12 col-md-4">
        <q-card class="system-card">
          <q-card-section class="bg-accent text-white">
            <div class="text-h6">日志管理</div>
          </q-card-section>
          <q-card-section>
            <div class="text-body1">查看系统操作日志和用户行为</div>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-12 col-md-4">
        <q-card class="system-card">
          <q-card-section class="bg-positive text-white">
            <div class="text-h6">系统配置</div>
          </q-card-section>
          <q-card-section>
            <div class="text-body1">管理系统全局配置和参数设置</div>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-12 col-md-4">
        <q-card class="system-card">
          <q-card-section class="bg-info text-white">
            <div class="text-h6">备份恢复</div>
          </q-card-section>
          <q-card-section>
            <div class="text-body1">系统数据备份和恢复功能</div>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-12 col-md-4">
        <q-card class="system-card cursor-pointer" @click="$router.push('/system/simulation')">
          <q-card-section class="bg-purple text-white">
            <div class="text-h6">仿真管理</div>
          </q-card-section>
          <q-card-section>
            <div class="text-body1">管理仿真环境和仿真文件存储</div>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-12 col-md-4">
        <q-card class="system-card cursor-pointer" @click="$router.push('/system/model')">
          <q-card-section class="bg-teal text-white">
            <div class="text-h6">模型管理</div>
          </q-card-section>
          <q-card-section>
            <div class="text-body1">管理模型文件和模型存储</div>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-12 col-md-4">
        <q-card class="system-card cursor-pointer" @click="$router.push('/system/dataset-management')">
          <q-card-section class="bg-deep-orange text-white">
            <div class="text-h6">数据集管理</div>
          </q-card-section>
          <q-card-section>
            <div class="text-body1">管理数据集文件和存储</div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </div>
</template>

<script setup>
// No additional logic needed for this page
</script>

<style scoped>
.system-card {
  height: 100%;
  transition: all 0.3s;
}

.system-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}
</style>
