<template>
  <q-list>
    <template v-for="(link, idx) in links" :key="idx">
      <!-- 带子菜单的导航项 -->
      <template v-if="link.children && !collapsed">
        <q-expansion-item
          expand-separator
          active-class="active-parent"
          class="nav-item"
          header-class="nav-header"
          :default-opened="link.defaultOpened"
          @click="onExpansionItemClick"
        >
          <template v-slot:header>
            <q-item-section avatar>
              <component :is="link.icon" width="24" height="24" />
            </q-item-section>
            <q-item-section>
              <span class="text-huangyou fs-28 customTitle">{{ link.name }}</span>
            </q-item-section>
          </template>

          <q-list class="q-pl-md">
            <q-item
              v-for="(child, childIdx) in link.children"
              :key="childIdx"
              :to="child.to"
              active-class="active"
              class="sub-nav-link"
              manual-focus
            >
              <q-item-section>
                <span class="text-huangyou fs-20 customSubTitle">{{ child.name }}</span>
              </q-item-section>
            </q-item>
          </q-list>
        </q-expansion-item>
      </template>

      <!-- 折叠时显示的主菜单项带子菜单的简化版 -->
      <template v-else-if="link.children && collapsed">
        <q-item
          v-for="(child, childIdx) in link.children"
          :key="childIdx"
          :to="child.to"
          active-class="active"
          class="collapsed-nav-item"
          manual-focus
          
        >
          <q-item-section avatar>
            <component :is="link.icon" width="24" height="24" />
          </q-item-section>
        </q-item>
      </template>

      <!-- 无子菜单的导航项 -->
      <q-item
        v-else
        :to="link.to"
        active-class="active"
        :class="['flex items-center q-my-sm bg-transparent', collapsed ? 'collapsed-nav-item' : 'nav-link']"
        manual-focus
       
      >
        <component :is="link.icon" :class="collapsed ? '' : 'q-mr-sm'" width="24" height="24" />
        <span v-if="!collapsed" class="text-huangyou fs-28">{{ link.name }}</span>
      </q-item>
    </template>
  </q-list>
</template>

<script setup>
import { ref } from 'vue'
import overviewIcon from 'assets/images/svg/nav-overview.svg'
import emulationIcon from 'assets/images/svg/nav-emulation.svg'
import trainingIcon from 'assets/images/svg/nav-training.svg'
import modelIcon from 'assets/images/svg/nav-model.svg'
import storageIcon from 'assets/images/svg/nav-storage.svg'
import algorithmIcon from 'assets/images/svg/nav-algorithm.svg'
import systemIcon from 'assets/images/svg/nav-system.svg'
import aiModelIcon from 'assets/images/svg/nav-ai-model.svg'

// 定义props
const props = defineProps({
  collapsed: {
    type: Boolean,
    default: false
  }
});

// 添加一个展开项点击事件处理
function onExpansionItemClick(e) {
  // 阻止冒泡，防止触发路由导航
  e.stopPropagation();
}

const links = [
  {
    name: '智能算法',
    to: '/intelligent-algorithm',
    icon: algorithmIcon,
  },
  {
    name: '全局概览',
    to: '/overview',
    icon: overviewIcon,
  },
  // {
  //   name: '仿真管理',
  //   to: '/emulation',
  //   icon: emulationIcon,
  // },
  // {
  //   name: '训练管理',
  //   to: '/training',
  //   icon: trainingIcon,
  // },
  // {
  //   name: '算法管理',
  //   to: '/algorithm',
  //   icon: algorithmIcon,
  // },
  // {
  //   name: '模型管理',
  //   to: '/model',
  //   icon: modelIcon,
  // },
  // {
  //   name: '存储管理',
  //   to: '/storage',
  //   icon: storageIcon,
  // },
  {
    name: '模型算法',
    icon: aiModelIcon,
    defaultOpened: true,
    children: [
      {
        name: '智能模型',
        to: '/ai-model/intelligent',
      },
      {
        name: '强化学习',
        to: '/ai-model/reinforcement',
      },
      {
        name: '深度学习',
        to: '/ai-model/deep-learning',
      }
    ]
  },
  {
    name: '系统管理',
    icon: systemIcon,
    defaultOpened: true,    // 默认展开子菜单
    children: [
      {
        name: '用户管理',
        to: '/system/users',
      },
      {
        name: '仿真管理',
        to: '/system/simulation',
      },
      {
        name: '模型管理',
        to: '/system/model',
      },
      {
        name: '数据集管理',
        to: '/system/dataset',
      }
    ]
  }
]
</script>

<style lang="scss" scoped>
.nav-link {
  height: 50px;
  clip-path: polygon(0 0, calc(100% - 10px) 0, 100% 50%, calc(100% - 10px) 100%, 0 100%);
  color:  var(--q-slideText);

  &.active,
  &:hover {
    color: var(--q-slideText) !important;
    background-color: var(--q-primary) !important;
  }
}


.customTitle{
  color: var(--q-slideText);
}
.customSubTitle{
  color: var(--q-slideText);
}

.collapsed-nav-item {
  width: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 ;
  height: 50px;
  box-sizing: border-box;
  color: $slideText;
  position: relative;
  &.active,
  &:hover {
    color: $slideText !important;
    background-color: var(--q-primary) !important;

  }

  .q-item__section--avatar,
  .q-item__section--main,
  .q-item__section--side{
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    padding: 0;
  }

  svg,img,.q-icon{
    display: block;
    margin: auto;
    font-size: 28px;
  }

}

.q-expansion-item--expanded{
  .q-item__section--avatar{
    min-width: 32px;
  }
  .q-item__section--side{
    padding-right: 0;
  }
}


.body--dark .nav-link,
.body--dark .collapsed-nav-item {
  color: #fff;
}

.nav-header {
  height: 50px;
  clip-path: polygon(0 0, calc(100% - 10px) 0, 100% 50%, calc(100% - 10px) 100%, 0 100%);
}

.active-parent {
  background-color: rgba(var(--q-primary), 0.1);

  .q-item__section--main {
    color:var(--q-primary);
  }
}

::v-deep.q-expansion-item--collapsed{
  .q-item__section--side{
    padding: 0;
  }
  .q-item__section--avatar{
    min-width: 32px;
  }
}


.sub-nav-link {
    clip-path: polygon(0 0, calc(100% - 10px) 0, 100% 50%, calc(100% - 10px) 100%, 0 100%);
  &.active {
    color: var(--q-slideText) !important;
    background-color: var(--q-primary) !important;
    clip-path: polygon(0 0, calc(100% - 10px) 0, 100% 50%, calc(100% - 10px) 100%, 0 100%);
  }

  &:hover{
    color: var(--q-slideText) !important;
    background-color:var(--q-primary) !important;
    clip-path: polygon(0 0, calc(100% - 10px) 0, 100% 50%, calc(100% - 10px) 100%, 0 100%);
  }

}
</style>
