import { useQuasar } from 'quasar'

export function usePlugin() {
  const { dialog: QDialog, notify: QNotify, localStorage, dark } = useQuasar()

  function dialog(message, opt = {}) {
    return QDialog({
      title: '请确认',
      message,
      focus: 'none',
      seamless: true,
      class: 'dialog-card',
      cancel: {
        label: '取消',
        color: 'grey-9',
      },
      ok: {
        label: '确定',
        color: 'primary',
        textColor: 'black',
      },
      ...opt,
    })
  }

  function customComponentDialog(component, componentProps) {
    return QDialog({
      component,
      componentProps,
    })
  }

  function notify(message, type = 'negative') {
    QNotify({
      message,
      type,
      textColor: 'black',
    })
  }

  return {
    dialog,
    customComponentDialog,
    notify,
    localStorage,
    dark,
  }
}
