<template>
  <TableCard :columns="columns" ref="table" :params="{ model_id: modelId }" api-url="backend/deductions/">
    <template #top>
      <q-btn
        @click="deduce"
        label="推演"
        icon="add"
        color="primary"
        text-color="black"
        padding="8px 40px"
        class="skew-both q-ml-auto q-mb-md"
      />
    </template>

    <template #body-cell-status="props">
      <q-td :props="props">
        <span :class="`text-${deductionStatus[props.value]?.color}`" class="fs-16">
          {{ deductionStatus[props.value]?.label }}
        </span>
      </q-td>
    </template>

    <template #body-cell-action="props">
      <q-td :props="props">
        <q-btn
          @click="openURL(props.row.file)"
          :label="props.row.status==3?'下载':'推理中...'" color="primary"
          :disable="props.row.status!=3" flat dense />
        <q-btn @click="onDelete(props.row.id)" label="删除" color="primary" class="q-ml-md" flat dense />
      </q-td>
    </template>
  </TableCard>
</template>

<script setup>
import { ref } from 'vue'
import { date, openURL } from 'quasar'
import { api } from 'boot/axios'
import { deductionStatus } from 'assets/const'
import { usePlugin } from 'composables/plugin'
import TableCard from 'components/TableCard.vue'

const props = defineProps({
  modelId: String,
})

const { dialog, notify } = usePlugin()

const columns = [
  {
    name: 'name',
    field: 'name',
    label: '回放文件',
    align: 'center',
  },
  {
    name: 'createTime',
    field: 'create_time',
    format: val => date.formatDate(val, 'YYYY-MM-DD HH:mm:ss'),
    label: '生成时间',
    align: 'center',
  },
  {
    name: 'status',
    field: 'status',
    label: '状态',
    align: 'center',
    slot: true,
  },
  {
    name: 'action',
    label: '操作栏',
    align: 'center',
    slot: true,
  },
]

const table = ref(null)
const deduceLoading = ref(false)

function deduce() {
  deduceLoading.value = true
  api
    .post('backend/models/deduction/', {
      model_id: props.modelId,
    })
    .then(() => {
      deduceLoading.value = false
      notify('推演创建成功', 'positive')
      table.value.getRows()
    })
    .catch(() => {
      deduceLoading.value = false
      notify('推演失败')
    })
}

function onDelete(id) {
  dialog('确认删除吗？').onOk(() => {
    api
      .delete(`backend/deductions/${id}/`)
      .then(() => {
        notify('删除成功', 'positive')
        table.value.getRows()
      })
      .catch(() => {
        notify('删除失败')
      })
  })
}
</script>
