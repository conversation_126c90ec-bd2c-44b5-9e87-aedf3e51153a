<template>
  <q-dialog ref="dialogRef" @hide="onHide" position="right" full-height>
    <q-card class="dialog-card column">
      <q-card-section class="flex items-center">
        <div class="title">日志</div>
        <q-btn v-close-popup icon="close" class="q-ml-auto" round flat dense />
      </q-card-section>
      <q-card-section class="col full-width">
        <div ref="terminalRef" class="full-height" />
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref } from 'vue'
import { api } from 'boot/axios'
import { useDialogPluginComponent } from 'quasar'
import { usePlugin } from 'composables/plugin'
import { Terminal } from 'xterm'
import { FitAddon } from '@xterm/addon-fit'
import 'xterm/css/xterm.css'

const props = defineProps({
  wsUrl: String,
  id: String,
})

defineEmits([...useDialogPluginComponent.emits])

const { dialogRef, onDialogHide } = useDialogPluginComponent()
const { dark } = usePlugin()

const loading = ref(false)
const detail = ref({})

const logWsUrl = ref('')

getDetail()

function getDetail() {
  loading.value = true
  api
    .get(`backend/tasks/${props.id}/`)
    .then(res => {
      loading.value = false
      const {
        running_status: runningStatus,
      } = res.data

      detail.value = {
        runningStatus,
      }

      const learner_pod = runningStatus.filter(str => str.includes('-learner'));
      console.log(learner_pod)

      const containerNames = learner_pod[0].match(/(.*?)-learner/);
      if (!containerNames) {
        notify('获取容器名称失败')
        return
      }

      const containerName = containerNames[0]
      console.log(containerName)
      console.log(learner_pod)

      logWsUrl.value = `${import.meta.env.VITE_WS || 'ws://'+location.host}/ws/log/?container_name=${containerName}&pod_name=${learner_pod}`
    })
    .catch(() => {
      loading.value = false
    })
    .finally(() => {
      initTerminal()
    })
}

const terminalRef = ref(null)

const terminal = new Terminal({
  cursorBlink: true,
  cursorStyle: 'underline',
  cursorInactiveStyle: 'none',
  fontSize: '14',
  lineHeight: 1.8,
})
const fitAddon = new FitAddon()
const observer = new ResizeObserver(() => fitAddon.fit())

terminal.loadAddon(fitAddon)

let webSocket

function initTerminal() {
  terminal.open(terminalRef.value)
  observer.observe(terminalRef.value)
  terminal.focus()

  webSocket = new WebSocket(logWsUrl.value)

  webSocket.onopen = () => {
    console.log('ws connected')
  }

  webSocket.onclose = () => {
    console.log('ws closed')
  }

  webSocket.onmessage = ev => {
    const data = JSON.parse(ev.data)

    if (data?.message) {
      data.message
        .split('\n')
        .filter(item => item)
        .forEach(item => {
          terminal.writeln('  ' + item)
        })
    }
  }
}


function onHide() {
  webSocket && webSocket.close()
  onDialogHide()
}

</script>

<style lang="scss" scoped>
.dialog-card {
  width: 1000px;
  padding: 80px 100px;
}
</style>
