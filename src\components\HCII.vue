<!--
 * @Author: <PERSON>zc
 * @Date: 2025-07-30 17:43:20
 * @LastEditors: Szc
 * @LastEditTime: 2025-08-02 10:52:51
 * @Description: 
-->
<template>
    <q-btn class="fixed" @click="floatBtnClick">
        <img src="../assets/images/rjjh.png" alt="">
        <q-tooltip anchor="center left" self="center right" style="background-color: rgba(0,0,0,.7) !important;
        color: white;border-radius: .0625rem;
        font-size:.225rem;">
            人机智能交互
        </q-tooltip>
    </q-btn>
</template>

<script setup>
import { useAiStore } from '../stores/aiImStore'

const AiStore = useAiStore()

const floatBtnClick = () => {
    AiStore.toggleAiWindow()
}
</script>

<style lang="scss" scoped>
.fixed {
    width: 50px;
    height: 50px;
    text-align: center;
    // box-shadow: 0 .05rem .35rem 0 rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-self: center;
    background: transparent !important;
    transition: all 0.6s ease;

    &:hover {
        // box-shadow: 0 4px 28px 0 rgba(74, 198, 255, 0.7) ;
        box-shadow: 0 .05rem .35rem 0 rgb(0, 180, 251) !important;
    }

    &::before {
        content: "";
        position: absolute;
        inset: 0;
        border-radius: 50%;
        background: #25ccf7;
        transition: all 0.6s ease;
        transform: scale(0.8);
        z-index: -1;
    }

    &:hover::before {
        box-shadow: 0 0 14px 2px #25ccf7;
        transform: scale(1.05);
    }

    img {
        width: 50px;
        height: 50px;
    }
}
</style>