
body.body--light {
  background: url(assets/images/global-bg-light.png) no-repeat center / cover fixed #fff;

  // 设计稿版本
  background: url(assets/images/bg.png) no-repeat center / cover fixed #fff; // 第一版设计稿
  // background: url(assets/images/global-bg-dark.png) no-repeat center / cover fixed #fff;
  background: rgba(24,52,72); // 第二版设计稿
}


body.body--dark {
  background: url(assets/images/global-bg-dark.png) no-repeat center / cover fixed #000;

  .q-table__container {
    .q-table {
      thead {
        background-color: #484848;
      }

      tbody {
        tr:nth-child(odd) {
          background-color: #000;
        }

        tr:nth-child(even) {
          background-color: #232323;
        }
      }
    }
  }

  .dialog-card {
    background-color: rgba(0, 0, 0, 0.6);
    box-shadow: -5px 0px 20px 0px rgba(0, 0, 0, 0.6);

    .q-card__actions {
      .q-btn--rectangle {
        border: 1px solid rgba(255, 255, 255, 0.6);
      }
    }
  }
}