<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide">
    <q-card class="dialog-card">
      <q-card-section class="flex items-center">
        <div class="text-h6">启动推演</div>
        <q-space />
        <q-btn v-close-popup icon="close" flat round dense />
      </q-card-section>

      <q-card-section>
        <div class="text-body1 q-mb-md">确认要启动推演吗？</div>
        <div class="text-grey-7">
          模型ID：{{ modelId }}
        </div>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn v-close-popup label="取消" color="grey-7" flat />
        <q-btn
          :loading="loading"
          label="确认"
          color="primary"
          @click="onSubmit"
        />
      </q-card-actions>

      <q-inner-loading :showing="loading">
        <q-spinner-dots size="40px" color="primary" />
      </q-inner-loading>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref } from 'vue'
import { useDialogPluginComponent } from 'quasar'
import { api } from 'boot/axios'
import { usePlugin } from 'composables/plugin'

const props = defineProps({
  modelId: {
    type: [String, Number],
    required: true
  }
})

const { dialogRef, onDialogHide, onDialogOK } = useDialogPluginComponent()
const { notify } = usePlugin()
const loading = ref(false)

async function onSubmit() {
  try {
    loading.value = true
    await api.post(`backend/models/${props.modelId}/simulate/`)
    notify('推演启动成功', 'positive')
    onDialogOK()
  } catch (error) {
    notify('推演启动成功', 'positive')
    loading.value = false
  } finally {
    loading.value = false
    // onDialogOK()
  }
}
</script>

<style lang="scss" scoped>
.dialog-card {
  min-width: 400px;
}
</style>
