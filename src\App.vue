<template>
  <router-view />
</template>

<script setup>
import { usePlugin } from 'composables/plugin'

const { dark, localStorage } = usePlugin()

dark.set(!!localStorage.getItem('theme'))
</script>

<style lang="scss">
// $
* {
  font-family: "Microsoft YaHei"; //字体设置
}

// 有值或聚焦时隐藏 label
.noLabel {

  &.q-field--with-content .q-field__label,
  &.q-field--focused .q-field__label {
    display: none;
  }
}

// 有值或聚焦时隐藏 label
.q-field--with-content .q-field__label,
.q-field--focused .q-field__label {
  display: none !important;
}

// 输入框label
.q-field--labeled .q-field__native {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

// 输入框底部hint值
.q-field__bottom {
  color: $slideText !important;
}

// 文本域label
.q-textarea.q-field--labeled .q-field__control-container {
  padding-top: 0 !important;
}

.labelColor {
  color: $slideText !important;
  font-family: "Microsoft YaHei";
  font-size: .25rem;
}

// 分割线 
.q-separator {
  background: #3c66a4;
}

.customAdd {
  width: fit-content;
  background: $inputBG;
  color: $slideText !important;
}

.el-input__wrapper {
  border-radius: 0 !important;
  background: var(--q-inputBG) !important;
  border: .0125rem solid transparent !important;
  border-image: linear-gradient(to right, #64778a, #293f64) 1 !important;
  // 不带圆角边框👆   带圆角👇
  border: .0125rem solid transparent !important;
  border-radius: .0625rem !important;
  background-clip: padding-box, border-box !important;
  background-origin: padding-box, border-box !important;
  background-image: linear-gradient(to right, $inputBG, $inputBG), linear-gradient(to right, #64778a, #293f64) !important;
}

.el-date-editor.el-input__wrapper,
.el-date-editor.el-input__wrapper.is-active {
  box-shadow: none !important;
}

// 日期选择器 文字颜色
.el-date-editor .el-range-input {
  color: $slideText !important;
}

// 日期选择器 "至" 颜色
.el-range-editor--large .el-range-separator {
  color: $slideText !important;
}

// 圆角
.roundBox {
  // border: 1px solid #b2d7f1;
  border-radius: .0625rem !important;
}

.grayBox {
  // background: $gray !important;
  color: $slideText !important;
  border: .0125rem solid $slideText !important;
}

.q-btn--outline:before {
  border: none !important;
}

// 表格中的 标签对齐方式
.q-td.text-center .q-badge {
  margin: 0 auto;
}

.labelT {
  font-size: .175rem;
  font-weight: 500;
  color: $slideText;
  line-height: .275rem;
  margin-right: .125rem;
}

.flexbox {
  display: flex;
  align-items: center;
}

.customText {
  font-size: .175rem !important;
}

.btn50 {
  height: .625rem !important;
}

// Y轴滚动条消失
.q-dialog__inner--minimized>div {
  // max-height: none;
}

.q-table--horizontal-separator tbody tr:not(:last-child)>td {
  border-bottom-width: 0;
}

.q-dialog__inner>.q-card>.q-card__actions .q-btn--rectangle {
  width: 1.1rem;
}

.minW88 {
  min-width: 1.1rem;
}

// 表格
:deep(.q-table__container) {
  border: none !important;
  min-height: 7.5rem !important; //最小高度600px
}

:deep(.q-table__container .q-table thead th) {
  font-size: .175rem !important;
}

:deep(.q-table__container .q-table tbody td) {
  font-size: .175rem !important;
}

:deep(.q-table td) {
  padding: .0875rem .2rem !important;
}

:deep(.q-table tbody td) {
  height: .6rem !important;
}

.closeBtn {
  width: 0.45rem;
}

// 开发笔记本太小，特殊处理
.unsetHeight {
  @media screen and (max-width: 1536px) {
    height: unset !important;
  }
}

.unsetHeight.q-pagination .q-pagination__middle .q-btn {
  @media screen and (max-width: 1536px) {
    height: unset !important;
  }
}
.unsetHeight .q-field__control {
  @media screen and (max-width: 1536px) {
    display: flex;
    align-items: center;
  }
}
.unsetHeight .q-field__control-container{
  @media screen and (max-width: 1536px) {
    height: unset;
  }
}

.el-popper__arrow{
  display: none !important;
}

.resizing * {
  user-select: none; /* 禁止选中文字 */
  -webkit-user-select: none; /* Safari/Chrome */
}
</style>