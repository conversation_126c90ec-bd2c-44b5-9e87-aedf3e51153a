<template>
  <div class="q-pa-md" style="padding-left: 0;padding-right: 0;">
    <q-card flat bordered class="q-mb-md">
      <q-card-section class="borderBot">
        <div class="labelColor">服务器运维管理</div>
      </q-card-section>
      <q-card-section class="q-pt-none noPading">
        <div class="row">
          <div class="col">
            <!-- 设备筛选表单 -->
            <div class="row q-col-gutter-md items-center pading16" style="padding-right: 40px !important;">
              <div class="flexbox customFlexBox">
                <div class="labelT">设备名称</div>
                <q-input style="flex: 1;" v-model="filter.deviceName" :label="filter.deviceName ? '' : '请输入'" outlined
                  dense />
              </div>
              <div class="flexbox customFlexBox">
                <div class="labelT">设备地址</div>
                <q-input style="flex: 1;" v-model="filter.deviceAddress" :label="filter.deviceAddress ? '' : '请输入'"
                  outlined dense />
              </div>
              <div class="flexbox customFlexBox">
                <div class="labelT">设备状态</div>
                <q-select style="flex: 1;" v-model="filter.deviceStatus" :options="deviceStatusOptions"
                  :label="filter.deviceStatus ? '' : '请选择'" outlined dense emit-value map-options clearable />
              </div>
              <div class="flexbox" style="justify-content: start;flex:0 0 auto;">
                <q-btn class="roundBox" style="margin-right: 15px;" color="primary" label="查询" @click="onSearch" />
                <q-btn style="margin-left: auto;" class="roundBox grayBox" outline color="primary" label="重置"
                  @click="onReset" />
              </div>
            </div>
            <!-- 表格 -->
            <q-card-section>
              <q-table :rows="tableData" :columns="columns" row-key="id" v-model:pagination="pagination.value"
                :rows-per-page-options="[5, 10, 15]" hide-selection-column>

                <template v-slot:body-cell-status="props">
                  <q-td class="text-center">
                    <q-badge class="custom-badge" :color="getStatusColor(props.row.status)">{{ props.row.status
                    }}</q-badge>
                  </q-td>
                </template>

                <template v-slot:body-cell-actions="props">
                  <q-td style="display: flex; justify-content: center;align-items: center;">
                    <q-btn flat dense size="sm" color="negative" label="下架" @click="openWindow('down', props.row)" />
                    <el-dropdown>
                      <q-btn flat class="drop">
                        <div class="el-dropdown-link">
                          ···
                        </div>
                      </q-btn>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item @click="openWindow('on', props.row)">开机</el-dropdown-item>
                          <el-dropdown-item @click="openWindow('off', props.row)">关机</el-dropdown-item>
                          <el-dropdown-item @click="openWindow('reboot', props.row)">重启</el-dropdown-item>
                          <el-dropdown-item @click="openWindow('edit', props.row)">编辑</el-dropdown-item>
                          <el-dropdown-item @click="openWindow('config', props.row)">运算卡配置</el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>



                  </q-td>
                </template>

                <template v-slot:bottom>
                  <div class="row items-center full-width paginationEl">
                    <div style="margin-right: 20px;">总计 {{ tableData.length }} 条</div>
                    <div>
                      <q-select class="unsetHeight" v-model="pagination.rowsPerPage" :options="[5, 10, 15]" outlined
                        dense options-dense emit-value map-options style="min-width: 100px"
                        @update:model-value="onRowsPerPageChange">
                        <template v-slot:selected>
                          {{ pagination.rowsPerPage }}/页
                        </template>
                      </q-select>
                    </div>
                    <q-pagination class="unsetHeight" style="margin-left:1rem;margin-right: 1rem;"
                      v-model="pagination.page" :max="Math.ceil(tableData.length / pagination.rowsPerPage)"
                      :max-pages="5" boundary-numbers direction-links />
                    <div class="flexbox">
                      <div style="margin-right:.125rem;">跳到</div>
                      <div class="roundBox">
                        <q-input class="dynamic-label-input" v-model="jumpText" style="width:.625rem;" dense
                          @keyup.enter="goJump">
                        </q-input>
                      </div>
                      <q-btn class="custom-btn" label="跳转" @click="goJump" />
                    </div>
                  </div>
                </template>
              </q-table>
            </q-card-section>
          </div>
        </div>
      </q-card-section>
    </q-card>
  </div>

  <!-- 开机、关机、重启弹窗 -->
  <q-dialog v-model="allWindows.operation" persistent>
    <q-card class="blackCard">
      <q-card-section class="row items-center">
        <q-avatar icon="error" text-color="warning" />
        <span class="q-ml-sm delContent">计算设备{{ currentDevice }}是否确认</span>
        <span v-if="currentOperation == 'on'">开机？</span>
        <span v-if="currentOperation == 'off'">关机？</span>
        <span v-if="currentOperation == 'reboot'">重启？</span>
        <span v-if="currentOperation == 'down'">下架？</span>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn flat label="取消" color="primary" v-close-popup />
        <q-btn flat class="bg-primary" label="确定" color="primary" v-close-popup />
      </q-card-actions>
    </q-card>
  </q-dialog>

  <!-- 编辑 -->
  <q-dialog v-model="allWindows.edit" persistent>
    <q-card class="blackCard">
      <q-card-section class="row items-center pad12">
        <div class="labelColor">编辑设备</div>
        <q-space />
        <q-btn class="closeBtn" icon="close" flat round dense v-close-popup />
      </q-card-section>

      <q-card-section class="row items-center">
        <q-form style="width: 100%;">

          <div class="flexItem">
            <div class="labelColor labelWidth">训练设备名称</div>
            <q-input class="flex1" v-model="editForm.name" :label="editForm.name ? '' : '请输入'"
              :rules="[val => !!val || '请输入设备名']" outlined dense />
          </div>

          <div class="flexItem">
            <div class="labelColor labelWidth">训练设备地址</div>
            <q-input class="flex1" v-model="editForm.address" :label="editForm.address ? '' : '请输入'"
              :rules="[val => !!val || '请输入设备地址']" outlined dense />
          </div>

          <div class="flexItem">
            <div class="labelColor labelWidth">运算卡型号</div>
            <q-select class="flex1" v-model="editForm.gpu" :options="modelOption" :label="editForm.gpu ? '' : '运算卡型号'"
              :rules="[val => !!val || '请选择运算卡型号']" outlined dense emit-value map-options />
          </div>

          <div class="flexItem">
            <div class="labelColor labelWidth">代理设备</div>
            <q-select class="flex1" v-model="editForm.agent" :options="equOption" :label="editForm.agent ? '' : '代理设备'"
              :rules="[val => !!val || '请选择代理设备']" outlined dense emit-value map-options />
          </div>

          <div class="flexItem">
            <div class="labelColor labelWidth">操作系统</div>
            <q-select class="flex1" v-model="editForm.system" :options="systemOption"
              :label="editForm.system ? '' : '操作系统'" :rules="[val => !!val || '请选择操作系统']" outlined dense emit-value
              map-options />
          </div>

        </q-form>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn flat label="取消" color="primary" v-close-popup />
        <q-btn flat class="bg-primary" label="确定" color="primary" v-close-popup />
      </q-card-actions>
    </q-card>
  </q-dialog>

  <!-- 运算卡配置 -->
  <q-dialog v-model="allWindows.config" persistent>
    <q-card class="blackCard">
      <q-card-section class="row items-center pad12">
        <div class="labelColor">运算卡配置</div>
        <q-space />
        <q-btn class="closeBtn" icon="close" flat round dense v-close-popup />
      </q-card-section>

      <q-card-section class="row items-center" style="flex-direction: column;">
        <div>
          <q-table :rows="configRows" :columns="configColumns" row-key="id" v-model:pagination="configPagination"
            hide-pagination selection="multiple" v-model:selected="selectedRows">

            <template v-slot:body-cell-status="props">
              <q-td class="text-center">
                <q-select class="cus-select" v-model="props.row.status" :options="statusOptions" dense outlined
                  style="min-width: 80px;" emit-value map-options />
              </q-td>
            </template>

          </q-table>
          <q-pagination v-model="configPagination.page" color="grey-8" :max="pagesNumber" size="sm"
            style="justify-content: center;margin-top: .25rem;" />
        </div>

      </q-card-section>

      <q-card-actions align="right">
        <q-btn flat label="取消" color="primary" v-close-popup />
        <q-btn flat class="bg-primary" label="确定" color="primary" v-close-popup />
      </q-card-actions>
    </q-card>
  </q-dialog>

</template>
<script setup>
import { reactive, ref, computed } from 'vue'

// 表单筛选项
const filter = reactive({
  deviceName: '',
  deviceAddress: '',
  deviceStatus: null
})

const deviceStatusOptions = [
  { label: '在线', value: '在线' },
  { label: '离线', value: '离线' },
  { label: '维护', value: '维护' }
]

const configColumns = [
  { name: 'deviceNo', label: '设备序号', field: 'deviceNo', align: 'center', sortable: true },
  { name: 'gpuModel', label: '运算卡型号', field: 'gpuModel', align: 'center' },
  { name: 'nodeIp', label: '计算节点IP', field: 'nodeIp', align: 'center' },
  { name: 'gpuMode', label: '运算卡模式', field: 'gpuMode', align: 'center' },
  { name: 'migCount', label: '单卡复用/MIG数量', field: 'migCount', align: 'center' },
  { name: 'status', label: '状态', field: 'status', align: 'center' }
]

const configRows = [
  {
    id: 1,
    deviceNo: 'GPU-001',
    gpuModel: 'NVIDIA RTX 3090',
    nodeIp: '*************',
    gpuMode: '计算模式',
    migCount: 4,
    status: '正常'
  },
  {
    id: 2,
    deviceNo: 'GPU-002',
    gpuModel: 'NVIDIA A100',
    nodeIp: '*************',
    gpuMode: 'MIG模式',
    migCount: 7,
    status: '正常'
  },
  {
    id: 3,
    deviceNo: 'GPU-003',
    gpuModel: 'NVIDIA RTX 4080',
    nodeIp: '*************',
    gpuMode: '计算模式',
    migCount: 2,
    status: '故障'
  },
  {
    id: 4,
    deviceNo: 'GPU-004',
    gpuModel: 'NVIDIA T4',
    nodeIp: '*************',
    gpuMode: '计算模式',
    migCount: 1,
    status: '正常'
  },
  {
    id: 5,
    deviceNo: 'GPU-005',
    gpuModel: 'NVIDIA A40',
    nodeIp: '*************',
    gpuMode: 'MIG模式',
    migCount: 6,
    status: '正常'
  },
  {
    id: 6,
    deviceNo: 'GPU-006',
    gpuModel: 'NVIDIA RTX 3080',
    nodeIp: '*************',
    gpuMode: '计算模式',
    migCount: 3,
    status: '故障'
  },
  {
    id: 7,
    deviceNo: 'GPU-007',
    gpuModel: 'NVIDIA A30',
    nodeIp: '*************',
    gpuMode: 'MIG模式',
    migCount: 4,
    status: '正常'
  },
  {
    id: 8,
    deviceNo: 'GPU-008',
    gpuModel: 'NVIDIA RTX 3070',
    nodeIp: '*************',
    gpuMode: '计算模式',
    migCount: 2,
    status: '正常'
  },
  {
    id: 9,
    deviceNo: 'GPU-009',
    gpuModel: 'NVIDIA RTX 3060',
    nodeIp: '*************',
    gpuMode: '计算模式',
    migCount: 1,
    status: '正常'
  },
  {
    id: 10,
    deviceNo: 'GPU-010',
    gpuModel: 'NVIDIA RTX 3050',
    nodeIp: '*************',
    gpuMode: '计算模式',
    migCount: 1,
    status: '正常'
  },
  {
    id: 11,
    deviceNo: 'GPU-011',
    gpuModel: 'NVIDIA RTX 3050',
    nodeIp: '*************',
    gpuMode: '计算模式',
    migCount: 1,
    status: '正常'
  }
]

const configPagination = ref({
  sortBy: 'fat',
  descending: false,
  page: 1,
  rowsPerPage: 10
  // rowsNumber: total
})

const pagesNumber = computed(() => Math.ceil(configRows.length / configPagination.value.rowsPerPage))

// 运算卡配置相关变量
const selectedRows = ref([])
const statusOptions = [
  { label: '正常', value: '正常' },
  { label: '故障', value: '故障' }
]

// 表格字段
const columns = [
  { name: 'name', label: '训练设备名称', field: 'name', align: 'center' },
  { name: 'address', label: '训练设备地址', field: 'address', align: 'center' },
  { name: 'agent', label: '代理设备', field: 'agent', align: 'center' },
  { name: 'status', label: '设备状态', field: 'status', align: 'center' },
  { name: 'platform', label: '平台架构', field: 'platform', align: 'center' },
  { name: 'system', label: '操作系统', field: 'system', align: 'center' }, // 新增列
  { name: 'gpu', label: '运算卡型号/数量', field: 'gpu', align: 'center' },
  { name: 'instance', label: '运行实例数', field: 'instance', align: 'center' },
  { name: 'version', label: '服务版本', field: 'version', align: 'center' },
  { name: 'creator', label: '创建人', field: 'creator', align: 'center' },
  { name: 'created_at', label: '创建时间', field: 'created_at', align: 'center' },
  { name: 'actions', label: '操作', field: 'actions', align: 'center' },
]

// 假数据（12条）
const tableData = ref([
  {
    id: 1,
    name: '设备A',
    address: '************',
    agent: '代理A',
    status: '在线',
    platform: 'x86_64',
    system: 'Ubuntu 20.04', // 新增字段
    gpu: 'NVIDIA RTX 3090/4',
    instance: 3,
    version: 'v1.2.0',
    creator: '张三',
    created_at: '2024-05-01 10:00:00'
  },
  {
    id: 2,
    name: '设备B',
    address: '************',
    agent: '代理B',
    status: '离线',
    platform: 'ARM',
    system: 'CentOS 7', // 新增字段
    gpu: 'NVIDIA A100/2',
    instance: 1,
    version: 'v1.1.0',
    creator: '李四',
    created_at: '2024-05-02 11:30:00'
  },
  {
    id: 3,
    name: '设备C',
    address: '************',
    agent: '代理C',
    status: '维护',
    platform: 'x86_64',
    system: 'Windows Server 2019', // 新增字段
    gpu: 'NVIDIA T4/8',
    instance: 2,
    version: 'v1.0.5',
    creator: '王五',
    created_at: '2024-05-03 09:15:00'
  },
  {
    id: 4,
    name: '设备D',
    address: '************',
    agent: '代理D',
    status: '在线',
    platform: 'x86_64',
    system: 'Debian 11', // 新增字段
    gpu: 'NVIDIA RTX 3080/2',
    instance: 4,
    version: 'v1.3.0',
    creator: '赵六',
    created_at: '2024-05-04 14:20:00'
  },
  {
    id: 5,
    name: '设备E',
    address: '************',
    agent: '代理E',
    status: '离线',
    platform: 'ARM',
    system: 'RHEL 8', // 新增字段
    gpu: 'NVIDIA A40/1',
    instance: 2,
    version: 'v1.0.9',
    creator: '孙七',
    created_at: '2024-05-05 16:45:00'
  },
  {
    id: 6,
    name: '设备F',
    address: '************',
    agent: '代理F',
    status: '维护',
    platform: 'x86_64',
    system: 'Ubuntu 22.04', // 新增字段
    gpu: 'NVIDIA T4/4',
    instance: 1,
    version: 'v1.0.8',
    creator: '周八',
    created_at: '2024-05-06 08:30:00'
  },
  {
    id: 7,
    name: '设备G',
    address: '************',
    agent: '代理G',
    status: '在线',
    platform: 'x86_64',
    system: 'CentOS Stream 8', // 新增字段
    gpu: 'NVIDIA RTX 3070/2',
    instance: 2,
    version: 'v1.2.1',
    creator: '吴九',
    created_at: '2024-05-07 12:10:00'
  },
  {
    id: 8,
    name: '设备H',
    address: '************',
    agent: '代理H',
    status: '离线',
    platform: 'ARM',
    system: 'Windows 10', // 新增字段
    gpu: 'NVIDIA A30/2',
    instance: 3,
    version: 'v1.1.2',
    creator: '郑十',
    created_at: '2024-05-08 13:55:00'
  },
  {
    id: 9,
    name: '设备I',
    address: '************',
    agent: '代理I',
    status: '维护',
    platform: 'x86_64',
    system: 'Fedora 35', // 新增字段
    gpu: 'NVIDIA T4/2',
    instance: 1,
    version: 'v1.0.7',
    creator: '钱十一',
    created_at: '2024-05-09 15:40:00'
  },
  {
    id: 10,
    name: '设备J',
    address: '************',
    agent: '代理J',
    status: '在线',
    platform: 'x86_64',
    system: 'OpenSUSE Leap 15.3', // 新增字段
    gpu: 'NVIDIA RTX 3060/1',
    instance: 2,
    version: 'v1.2.2',
    creator: '孙十二',
    created_at: '2024-05-10 17:25:00'
  },
  {
    id: 11,
    name: '设备K',
    address: '************',
    agent: '代理K',
    status: '离线',
    platform: 'ARM',
    system: 'macOS Big Sur', // 新增字段
    gpu: 'NVIDIA A10/1',
    instance: 1,
    version: 'v1.1.3',
    creator: '李十三',
    created_at: '2024-05-11 19:00:00'
  },
  {
    id: 12,
    name: '设备L',
    address: '************',
    agent: '代理L',
    status: '维护',
    platform: 'x86_64',
    system: 'Kali Linux 2022.1', // 新增字段
    gpu: 'NVIDIA T4/6',
    instance: 3,
    version: 'v1.0.6',
    creator: '周十四',
    created_at: '2024-05-12 20:45:00'
  },
])

const pagination = ref({
  rowsPerPage: 5,
  page: 1,
})

const jumpText = ref('')

const editForm = ref({
  name: '',
  address: '',
  gpu: '',//运算卡型号
  agent: '',//代理设备
  system: '',//操作系统
})

const systemOption = ref([
  { label: 'Ubuntu 20.04', value: 'Ubuntu 20.04' },
  { label: 'CentOS 7', value: 'CentOS 7' },
  { label: 'Windows Server 2019', value: 'Windows Server 2019' },
  { label: 'Debian 11', value: 'Debian 11' },
  { label: 'RHEL 8', value: 'RHEL 8' },
  { label: 'Ubuntu 22.04', value: 'Ubuntu 22.04' },
  { label: 'CentOS Stream 8', value: 'CentOS Stream 8' },
  { label: 'Windows 10', value: 'Windows 10' },
  { label: 'Fedora 35', value: 'Fedora 35' },
  { label: 'OpenSUSE Leap 15.3', value: 'OpenSUSE Leap 15.3' },
  { label: 'macOS Big Sur', value: 'macOS Big Sur' },
  { label: 'Kali Linux 2022.1', value: 'Kali Linux 2022.1' }
]);

const modelOption = ref([
  { label: 'NVIDIA RTX 3060', value: 'NVIDIA RTX 3060' },
  { label: 'NVIDIA RTX 4080', value: 'NVIDIA RTX 4080' },
])

const equOption = ref([
  { label: '代理A', value: '代理A' },
  { label: '代理B', value: '代理B' },
])

const currentOperation = ref(null); // 当前操作类型
const currentDevice = ref(null) // 当前设备

const allWindows = ref({
  operation: false,
  edit: false,
  config: false,
})


const openWindow = (type, data) => {
  console.log("data >>", data.name)
  currentOperation.value = type; // 记录操作类型
  currentDevice.value = data.name// 记录设备名称
  switch (type) {
    case 'edit':
      console.log('编辑');
      editForm.value = JSON.parse(JSON.stringify(data));
      allWindows.value.edit = true;
      break;
    case 'config':
      console.log('运算卡配置');
      allWindows.value.config = true;
      break;
    case 'on':
    case 'off':
    case 'reboot':
    case 'down':
      allWindows.value.operation = true; // 显示操作确认弹窗
      break;
    default:
      console.warn('未知操作类型:', type);
  }
  console.log(allWindows.value)
}

const getStatusColor = (status) => {
  if (status === '在线') return 'positive'
  if (status === '离线') return 'grey'
  if (status === '维护') return 'warning'
  return 'primary'
}

const onRowsPerPageChange = (e) => {
  pagination.value.rowsPerPage = e
  pagination.value.page = 1
  console.log('tableData', tableData.value)
}

const goJump = () => {
  const targetPage = Number(jumpText.value)
  const maxPage = Math.ceil(tableData.value.length / pagination.value.rowsPerPage)
  if (targetPage > 0 && targetPage <= maxPage) {
    pagination.value.page = targetPage
  }
}

const onSearch = () => {
  // 这里只做静态筛选演示
  // 实际开发可根据 filter 过滤 tableData
}

const onReset = () => {
  filter.deviceName = ''
  filter.deviceAddress = ''
  filter.deviceStatus = null
}
</script>

<style lang="scss" scoped>
.q-page {
  background: #f5f7fa;
}

::v-deep.q-field--auto-height.q-field--labeled .q-field__control-container {
  padding-top: 0;
}

::v-deep.q-field--labeled .q-field__native {
  padding-top: 0;
  padding-bottom: 0;
}

::v-deep.q-placeholder {
  background: pink !important;
}

.flexbox {
  display: flex;
  align-items: center;
}

.labelT {
  font-size: .175rem;
  font-weight: 500;
  color: var(--q-slideText);
  line-height: .275rem;
  margin-right: .125rem;
}

.flexCenter {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}

.paginationEl {
  display: flex;
  align-items: center;
  justify-content: center;
}


.custom-btn {
  width: fit-content;
  background-color: #396ea4;
  margin-left: .875rem;
  border-radius: .0625rem;
}

/* 有值或聚焦时隐藏 label */
.dynamic-label-input.q-field--with-content :deep(.q-field__label),
.dynamic-label-input.q-field--focused :deep(.q-field__label) {
  display: none;
}

.dynamic-label-input :deep(.q-field__inner) {
  border: none;
  /* 设置圆角的 必须单独取消 */
}

/* 单独设置圆角和边框色 */
.dynamic-label-input :deep(.q-field__inner) {
  border-radius: .1rem;
}

/* 文字居中 */
.dynamic-label-input :deep(.q-field__native) {
  text-align: center;
}

/* 分页器 */
:deep(.q-pagination .q-btn) {
  color: white !important;
}

:deep(.q-pagination .q-btn--standard) {
  background: #396ea4 !important;
}

:deep(.q-pagination .q-btn:hover) {
  background: #396ea4 !important;
}

:deep(.q-table__container) {
  border: none !important;
  min-height: 7.5rem !important; //最小高度600px
}

.borderBot {
  border-bottom: 1px solid #2a3f63 !important;
}

.noBorder {
  border: none !important;
}

.noPading {
  padding: 0px !important;
}

.pading16 {
  padding: 16px !important;
}

.leftMenu {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 5%;
    right: 0;
    width: 1px;
    height: 90%;
    background: linear-gradient(180deg, #87a6be, #3e68a4)
  }
}

// 菜单展开箭头为纯白色
:deep(.q-expansion-item__toggle-icon) {
  color: #fff !important;
}

// 二级菜单左侧缩进28px
:deep(.q-expansion-item .q-item) {
  // padding-left: 28px !important;
  // background: rgba(0,89,130,.15) !important;
  color: #fff !important;
}

// 选中状态背景色为#ffa363，文字纯白
:deep(.q-expansion-item__container .q-item.active),
:deep(.q-expansion-item__container .q-item.active) {
  background: rgba(255, 163, 99, .8) !important;
  color: #fff !important;
}

// hover时背景色可与选中一致或略浅
:deep(.q-expansion-item__container .q-item:hover) {
  background: rgba(255, 163, 99, .6) !important;
  color: #fff !important;
}

.sub-nav-link {
  padding-left: 28px !important;
  background: rgba(0, 89, 130, .15) !important;
  color: #fff !important;
}

.sub-nav-link.active {
  background: rgba(255, 163, 99, .8) !important;
  color: #fff !important;
}

.sub-nav-link:hover {
  background: rgba(255, 163, 99, .6) !important;
  color: #fff !important;
}

// 一级菜单（部门/单位）无论收起还是展开，背景色始终为透明
:deep(.q-expansion-item),
:deep(.q-expansion-item--expanded),
:deep(.q-expansion-item.nav-item),
:deep(.nav-header) {
  background: transparent !important; // 一级菜单背景透明
}

.customFlexBox {
  // width: 28%;
  flex: 1;
}

.blackCard {
  min-width: 5.375rem;
  max-width: 15rem;
  background: rgba(0, 0, 0, .9) !important;
  border: none !important;
  border-radius: .0625rem !important;
  // overflow: hidden !important;
}

.q-field--with-bottom {
  padding-bottom: 0px !important;
}

.flex.inline {
  display: block;
  text-align: center;
}

// 用户
.greyBox {
  width: 1.1rem;
  line-height: .4rem;
  border-radius: .0625rem !important;
  background: #bdbdbd !important;
  color: white;
  font-size: 1em;
}

// 普管
.organgeBox {
  width: 1.1rem;
  line-height: .4rem;
  border-radius: .0625rem !important;
  background: #ffa363 !important;
  color: white;
  font-size: 1em;
}

// 超管
.purpleBox {
  width: 1.1rem;
  line-height: .4rem;
  border-radius: .0625rem !important;
  color: white;
  font-size: 1em;
  background: #ffb640 !important;
}

.chip {
  width: 50px;
  display: flex;
  justify-content: center;
}

:deep(.chip .q-chip__content) {
  flex: 0 0 auto !important;
}

.flexItem {
  display: flex;
  align-items: center;
  margin-bottom: .25rem;
}

.flexItemEnd {
  display: flex;
}

.labelWidth {
  width: 1.25rem;
  position: relative;
  font-size: .175rem;

  &::before {
    position: absolute;
    content: '*';
    left: -0.1rem;
    top: 0;
    color: red;
  }
}

.labelNormal {
  width: 1.25rem;
}

.flex1 {
  flex: 1;
}

// 默认状态radio
:deep(.q-radio__inner) {
  color: white !important;
}

// 选中状态radio
:deep(.q-radio__inner--truthy) {
  color: orange !important;
}

// 按钮组
:deep(.q-btn-group) {
  border: 1px solid rgba(255, 255, 255, .5);
  border-radius: 3px;
}

:deep(.q-btn-group .q-btn) {
  background: $inputBG;
  background-image: none !important;
}

:deep(.q-btn-group .bg-primary) {
  background: #ffa363 !important;
}

.avatar-uploader .avatar {
  width: 88px;
  height: 88px;
  display: block;
}

.avatar-uploader .el-upload {
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  background: $inputBG;
  font-size: 28px;
  width: 88px;
  height: 88px;
  text-align: center;
  border: 1px solid #8c939d;
  border-radius: 5px;
}

.imgEl {
  width: 24px;
  height: 24px;
  margin-right: .0625rem;
}

.custom-tooltip {
  background: rgba(0, 0, 0, 1) !important;
  color: rgba(255, 255, 255, 0.9);
  border-radius: 5px !important;
  font-size: 20px !important;
}

.edit {
  background: url("../../assets/images/edit.png")no-repeat;
}

.edit:hover {
  background: url("../../assets/images/edit-active.png")no-repeat !important;
}

.user {
  background: url("../../assets/images/user.png")no-repeat;

  &:hover {
    background: url("../../assets/images/user-active.png")no-repeat !important;
  }
}

.refresh {
  background: url("../../assets/images/refresh.png")no-repeat;

  &:hover {
    background: url("../../assets/images/refresh-active.png") no-repeat !important;
  }
}

.delete {
  background: url("../../assets/images/delete.png")no-repeat;
}

.pad12 {
  padding: .15rem;
  border-bottom: .0125rem solid $slideText !important;
}

// 弹窗 确定按钮
.blueBtn {
  background-color: #396ea4;
}

.mb12 {
  margin-bottom: 20px;
}



// 部门选择器样式
.dept-option-item {
  display: flex;
  align-items: center;
  padding: 8px 0;

  .dept-expand-icon {
    margin-right: 8px;
    font-size: 16px;
    color: #000 !important;
  }

  .dept-label {
    flex: 1;
    color: #000 !important;
    font-size: 14px;
    font-weight: 500;
  }

  .dept-check-icon {
    margin-left: 8px;
    font-size: 16px;
  }
}

.dept-child-item {
  padding-left: 24px !important;
  background: #fff !important; //default

  &:hover {
    background: #ddd !important;
  }

  // 子菜单选中
  &.dept-child-selected {
    background: #e4eaf1 !important;
  }
}

.dept-child-option {
  display: flex;
  align-items: center;
  padding: 6px 0;

  .dept-child-label {
    flex: 1;
    color: #000 !important;
    font-size: 13px;
  }

  .dept-check-icon {
    margin-left: 8px;
    font-size: 14px;
  }
}

// 下拉菜单样式
:deep(.q-select__dropdown) {
  background: rgba(0, 0, 0, 0.9) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 8px !important;

  .q-item {
    color: rgba(255, 255, 255, 0.9) !important;

    &:hover {
      background-color: rgba(255, 163, 99, 0.1) !important;
    }

    &.q-item--active {
      background-color: rgba(255, 163, 99, 0.2) !important;
      color: #ffa363 !important;
    }
  }

  // 确保所有文字都是白色
  .q-item-section {
    color: rgba(255, 255, 255, 0.9) !important;
  }

  // 选项文字颜色
  .q-item__label {
    color: rgba(255, 255, 255, 0.9) !important;
  }
}

// 样式：展开icon旋转
.dept-option-item .dept-expand-icon.rotated {
  transform: rotate(180deg);
  transition: transform 0.2s;
}

// 输入框start
.q-field {
  font-size: .175rem;
  height: .5rem;
}

:deep(.q-field--dense .q-field__control) {
  //输入高度
  height: .5rem !important;
}

:deep(.q-field--dense .q-field__label) {
  font-size: .175rem !important;
  top: .125rem;
}

:deep(.q-field__label) {
  line-height: .25rem !important;
  font-size: .2rem;
}

.userNote {
  :deep(.q-field__label) {
    top: .1rem !important;
  }
}

// 输入框end

:deep(.q-table__bottom) {
  font-size: .15rem;
}

:deep(.q-btn__content) {
  font-size: .175rem;
}

:deep(.q-btn) {
  padding: .05rem .2rem;
  height: .45rem;
}

:deep(.q-btn .q-icon) {
  font-size: .3rem !important;
}

:deep(.q-field--dense .q-field__marginal) {
  height: .5rem !important;
}

:deep(.q-item__label) {
  font-size: .2rem !important;
}

:deep(.q-item__section--main) {
  font-size: .2rem !important;
}

:deep(.q-table__container .q-table thead th) {
  font-size: .175rem !important;
}

:deep(.q-table__container .q-table tbody td) {
  font-size: .175rem !important;
}

:deep(.q-table td) {
  padding: .0875rem .2rem !important;
}

:deep(.q-table tbody td) {
  height: .6rem !important;
}


.blackCard {

  // 表单提示项
  :deep(.q-field--dense .q-field__bottom) {
    min-height: .25rem !important;
    font-size: .1375rem !important;

  }

  :deep(.q-gutter-md > *) {
    margin-top: .25rem;
    margin-left: .25rem;
  }
}


:deep(.q-pagination__middle .q-btn) {
  width: .35rem;
  height: .35rem;
}

// 取消hover时引发的quasar和element plus兼容性问题(出现白色外框)
:deep(.el-dropdown .el-dropdown-link:focus-visible) {
  outline: none !important;
}

.drop {
  width: .5625rem;
  height: unset;
  padding: 0;
}

.el-dropdown {
  display: flex;
  width: 100%;
  height: .45rem;
  align-items: center;
}

.el-dropdown-link {
  display: block;
  width: 100%;
  height: 100%;
  line-height: .45rem;
  color: white;
  font-size: .3rem;
}

.el-dropdown-menu {
  background: rgba(0, 0, 0, .9) !important;
}

:deep(.el-dropdown-menu__item) {
  color: $slideText !important;
}

:deep(.el-dropdown-menu__item:hover) {
  background-color: rgba(255, 163, 99, 0.5) !important;
}

.custom-badge {
  width: 1.1rem;
  line-height: .4rem;
  border-radius: .0625rem !important;
  color: white;
  font-size: 1em;
}

.q-avatar {
  font-size: .6rem;
}

.cus-select {
  @media screen and (max-width: 1536px) {
    height: unset !important;

    :deep(.q-field__control) {
      height: unset !important;
      align-items: center;
    }
  }
}
</style>
