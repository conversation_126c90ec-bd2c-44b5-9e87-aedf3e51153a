<template>
  <q-separator color="primary" size="2px" class="sticky" />

  <IndicatorCard :indicator="indicator" class="q-mt-md" />

  <div class="row q-mt-md">
    <TrainingCard class="col-6" />
    <StorageCard v-if="saving"  class="q-ml-md col" />
  </div>

  <UsageCard :resource="resource" class="q-mt-md" />

  <q-inner-loading :showing="loading" />
</template>

<script setup>
import { useOverview } from 'composables/overview'
import IndicatorCard from './IndicatorCard.vue'
import TrainingCard from './TrainingCard.vue'
import StorageCard from './StorageCard.vue'
import UsageCard from './UsageCard.vue'

const { loading, indicator, resource, saving } = useOverview()
console.log("saving",saving)
</script>
