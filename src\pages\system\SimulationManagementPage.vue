<template>
  <div class="q-pa-md">
    <!-- <div class="text-h5 q-mb-md labelColor">仿真管理</div> -->

    <div class="row q-col-gutter-lg">
      <!-- 左侧：仿真文件管理 -->
      <div class="col-12 col-md-8">
        <q-card flat bordered class="q-mb-md">
          <q-card-section style="padding: 0;">
          </q-card-section>

          <q-card-section>
            <div class="row q-col-gutter-md">
              <div class="col-md-4 col-sm-6 col-12 flexbox">
                <div class="labelT">上传人</div>
                <q-input style="flex: 1;" outlined dense v-model="filterOptions.creator"
                  :label="filterOptions.creator ? '' : '创建人'" clearable>
                  <template v-slot:append>
                    <q-icon name="person" />
                  </template>
                </q-input>
              </div>

              <div class="col-md-4 col-sm-6 col-12 flexbox">
                <div class="labelT">上传时间</div>
                <!-- element plus 时间选择器 -->
                <el-date-picker class="datePicker" style="flex: 1;" size="large" v-model="filterOptions.dateVal"
                  type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                  value-format="YYYY-MM-DD" />
              </div>

              <div class="col-md-4 col-sm-6 col-12 flexbox">
                <div class="labelT">仿真名称</div>
                <q-input style="flex: 1;" outlined dense v-model="filterOptions.name"
                  :label="filterOptions.name ? '' : '请输入'" clearable>
                  <template v-slot:append>
                    <q-icon name="search" />
                  </template>
                </q-input>
              </div>

              <!-- <div class="col-md-3 col-sm-6 col-12">
                <q-select outlined dense v-model="filterOptions.engine_type" :options="engineOptions"
                  :label="filterOptions.engine_type ? '' : '引擎类型'" emit-value map-options clearable>
                  <template v-slot:append>
                    <q-icon name="category" />
                  </template>
                </q-select>
              </div> -->

              <!-- <div class="col-md-3 col-sm-6 col-12">
                <q-select outlined dense v-model="filterOptions.need_render" :options="renderOptions" :label="filterOptions.need_render
                  != null ? '' : '是否需要渲染'" emit-value map-options clearable>
                  <template v-slot:append>
                    <q-icon name="visibility" />
                  </template>
                </q-select>
              </div> -->

              <!-- <div class="col-md-7 col-12">
                <div class="row q-col-gutter-sm">
                  <div class="col-5">
                    <q-input outlined dense v-model="filterOptions.start_date"
                      :label="filterOptions.start_date ? '' : '开始日期'" type="date" clearable />
                  </div>
                  <div class="text">至</div>
                  <div class="col-5 inputel">
                    <q-input outlined dense v-model="filterOptions.end_date" :label="filterOptions.end_date ? '' : '结束日期'"
                      type="date" clearable />
                  </div>
                </div>
              </div> -->

              <div class="col-12" style="display: flex;justify-content: center;">
                <div class="flexCenter">
                  <q-btn color="primary" label="查询" class="q-mr-sm roundBox" @click="handleSearch" />
                  <q-btn class="grayBox roundBox" outline color="primary" label="重置" @click="resetSearch" />
                </div>
              </div>

            </div>
          </q-card-section>
        </q-card>

        <q-table flat bordered :rows="simulationFiles" :columns="columns" row-key="id" selection="multiple"
          v-model:selected="selected" :pagination="pagination.value" :loading="loading" :rows-per-page-options="[0]"
          hide-selection-column>
          <template v-slot:body-cell-is_required="props">
            <q-td :props="props">
              <q-badge :class="props.row.is_required ? 'yes' : 'grayBox roundBox ohNo'"
                :label="props.row.is_required ? '是' : '否'" />
            </q-td>
          </template>

          <template v-slot:body-cell-desc="props">
            <q-td :props="props">
              <div class="ellipsis" style="max-width: 200px;">
                {{ props.row.desc || '-' }}
                <q-tooltip v-if="props.row.desc">{{ props.row.desc }}</q-tooltip>
              </div>
            </q-td>
          </template>

          <template v-slot:body-cell-operations="props">
            <q-td :props="props">
              <q-btn class="customText" flat dense size="sm" color="primary" label="查看" @click="viewFile(props.row)" />
              <q-btn class="customText" flat dense size="sm" color="negative" label="删除"
                @click="confirmDelete(props.row)" />
            </q-td>
          </template>

          <template v-slot:bottom>
            <div class="row items-center  full-width paginationEl">
              <div style="margin-right: .25rem;">总计 {{ pagination.total }} 条</div>

              <div>
                <q-select class="unsetHeight" v-model="pagination.rowsPerPage" :options="[5, 10]" outlined dense
                  options-dense emit-value map-options @update:model-value="onRowsPerPageChange"
                  style="min-width: 1.25rem">
                  <template v-slot:selected>
                    {{ pagination.rowsPerPage }}/页
                  </template>
                </q-select>
              </div>

              <q-pagination class="unsetHeight" v-model="pagination.page"
                :max="Math.ceil(pagination.total / pagination.rowsPerPage)" :max-pages="5" boundary-numbers
                direction-links />

              <div class="flexbox">
                <div style="margin-right: .125rem;">跳到</div>
                <div class="roundBox">
                  <q-input class="dynamic-label-input" v-model="jumpText" style="width:.625rem;" dense
                    @keyup.enter="goJump">
                  </q-input>
                </div>
                <q-btn class="custom-btn" label="跳转" @click="goJump" />
              </div>

              <div>


              </div>
            </div>
          </template>
        </q-table>
      </div>

      <!-- 查看仿真详情对话框 -->
      <q-dialog v-model="viewDialog">
        <q-card class="blackCard">
          <q-card-section class="row items-center">
            <div class="labelColor">仿真详情</div>
            <q-space />
            <q-btn class="labelColor closeBtn" icon="close" flat round dense v-close-popup />
          </q-card-section>

          <q-card-section>
            <div class="q-gutter-y-md">
              <div class="row  labelColor">
                <div class="fontlabel">仿真环境大小:</div>
                <div class="font10">{{ viewingRecord.size }} GB</div>
              </div>
              <div class="row labelColor">
                <div class="fontlabel">上传人:</div>
                <div class="font10">{{ viewingRecord.uploader }}</div>
              </div>
              <div class="row labelColor">
                <div class="fontlabel">引擎类型:</div>
                <div class="font10">{{ viewingRecord.engineType }}</div>
              </div>
              <div class="row labelColor">
                <div class="fontlabel">是否需要渲染:</div>
                <div class="font10">{{ viewingRecord.needRender ? '是' : '否' }}</div>
              </div>
              <div class="row labelColor">
                <div class="fontlabel">创建时间:</div>
                <div class="font10">{{ formatDate(viewingRecord.createTime) }}</div>
              </div>
            </div>
          </q-card-section>

          <q-card-actions align="right">
            <q-btn class="grayBox roundBox" flat label="关闭" color="primary" v-close-popup />
          </q-card-actions>
        </q-card>
      </q-dialog>

      <!-- 右侧：仿真环境存量 -->
      <div class="col-12 col-md-4">
        <!-- 修改仿真环境存量图表容器 -->
        <q-card flat bordered style="width: 100%;height: 4rem;">
          <q-card-section style="padding-bottom: 0 !important;">
            <div class="labelColor">仿真环境存量</div>
          </q-card-section>

          <q-separator class="mt10"></q-separator>

          <q-inner-loading :showing="visible">
            <q-spinner-gears size="50px" color="primary" />
          </q-inner-loading>
          <div ref="usageChart" v-show="showSimulatedReturnData && hasEngineData" style="width:7rem; height:3rem;"></div>
          <div v-show="showSimulatedReturnData && !hasEngineData" class="no-data-placeholder">暂无数据</div>
        </q-card>

        <!-- 修改内存使用量图表容器 -->
        <q-card class="mt10" style="width: 100%;height: 4rem;">
          <q-card-section style="padding-bottom: 0 !important;">
            <div class="labelColor">内存使用量</div>
          </q-card-section>
          <q-separator class="mt10"></q-separator>


          <q-inner-loading :showing="visible" class="cusLoad">
            <q-spinner-gears size="50px" color="primary" />
          </q-inner-loading>

          <div class="flexReverse">
            <div class="textDec" v-show="showSimulatedReturnData && hasEngineData">
              <div class="labelColor font10">总计: {{ totalStorage }}</div>
              <div class="labelColor font10">已使用: {{ usedStorage }}</div>
            </div>
            <div ref="memoryChart" v-show="showSimulatedReturnData && hasEngineData" style="width:7rem; height: 3rem;"></div>
            <div v-show="showSimulatedReturnData && !hasEngineData" class="no-data-placeholder">暂无数据</div>
          </div>

        </q-card>

        <q-card class="mt10">
          <div style="display: flex;justify-content: space-evenly;padding: 10px 0px;">
            <q-btn color="primary" label="上传仿真" class="q-mr-sm roundBox btn50" @click="uploadDialog = true" />
            <q-btn color="secondary" label="导出仿真" class="roundBox btn50" @click="exportSimulation" />
          </div>
        </q-card>
      </div>
    </div>

    <!-- 上传仿真弹窗 -->
    <q-dialog v-model="uploadDialog">
      <q-card class="blackCard">
        <q-card-section class="row items-center">
          <div class="labelColor">上传仿真环境</div>
          <q-space />
          <q-btn class="closeBtn" icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section class="q-ml-md q-pa-md">
          <q-form @submit="onSubmit" class="q-gutter-md">
            <div class="row q-col-gutter-md">
              <div class="col-12">
                <q-input v-model="newSimulation.name" :label="newSimulation.name ? '' : '仿真名称'" outlined dense
                  :rules="[val => !!val || '请输入仿真名称']" />
              </div>

              <div class="col-12">
                <q-input v-model="newSimulation.desc" :label="newSimulation.desc ? '' : '仿真描述'" outlined dense
                  type="textarea" autogrow />
              </div>

              <div class="col-sm-6 col-xs-12">
                <q-select v-model="newSimulation.engine_type" :options="engineOptions"
                  :label="newSimulation.engine_type ? '' : '仿真引擎'" outlined dense map-options emit-value
                  :rules="[val => !!val || '请选择仿真引擎']" />
              </div>

              <div class="col-sm-6 col-xs-12">
                <q-input v-model="newSimulation.engine_size" :label="newSimulation.engine_size ? '' : '引擎大小 (GB)'"
                  outlined dense type="number" :rules="[
                    val => !!val || '请输入引擎大小',
                    val => val > 0 || '引擎大小必须大于0'
                  ]" />
              </div>

              <div class="col-sm-6 col-xs-12">
                <q-input v-model="newSimulation.address" :label="newSimulation.address ? '' : '仿真地址 (可选)'" outlined
                  dense hint="仿真环境的网络地址" />
              </div>

              <div class="col-sm-6 col-xs-12">
                <div class="q-pa-md">
                  <q-toggle class="labelColor font10" v-model="newSimulation.need_render" label="是否需要渲染" />
                </div>
              </div>
            </div>

            <q-separator class="q-my-md" />

            <div class="row q-col-gutter-md">
              <div class="col-sm-6 col-xs-12">
                <q-file v-model="uploadFiles.config_file" :label="uploadFiles.config_file ? '' : '配置文件 (可选)'" outlined
                  dense counter accept=".json,.yaml,.yml,.xml,.config" hint="上传仿真配置文件">
                  <template v-slot:prepend>
                    <q-icon name="settings" />
                  </template>
                </q-file>
              </div>

              <div class="col-sm-6 col-xs-12">
                <q-file v-model="uploadFiles.base_image" :label="uploadFiles.base_image ? '' : '基础镜像 (可选)'" outlined
                  dense counter accept=".iso,.img,.tar,.zip,.rar,.gz" hint="上传仿真环境的基础镜像">
                  <template v-slot:prepend>
                    <q-icon name="cloud_upload" />
                  </template>
                </q-file>
              </div>
            </div>

            <div class="row justify-end q-mt-md">
              <q-btn label="取消" color="grey-7" v-close-popup class="q-mr-sm roundBox" />
              <q-btn class="roundBox" label="上传" type="submit" color="primary" :loading="submitting" />
            </div>
          </q-form>
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- 删除确认弹窗 -->
    <q-dialog v-model="deleteDialog" persistent>
      <q-card class="blackCard">
        <q-card-section class="row items-center">
          <q-avatar icon="delete" color="negative" text-color="white" />
          <span class="q-ml-sm labelColor">确认删除此仿真文件?</span>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" color="primary" v-close-popup />
          <q-btn flat label="删除" color="negative" @click="deleteFile" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { usePlugin } from 'composables/plugin'
import { api } from 'boot/axios'
import { date } from 'quasar'
import _ from 'lodash'
import * as echarts from 'echarts';

const { notify, dialog } = usePlugin()

// 搜索过滤器（修改为对象形式，便于管理）
const filterOptions = reactive({
  creator: '',
  name: '',
  engine_type: null,
  need_render: null,
  start_date: '',
  end_date: '',
  dateVal: [],
})

const start_date = computed(() => filterOptions.dateVal?.[0] || '');
const end_date = computed(() => filterOptions.dateVal?.[1] || '');

const visible = ref(false)
const showSimulatedReturnData = ref(false)
// 内存使用量echart
const memoryChart = ref(null);
let memoryChartInstance = null;

const jumpText = ref('')//跳转
// 下拉选项
const renderOptions = [
  { label: '是', value: true },
  { label: '否', value: false }
]

// 表格配置
const loading = ref(false)
const selected = ref([])
const pagination = ref({
  rowsPerPage: 5,
  page: 1,
  total: 0
})

const columns = [
  { name: 'name', align: 'left', label: '仿真名称', field: 'name', sortable: true },
  { name: 'desc', align: 'left', label: '仿真描述', field: 'desc' },
  { name: 'size', align: 'center', label: '仿真环境大小', field: 'size', sortable: true },
  { name: 'uploader', align: 'center', label: '上传人', field: 'uploader' },
  { name: 'engine', align: 'center', label: '引擎类型', field: 'engine' },
  { name: 'is_required', align: 'center', label: '是否需要渲染', field: 'is_required' },
  { name: 'create_time', align: 'center', label: '创建时间', field: 'create_time', sortable: true },
  { name: 'operations', align: 'center', label: '操作', field: 'operations' }
]

const onRowsPerPageChange = (e) => {
  console.log(8888, e)
  pagination.value.rowsPerPage = e
  fetchSimulationFiles()
}

// 跳转（支持回车键）
const goJump = () => {
  console.log("回车");
  pagination.value.page = Number(jumpText.value)
}

// 仿真文件数据
const simulationFiles = ref([
  { id: 1, name: '六部仿真', size: '218GB', uploader: '张三', is_required: true, engine: 'Unreal Engine5', create_time: '2023-01-28 23:50:21' },
  { id: 2, name: 'Airsim无人车仿真', size: '321MB', uploader: '张三', is_required: true, engine: 'Unreal Engine4', create_time: '2023-01-28 23:50:21' },
  { id: 3, name: 'Airsim无人机仿真', size: '4.2GB', uploader: '张三', is_required: true, engine: 'Unreal Engine4', create_time: '2023-01-28 23:50:21' },
  { id: 4, name: '量子岛屿争夺模拟', size: '4.2GB', uploader: '张三', is_required: false, engine: 'Command', create_time: '2023-01-28 23:50:21' }
])

// 仿真环境存量数据
const diskUsage = ref(74)
const usageStats = ref({
  total: 0.7,
  UE5: 0.4,
  UE4: 0.35,
  Command: 0.3
})
const totalStorage = ref('384TB')
const usedStorage = ref('284.16TB')
const engineStats = ref({}) // 用于存储各引擎类型的原始数据

// 弹窗控制
const uploadDialog = ref(false)
const deleteDialog = ref(false)
const fileToDelete = ref(null)
const viewDialog = ref(false)
const viewingRecord = ref({
  size: '',
  uploader: '',
  engineType: '',
  needRender: false,
  createTime: ''
})

// 上传相关
const simulationFile = ref(null)
const submitting = ref(false)
const uploadFiles = reactive({
  config_file: null,
  base_image: null
})
const newSimulation = reactive({
  name: '',
  desc: '',
  engine_type: '',
  engine_size: 20,
  address: null,
  need_render: false
})

const engineOptions = [
  { label: 'Unreal Engine 5', value: 'UE5' },
  { label: 'Unreal Engine 4', value: 'UE4' },
  { label: 'Command', value: 'Command' },
  { label: 'Unity', value: 'Unity' },
  { label: '其他', value: 'Other' }
]

// 添加防抖处理
// const debouncedFetchStats = _.debounce(fetchSimulationStats, 10)

// 监听分页变化
watch(() => pagination.value.rowsPerPage, (newVal, oldVal) => {
  // 当 rowsPerPage 变化时，重置页码为 1
  pagination.value.page = 1
})

// 保留原有的 page 监听（单独处理页码变化）
watch(() => pagination.value.page, () => {
  fetchSimulationFiles()
})


// 获取仿真文件列表
onMounted(() => {
  fetchSimulationFiles()
})

// 获取仿真表格
function fetchSimulationFiles() {
  loading.value = true
  
  // 重置引擎统计数据，防止数据叠加
  engineStats.value = {}

  // 构建查询参数
  const params = new URLSearchParams()
  if (filterOptions.creator) params.append('creater', filterOptions.creator)
  if (filterOptions.name) params.append('name', filterOptions.name)
  if (filterOptions.engine_type !== null) params.append('engine_type', filterOptions.engine_type)
  if (filterOptions.need_render !== null) params.append('need_render', filterOptions.need_render)
  if (start_date.value) params.append('start_date', start_date.value)
  if (end_date.value) params.append('end_date', end_date.value)
  // 添加分页参数
  params.append('page', pagination.value.page.toString())
  params.append('page_size', pagination.value.rowsPerPage.toString())

  api.get(`backend/emulators/?${params.toString()}`)
    .then(res => {
      // 更新总数
      if (res.count !== undefined) {
        pagination.value.total = res.count
      }
      simulationFiles.value = (res?.results || []).map(item => {
        const {
          id,
          name,
          desc,
          emulator_id: emulatorId,
          engine_type: engine,
          engine_size: size,
          need_render: is_required,
          create_time: createTime,
          creater,
          creater_name
        } = item

        // 也可以在这里累计引擎统计信息
        const engineType = item.engine_type
        const engineSize = item.engine_size || 0
        if (engineType) {
          if (!engineStats.value[engineType]) {
            engineStats.value[engineType] = 0
          }
          engineStats.value[engineType] += engineSize
        }

        return {
          id,
          emulatorId,
          name,
          desc,
          size: formatFileSize(size * 1024 * 1024 * 1024), // 转换为字节后格式化
          uploader: creater_name || creater, // 使用creater_name作为上传人
          is_required,
          engine,
          create_time: date.formatDate(createTime, 'YYYY-MM-DD HH:mm:ss')
        }
      })
      showSimulatedReturnData.value = true
      initUsageChart()
      initmemoryChart()
      loading.value = false
    })
    .catch(err => {
      console.error('获取仿真文件列表失败:', err)
      loading.value = false
    })
}

// 查看文件详情
function viewFile(row) {
  viewingRecord.value = {
    size: row.size || '20.00',
    uploader: row.uploader || 'admin',
    engineType: row.engine || 'Command',
    needRender: row.is_required || false,
    createTime: row.create_time
  }
  viewDialog.value = true
}

// 删除文件
function confirmDelete(file) {
  fileToDelete.value = file
  deleteDialog.value = true
}

function deleteFile() {
  if (fileToDelete.value) {
    loading.value = true

    api.delete(`backend/emulators/${fileToDelete.value.id}/`)
      .then(() => {
        const index = simulationFiles.value.findIndex(f => f.id === fileToDelete.value.id)
        if (index !== -1) {
          // 删除前先记录下要删除的文件信息
          const fileToRemove = simulationFiles.value[index]
          // 从engineStats中减去对应的数据
          if (fileToRemove.engine && engineStats.value && engineStats.value[fileToRemove.engine]) {
            // 从显示格式中提取数值
            const sizeMatch = fileToRemove.size.match(/(\d+(\.\d+)?)\s*(GB|MB|KB|B|TB)/)
            if (sizeMatch) {
              const sizeNum = parseFloat(sizeMatch[1])
              const sizeUnit = sizeMatch[3]
              // 转换为GB
              let sizeInGB = sizeNum
              if (sizeUnit === 'TB') sizeInGB = sizeNum * 1024
              if (sizeUnit === 'MB') sizeInGB = sizeNum / 1024
              if (sizeUnit === 'KB') sizeInGB = sizeNum / (1024 * 1024)
              if (sizeUnit === 'B') sizeInGB = sizeNum / (1024 * 1024 * 1024)
              
              // 更新统计数据
              engineStats.value[fileToRemove.engine] -= sizeInGB
              if (engineStats.value[fileToRemove.engine] < 0) {
                engineStats.value[fileToRemove.engine] = 0
              }
              
              // 如果所有引擎数据都为0，可能需要显示"暂无数据"
              let hasData = false
              for (const key in engineStats.value) {
                if (engineStats.value[key] > 0) {
                  hasData = true
                  break
                }
              }
              if (!hasData) {
                // 清空数据以确保显示"暂无数据"
                engineStats.value = {}
              }
            }
          }
          
          // 从表格中移除
          simulationFiles.value.splice(index, 1)
        }
        notify('删除成功', 'positive')
        // 重新初始化图表
        initUsageChart()
        initmemoryChart() // 同时更新内存使用量图表
        fileToDelete.value = null
        loading.value = false
      })
      .catch(err => {
        console.error('删除文件失败:', err)
        notify('删除文件失败')
        loading.value = false
      })
  }
}

// 上传仿真文件
function onSubmit() {
  submitting.value = true

  const formData = new FormData()
  formData.append('name', newSimulation.name)
  formData.append('desc', newSimulation.desc || newSimulation.name)
  formData.append('engine_type', newSimulation.engine_type)
  formData.append('engine_size', newSimulation.engine_size)
  formData.append('need_render', newSimulation.need_render)

  if (newSimulation.address) {
    formData.append('address', newSimulation.address)
  }

  if (uploadFiles.config_file) {
    formData.append('config_file', uploadFiles.config_file)
  }

  if (uploadFiles.base_image) {
    formData.append('base_image', uploadFiles.base_image)
  }

  api.post('backend/emulators/', formData)
    .then(res => {
      const {
        id,
        name,
        desc,
        engine_type: engine,
        engine_size: size,
        need_render: is_required,
        create_time: createTime,
        creater
      } = res

      const newFile = {
        id,
        name,
        desc,
        size: formatFileSize(size * 1024 * 1024 * 1024),
        uploader: creater,
        is_required,
        engine,
        create_time: date.formatDate(createTime, 'YYYY-MM-DD HH:mm:ss')
      }

      simulationFiles.value.unshift(newFile)
      uploadDialog.value = false
      notify('上传成功', 'positive')
      fetchSimulationFiles()
      // 重置表单
      resetNewSimulationForm()
    })
    .catch(err => {
      console.error('上传失败:', err)
      notify('上传失败: ' + (err.message || '未知错误'), 'negative')
    })
    .finally(() => {
      submitting.value = false
    })
}

function resetNewSimulationForm() {
  newSimulation.name = ''
  newSimulation.desc = ''
  newSimulation.engine_type = ''
  newSimulation.engine_size = 20
  newSimulation.address = null
  newSimulation.need_render = false
  uploadFiles.config_file = null
  uploadFiles.base_image = null
}

// 导出仿真文件
function exportSimulation() {
  if (selected.value.length === 0) {
    notify('请选择要导出的仿真', 'warning')
    return
  }

  const ids = selected.value.map(item => item.id).join(',')

  // 获取仿真数据
  api.get('backend/emulators/', {
    params: {
      page_size: 1000,
      ids: ids
    }
  })
    .then(response => {
      if (!response || !response.results) {
        notify('导出失败', 'negative')
        return
      }

      // 转换为CSV格式
      const headers = ['仿真编号', '仿真名称', '仿真环境大小', '引擎类型', '上传人', '是否需要渲染', '创建时间', '描述']
      const rows = response.results.map(simulation => [
        simulation.id,
        simulation.name || '',
        simulation.size ? simulation.size + ' GB' : '',
        simulation.engine_type || '',
        simulation.creator || '',
        simulation.need_render ? '是' : '否',
        simulation.created_at || '',
        simulation.description || ''
      ])

      // 创建CSV内容
      const csvContent = [
        headers.join(','),
        ...rows.map(row => row.join(','))
      ].join('\n')

      // 创建并下载文件
      const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `仿真列表_${date.formatDate(new Date(), 'YYYY-MM-DD_HH-mm')}.csv`)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      notify('导出成功', 'positive')
    })
    .catch(error => {
      console.error('导出失败:', error)
      notify('导出失败', 'negative')
    })
}

// 获取仿真环境  图表 统计信息
// function fetchSimulationStats() {
//   visible.value = true
//   showSimulatedReturnData.value = false

//   api.get('backend/emulators/stats/')
//     .then(res => {
//       const { total_storage, used_storage, engine_stats } = res
//       // 保存原始数据
//       if (engine_stats) {
//         console.log("engine_stats", engine_stats)
//         engineStats.value = engine_stats
//       }
//       // 更新仪表盘数据
//       if (total_storage && used_storage) {
//         diskUsage.value = Math.round((used_storage / total_storage) * 100) //百分比
//         totalStorage.value = formatFileSize(total_storage)//总计
//         usedStorage.value = formatFileSize(used_storage)//已使用
//       }
//       // 更新环境使用量数据
//       if (engine_stats) {
//         // 计算各引擎总和
//         const total = Object.values(engine_stats).reduce((sum, val) => sum + val, 0)

//         if (total > 0) {
//           // 计算每种引擎占比
//           usageStats.value = {
//             total: used_storage / total_storage || 0.7,
//             UE5: (engine_stats.UE5 || 0) / total,
//             UE4: (engine_stats.UE4 || 0) / total,
//             Command: (engine_stats.Command || 0) / total
//           }
//         }
//       }
//       initUsageChart()
//       initmemoryChart()
//     })
//     .catch(err => {
//       console.error('获取统计信息失败:', err)
//       // 失败时尝试从文件列表计算
//       fetchSimulationStats()
//     }).finally(() => {
//       visible.value = false
//       showSimulatedReturnData.value = true
//     })
// }

// 格式化文件大小
function formatFileSize(bytes) {
  if (!bytes) return '0 B'

  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let i = 0
  while (bytes >= 1024 && i < units.length - 1) {
    bytes /= 1024
    i++
  }

  return `${bytes} ${units[i]}`         //输出16GB
  // return `${bytes.toFixed(2)} ${units[i]}`  //输出16.00GB
}

// 搜索功能
function handleSearch() {
  pagination.value.page = 1 // 重置到第一页
  fetchSimulationFiles()
}

function resetSearch() {
  // 重置所有过滤条件
  Object.keys(filterOptions).forEach(key => {
    if (typeof filterOptions[key] === 'boolean') {
      filterOptions[key] = null
    } else {
      filterOptions[key] = ''
    }
  })

  pagination.value.page = 1 // 重置到第一页
  fetchSimulationFiles()
}

// 统计相关函数
function getTotalEngineSize() {
  if (!engineStats.value) return 0
  return Object.values(engineStats.value).reduce((sum, val) => sum + val, 0)
}

// 添加日期格式化函数
function formatDate(dateString) {
  if (!dateString) return ''
  return date.formatDate(dateString, 'YYYY-MM-DD HH:mm:ss')
}

// 添加 chart 引用
const usageChart = ref(null);
let usageChartInstance = null;

// 添加一个计算属性来检查是否有引擎数据
const hasEngineData = computed(() => {
  if (!engineStats.value || Object.keys(engineStats.value).length === 0) {
    return false;
  }
  // 检查是否所有引擎数据都为0
  return Object.values(engineStats.value).some(value => value > 0);
});

// 初始化图表函数
const initUsageChart = () => {
  if (!usageChart.value) return;
  
  // 如果没有数据，不初始化图表
  if (!hasEngineData.value) return;

  // 销毁旧实例
  if (usageChartInstance) {
    usageChartInstance.dispose();
  }

  // 初始化图表
  usageChartInstance = echarts.init(usageChart.value);

  // 准备数据
  const engineData = [];
  const totalSize = getTotalEngineSize();

  // 添加总量数据
  if (totalSize > 0) {
    engineData.push({
      name: '总量',
      value: totalSize,
      itemStyle: { color: '#1976D2' }
    });
  }

  // 添加各引擎数据
  for (const [engine, size] of Object.entries(engineStats.value)) {
    if (size > 0) {
      engineData.push({
        name: engine,
        value: size,
        itemStyle: { color: getEngineColor(engine) }
      });
    }
  }
  console.log("engineData", engineData)
  // 排序数据（按大小降序）
  engineData.sort((a, b) => b.value - a.value);

  // 配置项
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: params => {
        const data = params[0].data;
        return `${data.name}: ${formatFileSize(data.value * 1024 * 1024 * 1024)}`;
      }
    },
    grid: {
      left: '10%',
      right: '5%',
      bottom: window.screen.width <= 1536 ? '15%':'8%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '存储大小 (GB)',
      nameLocation: 'center',
      nameGap: 30, // 调整名称与轴线的距离，数值越大越远
      nameTextStyle: {
        color: '#fff',
        fontSize: window.screen.width <= 1536 ? 12:16
      },
      axisLabel: {
        color: '#fff',
        fontSize: window.screen.width <= 1536 ? 12:16
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#fff'
        }
      }
    },
    yAxis: {
      type: 'category',
      data: engineData.map(item => item.name),
      axisLabel: {
        color: '#fff',
        fontSize: window.screen.width <= 1536 ? 12:16
      }
    },
    series: [{
      name: '使用量',
      type: 'bar',
      data: engineData,
      label: {
        show: true,
        position: 'top',
        formatter: params => formatFileSize(params.value * 1024 * 1024 * 1024),
        color: '#fff',
        fontSize: window.screen.width <= 1536 ? 12:16
      },
      barWidth: '50%'
    }]
  };

  // 设置配置项
  usageChartInstance.setOption(option);

  // 响应式调整
  window.addEventListener('resize', () => usageChartInstance.resize());
};

// 引擎颜色映射
const getEngineColor = (engine) => {
  const colors = {
    'UE5': '#5470C6',
    'UE4': '#91CC75',
    'Command': '#FAC858',
    'Unity': '#EE6666',
    'Other': '#73C0DE'
  };
  return colors[engine] || '#73C0DE';
};

// 初始化仪表盘
const initmemoryChart = () => {
  if (!memoryChart.value) return;
  
  // 如果没有数据，不初始化图表
  if (!hasEngineData.value) return;

  if (memoryChartInstance) {
    memoryChartInstance.dispose();
  }

  memoryChartInstance = echarts.init(memoryChart.value);

  const option = {

    series: [{
      type: 'gauge',
      radius: '90%',  // 增大半径
      center: ['60%', '60%'],
      progress: {
        show: false,
        width: 18,
        itemStyle: {
          color: 'auto'  // 自动匹配分段颜色
        }
      },
      axisLine: {
        lineStyle: {
          width: 18,
          color: [
            [0.3, '#165DFF'],  // 0-30% 蓝色
            [0.7, '#F7BA1E'],  // 30-70% 黄色
            [1, '#F53F3F']     // 70-100% 红色
          ]
        }
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: '#fff'
        }
      },
      splitLine: {
        show: false
      },
      axisLabel: {
        show: true,
        color: '#fff',
        fontSize: window.screen.width <= 1536 ? 12:18
      },
      pointer: {
        itemStyle: {
          color: 'auto'
        }
      },
      detail: {
        valueAnimation: true,
        fontSize: 24,
        offsetCenter: [0, '70%'],
        color: '#fff',
        formatter: '{value}%'
      },
      data: [{
        value: diskUsage.value
      }]
    }]
  };

  memoryChartInstance.setOption(option);
  window.addEventListener('resize', () => memoryChartInstance.resize());
};
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
}

.text {
  padding-left: 0;
  position: relative;
  left: 4px;
  top: 6px;
  width: 11px;
}

@media (max-width: 599px) {
  .col-md-7 {
    margin-bottom: 16px;
  }

  .col-md-7 .row {
    margin: 0;
  }

  .col-5 {
    padding: 0 4px;
  }

  .text {
    line-height: 40px;
  }

  .flex.justify-start {
    justify-content: flex-start;
  }

  .q-btn {
    min-width: 80px;
  }
}

:deep(.q-field--auto-height.q-field--labeled .q-field__control-container) {
  padding-top: 0;
}

:deep(.q-field--labeled .q-field__native) {
  padding-top: 0;
  padding-bottom: 0;
}

:deep(.q-textarea.q-field--dense.q-field--labeled .q-field__control-container) {
  padding: 0 !important;
}

:deep(.q-textarea.q-field--dense.q-field--labeled .q-field__native) {
  height: 100% !important;
  line-height: 36px;
}

.flexCenter {
  width: 3rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mt10 {
  margin-top: 10px;
}

.flexReverse {
  display: flex;
  position: relative;

  .textDec {
    position: absolute;
    left: .25rem;
    top: .25rem;
  }
}

.noPb {
  padding-bottom: 0;
  position: relative;
  height: 250px; //减去标题
}

// 跳转按钮
.custom-btn {
  width: 60px;
  background-color: #396ea4;
  margin-left: 70px;
  border-radius: 6px;
}

/* 有值或聚焦时隐藏 label */
.dynamic-label-input.q-field--with-content :deep(.q-field__label),
.dynamic-label-input.q-field--focused :deep(.q-field__label) {
  display: none;
}

.dynamic-label-input :deep(.q-field__inner) {
  border: none; //设置圆角的 必须单独取消
}

// 单独设置圆角和边框色
.dynamic-label-input :deep(.q-field__inner) {
  border-radius: 8px;
}

// 文字居中
.dynamic-label-input :deep(.q-field__native) {
  text-align: center;
}

.paginationEl {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 输入框start
.q-field {
  font-size: .175rem;
  height: .5rem;
}

:deep(.q-field--dense .q-field__control) {
  //输入高度
  height: 100% !important;
}

:deep(.q-field--dense .q-field__label) {
  font-size: .175rem !important;
  top: .125rem;
}

:deep(.q-field__label) {
  //label
  line-height: .25rem !important;
}

// 尾部带图标输入框
:deep(.q-field__marginal) {
  font-size: .3rem !important;
}

:deep(.q-field--labeled .q-field__native) {
  line-height: .3rem !important;
}

:deep(.q-field--dense .q-field__marginal) {
  height: .5rem !important;
}

// element 日期选择器
:deep(.datePicker) {
  height: 0.5rem !important;
}

:deep(.el-date-editor .el-range__icon) {
  //left icon
  font-size: .175rem;
}

:deep(.el-range-editor--large .el-range-input) {
  font-size: .175rem;
}

:deep(.el-date-editor .el-range__close-icon) {
  //close 
  font-size: .175rem;
}

:deep(.el-range-editor--large .el-range-separator) {
  //至
  font-size: .175rem;
  line-height: .5rem;
}

// 输入框end

// 表格适配
:deep(.q-table__container) {
  // border: none !important;
  min-height: 7.5rem !important; //最小高度600px
  box-shadow: 0 .0125rem .0625rem #0003, 0 .025rem .025rem #00000024, 0 .0375rem .0125rem -0.025rem #0000001f;
}

:deep(.q-table__container .q-table thead th) {
  font-size: .175rem !important;
}

:deep(.q-table__container .q-table tbody td) {
  font-size: .175rem !important;
}

:deep(.q-table td) {
  padding: .0875rem .2rem !important;
}

:deep(.q-table tbody td) {
  height: .6rem !important;
}

// 表格内标签
:deep(.q-table .q-badge) {
  width: .6rem !important;
  // height: .3125rem !important;
  line-height: .3125rem !important;
  border-radius: .0625rem !important;
  font-size: .15rem !important;
  display: block !important;
  text-align: center;
}

// 分页器适配
:deep(.q-pagination__middle .q-btn) {
  width: .35rem;
  height: .35rem;
}

:deep(.q-table__bottom) {
  font-size: .15rem; //文字大小
}

:deep(.q-field--dense .q-field__marginal) {
  height: .5rem !important;
}

// 跳转按钮
.custom-btn.q-btn {
  width: fit-content;
  background-color: #396ea4;
  margin-left: .875rem;
  border-radius: .0625rem;
  height: 0.45rem;
}

:deep(.q-btn__content) {
  font-size: .175rem;
}

// 按钮
:deep(.q-btn) {
  padding: .05rem .2rem;
  height: .45rem;
}


.yes {
  background: #214d70;
  border: .0125rem solid #4ab4ff !important;
}

.ohNo {
  background-color: #2f2b2c;
  border: .0125rem solid #7e352b !important;
  text-align: center;
}

.paginationEl {
  display: flex;
  align-items: center;
  justify-content: center;
}

.blackCard {
  min-width: 5.375rem;
  background: rgba(0, 0, 0, .9) !important;
  border: none !important;
  border-radius: .0625rem !important;
  // overflow: hidden !important;

  :deep(.q-btn__content) {
    font-size: .175rem;
  }

  .delAvatar {
    font-size: .6rem;
  }

  .delContent {
    font-size: .2rem;
  }

  .q-field--with-bottom {
    padding-bottom: 0 !important;
  }

  .q-field__bottom {
    min-height: .25rem !important;
    padding: .1rem .15rem 0 !important;
  }

  :deep(.q-field--dense .q-field__bottom) {
    font-size: .1375rem !important;
  }

  .q-col-gutter-md>* {
    padding-left: .25rem !important;
    padding-top: .25rem !important;
  }

  .q-mt-md {
    margin-top: .5rem !important;
  }
}

.fontlabel {
  min-width: 1.5rem;
  text-align: right;
  font-size: .175rem;
}

.font10 {
  font-size: .15rem;
  margin-left: .25rem;
}

// 添加暂无数据占位样式
.no-data-placeholder {
  width: 100%;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ccc;
  font-size: 0.2rem;
}
</style>
