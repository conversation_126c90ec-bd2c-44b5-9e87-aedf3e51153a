<template>
  <q-dialog ref="dialogRef" @before-show="onBeforeShow" @hide="onDialogHide" position="right" full-height>
    <q-card class="dialog-card">
      <q-card-section class="flex items-center">
        <div class="title">{{ scenario.id ? '编辑想定' : '新增想定' }}</div>
        <q-btn v-close-popup icon="close" class="q-ml-auto" round flat dense />
      </q-card-section>

      <q-form @submit="onSubmit">
        <q-card-section>
          <div class="text-bold q-mb-sm require">想定名称</div>
          <q-input
            v-model.trim="scenario.name"
            :rules="[val => !!val || '请输入想定名称']"
            placeholder="请输入想定名称"
            maxlength="20"
            lazy-rules
            outlined
          />

          <div class="text-bold q-mt-md q-mb-sm require">想定代码</div>
          <q-file
            v-if="!scenario.id || !showFileChip"
            v-model.trim="scenario.file"
            :rules="[val => !!val || '请选择代码zip文件']"
            label="请选择代码zip文件"
            outlined
          >
            <template v-slot:append>
              <q-avatar>
                <SvgFile />
              </q-avatar>
            </template>

            <template v-slot:after>
              <q-btn v-if="!showFileChip" @click="showFileChip = true" label="取消编辑" color="grey-9" flat />
            </template>
          </q-file>
          
          <div v-else>
            <q-chip
              v-model="showFileChip"
              :label="scenario.fileName"
              @click="downloadScenarioFile(scenario.id)"
              size="lg"
              color="primary"
              text-color="black"
              removable
              clickable
            >
              <q-tooltip>{{ scenario.fileName }}</q-tooltip>
            </q-chip>
          </div>

          <div class="text-caption text-weight-light">（想定文件应包含想定定义及想定调用配置文件 <a @click="downloadTemplate" href="#">模版下载</a>）</div>

          <div class="text-bold q-mt-md q-mb-sm">
            想定解析
            <q-btn @click="checkFile" class="" flat>
                <q-icon v-if="showEye">
                  <SvgEye />
                </q-icon>
                <q-icon v-else>
                  <SvgEyeSlash />
                </q-icon>
            </q-btn>
          </div>

          <div v-if="showEye" class="q-pa-md">
              <div class="row">
                <div class="col-md-6">
                  <div class="text-subtitle2 text-justify text-center">状态空间：</div>
                  <q-list v-if="obsItems.length > 0" class="text-bold q-mt-md q-mr-md" bordered>
                    <q-item v-for="obsItem in obsItems" :key="obsItem.id">
                      <q-item-section>
                        <q-item-label>{{ obsItem.name }}</q-item-label>
                        <q-item-label caption>{{ obsItem.description }}</q-item-label>
                      </q-item-section>
                    </q-item>
                  </q-list>
                </div>
                <div class="col-md-6">
                  <div class="text-subtitle2 text-justify text-center">动作空间：</div>
                  <q-list v-if="actionItems.length > 0" class="text-bold q-mt-md" bordered>
                    <q-item v-for="actionItem in actionItems" :key="actionItem.id">
                      <q-item-section>
                        <q-item-label>{{ actionItem.name }}</q-item-label>
                        <q-item-label caption>{{ actionItem.description }}</q-item-label>
                      </q-item-section>
                    </q-item>
                  </q-list>
                </div>
              </div>
          </div>
          
          <div class="text-bold q-mt-md q-mb-sm require">想定色彩</div>
          <ScenarioColor v-model="scenario.color" class="q-pb-md" required />

          <div class="text-bold q-mt-md q-mb-sm">想定描述</div>
          <q-input
            v-model.trim="scenario.desc"
            placeholder="请输入想定描述..."
            type="textarea"
            maxlength="200"
            outlined
          />
        </q-card-section>

        <q-card-actions align="right">
          <q-btn v-close-popup label="取消" color="grey-9" />
          <q-btn type="submit" :loading="loading" label="确定" color="primary" text-color="black" />
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref } from 'vue'
import { useDialogPluginComponent, openURL } from 'quasar'
import { api } from 'boot/axios'
import { usePlugin } from 'composables/plugin'
import ScenarioColor from './ScenarioColor.vue'
import SvgFile from 'assets/images/svg/file.svg'
import SvgEye from 'assets/images/svg/eye.svg'
import SvgEyeSlash from 'assets/images/svg/eye-slash.svg'

const props = defineProps({
  params: Object,
  emulator: Object,
})

defineEmits([...useDialogPluginComponent.emits])

const { dialogRef, onDialogOK, onDialogHide } = useDialogPluginComponent()
const { notify } = usePlugin()

const loading = ref(false)
const scenario = ref({
  id: null,
  name: '',
  file: null,
  fileName: '',
  color: 0,
  desc: '',
  config: null,
  emulator: null,
})

const showFileChip = ref(true)
const showEye = ref(false)

const obsItems = ref([])
const actionItems = ref([])

function checkFile() {

  const { file } = scenario.value
  if (!file) {
    notify('请先选择代码zip文件包')
    loading.value = false
    return
  }

  showEye.value = !showEye.value

  const url =  'backend/scenarios/check_file'
  const method = 'post'

  api
    .request({
      url,
      method,
      data: {
        file,
      },
      headers: { 'Content-Type': 'multipart/form-data' },
    })
    .then(response => {
       const { config } = response.data

       obsItems.value = config.agents[0]?.observations || []
       actionItems.value = config.agents[0]?.actions || []
     })
    .catch(error => {
       notify('解析失败')
     })
}

function onBeforeShow() {
  if (props.params) {
    const { id, name, config, file: fileName, color = 0, desc = '' } = props.params

    scenario.value = {
      id,
      name,
      fileName: fileName ? decodeURIComponent(fileName.split('/').pop()) : '',
      color,
      desc,
      config,
    }
    showFileChip.value = !!fileName
    obsItems.value = config.agents[0]?.observations || []
    actionItems.value = config.agents[0]?.actions || []
  }

  if (props.emulator) {
    scenario.value.emulator = props.emulator.id
  }
}

function downloadScenarioFile(id) {
  if (id) {
    openURL(`${api.defaults.baseURL}/backend/scenarios/download_file?id=${id}`)
  }
}

function downloadTemplate() {
  openURL(`${api.defaults.baseURL}/common/templates/aircombat_single.zip`)
}

function onSubmit() {
  loading.value = true

  const { id, name, file, color, desc, emulator } = scenario.value

  const url = id ? `backend/scenarios/${id}/` : 'backend/scenarios/'
  const method = id ? 'patch' : 'post'

  api
    .request({
      url,
      method,
      data: {
        name,
        file,
        color,
        desc,
        emulator,
      },
      headers: { 'Content-Type': 'multipart/form-data' },
    })
    .then(() => {
      loading.value = false
      notify('保存成功', 'positive')
      onDialogOK()
    })
    .catch(error => {
      loading.value = false
      notify(error.code == 'object_exists' ? '名称已存在' : '保存失败')
    })
}
</script>

<style lang="scss" scoped>
.dialog-card {
  width: 600px;
  padding: 80px 100px;
}
</style>
