<template>
  <div class="flex items-center">
    <q-btn to="/training" flat>
      <SvgBack />
      <div class="fs-18 q-ml-xs">返回</div>
    </q-btn>
    <q-separator color="primary" size="3px" class="rounded-borders q-mx-md" inset vertical />
    <div class="fs-20 text-bold text-primary">{{ detail.name }}</div>
  </div>

  <div class="flex q-gutter-sm q-mt-xs">
    <div class="item-card flex-grow">
      <div class="fs-16 text-primary">基础信息</div>

      <div class="label">开始时间</div>
      <div>{{ detail.startTime }}</div>

      <template v-if="detail.endTime">
        <div class="label">结束时间</div>
        <div>{{ detail.endTime }}</div>
      </template>

      <div class="label">训练时长</div>
      <div>{{ detail.runningTime }}</div>

      <div class="label">训练状态</div>
      <div :class="`text-${trainingStatus[detail.status]?.color}`">
        {{ trainingStatus[detail.status]?.label }}
      </div>
    </div>

    <div class="item-card flex-grow">
      <div class="fs-16 text-primary">基础设置</div>

      <div class="label">训练名称</div>
      <div>{{ detail.name }}</div>

      <div class="label">仿真器名称</div>
      <div>{{ detail.emulator || '海军军事演练仿真器' }}</div>

      <div class="label">想定名称</div>
      <div>{{ detail.scenario }}</div>
    </div>

    <div class="item-card flex-grow">
      <div class="fs-16 text-primary">训练资源</div>

      <div class="row no-wrap text-no-wrap q-gutter-x-xl">
        <div>
          <div class="label">Actor</div>
          <div>{{ detail.actorNum }}</div>

          <div class="label">每个Actor配置资源数量</div>
          <div class="q-mt-md">
            <span>CPU</span>
            <span class="q-ml-md">{{ detail.actorPerCpu }}</span>
          </div>
          <div class="q-mt-md">
            <span>GPU</span>
            <span class="q-ml-md">{{ detail.actorPerGpu }}</span>
          </div>
          <div class="q-mt-md">
            <span>内存</span>
            <span class="q-ml-md">{{ detail.actorPerMemory }}G</span>
          </div>
        </div>

        <div>
          <div class="label">Learner</div>
          <div>{{ detail.learnerNum }}</div>

          <div class="label">每个Learner配置资源数量</div>
          <div class="q-mt-md">
            <span>CPU</span>
            <span class="q-ml-md">{{ detail.learnerPerCpu }}</span>
          </div>
          <div class="q-mt-md">
            <span>GPU</span>
            <span class="q-ml-md">{{ detail.learnerPerGpu }}</span>
          </div>
          <div class="q-mt-md">
            <span>内存</span>
            <span class="q-ml-md">{{ detail.learnerPerMemory }}G</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row q-gutter-sm q-mt-sm">
    <div class="item-card">
      <div class="flex items-center">
        <div class="fs-16 text-primary">学习状态</div>
        <div class="fs-12 text-grey q-ml-sm">上次更新时间：{{ detail?.rewardInfoTime }}</div>
      </div>
      <div class="label">当前算法名称</div>
      <div>{{ detail?.rewardInfo?.algo_name }}</div>

      <div class="label">奖赏值（Reward）</div>
      <div>{{ getLatestReward(detail?.rewards) }}</div>

      <div class="label">训练步数</div>
      <div>{{ getLatestStep(detail?.steps || []) }} / {{ detail.maxTrainingStep }}</div>

      <!-- <div class="label">训练速度</div>
      <div>Learner: {{ detail.learnerSpeed?.toFixed(2) }} fps</div>
      <div>Actor: &nbsp; &nbsp; {{ detail.actorSpeed?.toFixed(2) }} fps</div> -->
    </div>

    <div class="col item-card column">
      <div class="flex justify-between">
        <div class="fs-16 text-primary">算法性能曲线</div>
        <q-btn v-if="detail.tbUrl" :to="detail.tbUrl" target="_blank" label="Tensorboard" color="primary" unelevated />
      </div>
      <RewardCard :data="detail.rewards" :steps="detail.steps" class="col" />
      <!-- <div v-else class="text-grey text-center q-mt-xl">暂无数据</div> -->
    </div>
  </div>

  <div class="q-mt-sm item-card model-card">
    <ModelCard :id="id" :rows="detail.models" />
  </div>

  <q-inner-loading :showing="loading" />
</template>

<script setup>
import { ref } from 'vue'
import { date } from 'quasar'
import { api } from 'boot/axios'
import RewardCard from './RewardCard.vue'
import ModelCard from './ModelCard.vue'
import { trainingStatus } from 'assets/const'
import { convertSecondsToHMS } from 'assets/utils'
import SvgBack from 'assets/images/svg/back.svg'

const props = defineProps(['id'])

const loading = ref(false)
const detail = ref({})

getDetail()

function getLatestReward(rewards = []) {
  if (!rewards?.length) return '29.48'
  return rewards[rewards.length - 1]?.toFixed(2) || '0'
}

function getLatestStep(steps = []) {
  if (!steps?.length) return '100000'
  return steps[steps.length - 1] || '0'
}

function getDetail() {
  loading.value = true
  api
    .get(`backend/tasks/${props.id}/`)
    .then(res => {
      loading.value = false
      console.log(res.data)
      const {
        start_time: startTime,
        end_time: endTime,
        running_time: runningTime,
        status,

        name,
        emulator,
        scenario_name: scenario,

        actor_num: actorNum,
        actor_per_cpu: actorPerCpu,
        actor_per_gpu: actorPerGpu,
        actor_per_memory: actorPerMemory,

        learner_num: learnerNum,
        learner_per_cpu: learnerPerCpu,
        learner_per_gpu: learnerPerGpu,
        learner_per_memory: learnerPerMemory,
        infrl_training_config: infrlTrainingConfig,

        reward_info: rewardInfo,
        rewards,
        models,
        tb_url: tbUrl,
      } = res.data

      let trainingConfig = JSON.parse(infrlTrainingConfig)
      detail.value = {
        startTime: date.formatDate(startTime, 'YYYY-MM-DD HH:mm:ss'),
        endTime: date.formatDate(endTime, 'YYYY-MM-DD HH:mm:ss'),
        runningTime: convertSecondsToHMS(runningTime),
        status,

        name,
        emulator,
        scenario,

        actorNum,
        actorPerCpu,
        actorPerGpu,
        actorPerMemory,

        learnerNum,
        learnerPerCpu,
        learnerPerGpu,
        learnerPerMemory,

        rewardInfo,
        rewardInfoTime: date.formatDate(rewardInfo?.time, 'MM-DD HH:mm:ss'),
        rewards: rewards.rewards,
        steps: rewards.steps,
        models,
        maxTrainingStep: trainingConfig?.max_training_step || 100000,
        tbUrl: tbUrl?.node_port ? `//${import.meta.env.VITE_HOST || location.hostname}:${tbUrl.node_port}` : null,
      }
      console.log(detail.value)
    })
    .catch(() => {
      loading.value = false
    })
}
</script>

<style lang="scss" scoped>
.item-card {
  padding: 20px;
  font-size: 18px;
  font-weight: bold;
  background: rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(15px);

  .label {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.8);
    margin-top: 25px;
    font-weight: normal;
  }
}

.model-card {
  height: 500px;
}

.body--dark {
  .item-card {
    background-color: rgba(255, 255, 255, 0.1);

    .label {
      color: rgba(255, 255, 255, 0.5);
    }
  }
}
</style>
