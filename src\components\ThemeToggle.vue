<template>
  <q-toggle
    :model-value="dark.isActive"
    @update:model-value="toggleDarkMode"
    checked-icon="dark_mode"
    unchecked-icon="light_mode"
  >
    <q-tooltip>切换到{{ dark.isActive ? '浅色' : '深色' }}主题</q-tooltip>
  </q-toggle>
</template>

<script setup>
import { usePlugin } from 'composables/plugin'

const { dark, localStorage } = usePlugin()

function toggleDarkMode() {
  dark.toggle()
  localStorage.set('theme', dark.isActive ? 1 : 0)
}
</script>
