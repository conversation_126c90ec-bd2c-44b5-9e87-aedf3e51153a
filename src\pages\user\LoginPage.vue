<template>
  <!-- <q-page class="row items-center justify-evenly bg-white" style="height: 100vh"> -->
  <div class="flex flex-center window-height" style="gap: 48px;">
    <!-- Left Side -->
    <div class="rounded-borders shadow-2 flex flex-center" style="width: 40%; min-width: 480px; min-height: 480px;">
      <q-img
        :src="smartModelImage"
        style="width: 100%; height: 100%; object-fit: contain;"
        contain
      />
    </div>

    <!-- Right Side Login Form -->
    <q-card flat bordered class="q-pa-lg shadow-2" style="width: 40%; max-width: 500px">
      <q-card-section class="text-center">
        <div class="labelColor">登录</div>
      </q-card-section>

      <q-card-section>
        <q-input v-model="username" :label="username?'':'账号'" outlined class="q-mb-md" />
        <q-input v-model="password" :label="password?'':'密码'" type="password" outlined class="q-mb-md" />

        <div class="row items-center q-mb-md">
          <q-checkbox v-model="remember" label="记住密码" class="font16" />
          <q-space />
          <q-btn flat label="账号注册" size="sm" @click="register" class="text-primary labelColor font16" />
          <q-btn flat label="忘记密码" size="sm" @click="forgotPassword" class="text-primary labelColor font16" />
        </div>

        <q-btn :loading="loading" label="登录" color="primary" class="full-width" @click="login" />
      </q-card-section>
    </q-card>
  </div>
  <!-- </q-page> -->
</template>

<script setup>
import { ref, } from 'vue'
import { useRouter } from 'vue-router'
import { api } from 'boot/axios'
import { usePlugin } from 'composables/plugin.js'
import { useUserStore } from '../../stores/userStore.js'
import{storeToRefs} from 'pinia'

const { notify, localStorage } = usePlugin()
const router = useRouter()

// 在Vite中使用相对路径引入图片
const smartModelImage = '/src/assets/images/smart_model_image.png'

const username = ref('')
const password = ref('')
const remember = ref(false)
const loading = ref(false)

const userStore = useUserStore()
let {userAvatar} = storeToRefs(userStore)

function login() {
  // Add your login logic here
  console.log('Logging in with:', username.value, password.value, remember.value)

  api
    .post('user/login/', {
      username: username.value,
      password: password.value,
    })
    .then(res => {
      loading.value = false
      localStorage.set('token', res.access)
      localStorage.set('username', res.user)
      notify('登录成功', 'positive')
      router.push('/')
      console.log('ravatar_url',res.avatar_url)
      userStore.changeUserAvatar(res.avatar_url)
      console.log("userAvatar",userAvatar.value)
    })
    .catch(() => {
      loading.value = false
      notify('账号或密码错误')
    })
}

function register() {
  // Navigate to registration page
  router.push('/user/register')
}

function forgotPassword() {
  // Navigate to forgot password page
  router.push('/user/forgot-password')
}
</script>

<style scoped>
.text-primary {
  color: #409eff;
}
.font16{
  font-size: .15rem !important;
}
:deep(.q-field){
  font-size: .175rem !important;
}
</style>
