<template>
  <div v-if="detail" class="q-mb-md">
    <ListItem :list="[detail]" />
  </div>

  <div class="q-pa-md code-card">
    <div class="flex items-center">
      <q-btn :to="route" flat>
        <SvgBack />
        <div class="fs-18 q-ml-xs">返回</div>
      </q-btn>

      <template v-if="mode != 2">
        <q-separator color="primary" size="3px" class="rounded-borders q-mx-md" inset vertical />
        <div class="fs-16 text-primary">{{ ['', '新增', '', '衍生新', '编辑'][mode] + name }}</div>
      </template>

      <q-space />
      <template v-if="mode == 1">
        <q-btn
          @click="onSubmit"
          :loading="saveLoading"
          label="保存"
          icon="save"
          color="primary"
          text-color="black"
          padding="8px 40px"
          class="skew-both"
        />
      </template>
      <template v-else-if="mode == 2">
        <q-btn
          @click="onEdit(3)"
          :label="`衍生新${name}`"
          icon="add"
          color="primary"
          text-color="black"
          padding="8px 40px"
          class="skew-both"
        />

        <q-btn
          @click="onEdit(4)"
          :label="`编辑${name}`"
          icon="edit"
          color="primary"
          text-color="black"
          padding="8px 40px"
          class="skew-both"
        />
      </template>
      <template v-else>
        <q-btn @click="mode = 2" label="取消" color="grey-9" />
        <q-btn
          @click="onSubmit"
          :loading="saveLoading"
          label="保存"
          color="primary"
          text-color="black"
          class="q-ml-sm"
        />
      </template>
    </div>

    <q-form ref="form" v-if="mode != 2" class="row q-gutter-x-md q-mt-md">
      <div class="col-4">
        <div class="text-bold q-mb-sm require">{{ name }}名称</div>
        <q-input
          v-model.trim="formData.name"
          :rules="[val => !!val || `请输入${name}名称`]"
          :placeholder="`请输入${name}名称`"
          maxlength="20"
          lazy-rules
          outlined
        />
      </div>

      <div class="col">
        <div class="text-bold q-mb-sm">{{ name }}描述</div>
        <q-input v-model.trim="formData.desc" :placeholder="`请输入${name}描述`" maxlength="200" lazy-rules outlined />
      </div>

      <div class="row">
        <div class="col-4">
          <div class="text-bold q-mb-sm require" style="font-size: large;">智能体类型</div>
          <q-select
            v-model="formData.agent_type"
            :options="agentTypeList"
            option-value="id"
            option-label="name"
            :rules="[val => !!val || '请选择智能体类型']"
            placeholder="智能体类型"
            emit-value
            map-options
            outlined
            lazy-rules
          >
            <template v-slot:no-option>
              <div class="text-grey q-pa-sm">暂无可用的智能体类型</div>
            </template>
          </q-select>
        </div>
      </div>
    </q-form>

    <MonacoEditor @shown="init" ref="editor" language="python" class="q-mt-md editor" />

    <q-inner-loading :showing="loading" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { api } from 'boot/axios'
import { usePlugin } from 'composables/plugin'
import ListItem from 'components/ListItem.vue'
import MonacoEditor from 'components/MonacoEditor.vue'
import SvgBack from 'assets/images/svg/back.svg'

const props = defineProps(['id', 'name', 'apiUrl', 'route', 'defaultCodeUrl'])

const router = useRouter()
const { notify } = usePlugin()

const form = ref(null)
const editor = ref(null)

const mode = ref(props.id ? 2 : 1) //1:新增 2:详情 3:衍生 4:编辑
const loading = ref(false)
const saveLoading = ref(false)
const detail = ref(null)
const formData = ref({
  name: '',
  desc: '',
  agent_type: null  // 添加智能体类型字段
})

// 添加智能体类型列表
const agentTypeList = ref([
  { id: 1, name: 'DQN' },
  { id: 2, name: 'PPO' },
  { id: 3, name: 'SAC' },
  { id: 4, name: 'DDPG' }
])

function init() {
  if (props.id) {
    loading.value = true
    api
      .get(`${props.apiUrl + props.id}/`)
      .then(async res => {
        loading.value = false

        let { name, desc, file: fileUrl } = res

        detail.value = { name, desc }

        if (process.env.PROD) {
          const temp = fileUrl.split('common/')

          if (temp.length > 1) {
            fileUrl = `/common/${temp[1]}`
          }
        }

        editor.value.setValueFromUrl(fileUrl)
      })
      .catch(() => {
        loading.value = false
      })
  } else if (props.defaultCodeUrl) {
    // editor.value.setValueFromUrl(props.defaultCodeUrl)
  }
}

function onEdit(targetMode) {
  mode.value = targetMode
  formData.value = {
    name: targetMode == 3 ? '' : detail.value?.name,
    desc: targetMode == 3 ? '' : detail.value?.desc,
    agent_type: targetMode == 3 ? null : detail.value?.agent_type
  }
}

function onSubmit() {
  form.value.validate().then(success => {
    if (success) {
      saveLoading.value = true

      const { name, desc = '' } = formData.value
      const file = new File([editor.value.getValue()], `${name}.py`)
      const url = mode.value == 4 ? props.apiUrl + `${props.id}/` : props.apiUrl
      const method = mode.value == 4 ? 'patch' : 'post'

      api
        .request({
          url,
          method,
          data: {
            name,
            desc,
            file,
            agent_type: formData.value.agent_type
          },
          headers: { 'Content-Type': 'multipart/form-data' },
        })
        .then(() => {
          saveLoading.value = false
          notify('保存成功', 'positive')
          router.push(props.route)
        })
        .catch(error => {
          saveLoading.value = false
          notify(error.code == 'object_exists' ? '名称已存在' : '保存失败')
        })
    }
  })
}
</script>

<style lang="scss" scoped>
.code-card {
  background-color: rgba(0, 0, 0, 0.08);

  .editor {
    height: 600px;
  }
}

.body--dark {
  .code-card {
    background-color: rgba(255, 255, 255, 0.1);
  }
}
</style>
