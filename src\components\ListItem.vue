<template>
  <div
    v-for="(item, index) in list"
    @click="$emit('item:click', item.id)"
    :key="index"
    class="flex no-wrap q-mb-md cursor-pointer list-item"
  >
    <div class="text-no-wrap text-bold text-roboto fs-40 q-px-xl q-py-lg">{{ item.name }}</div>
    <div class="col relative-position desc">
      <div class="absolute-full flex items-center">
        <div class="q-pl-xl q-pr-lg ellipsis-3-lines">
          <div>{{ item.desc }}</div>
          <q-tooltip>{{ item.desc }}</q-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps(['list'])

defineEmits(['item:click'])
</script>

<style lang="scss" scoped>
.list-item {
  background-color: rgba(0, 0, 0, 0.08);
  border-left: 2px solid $primary;
  backdrop-filter: blur(15px);

  .desc {
    color: rgba(0, 0, 0, 0.6);
    background-color: rgba(0, 0, 0, 0.06);
    clip-path: polygon(0 100%, 40px 0, 100% 0, 100% 100%, 0 100%);
  }

  &:hover {
    background-color: rgba(255, 144, 0, 0.3);
  }
}

.body--dark {
  .list-item {
    background-color: rgba(255, 255, 255, 0.2);

    .desc {
      color: rgba(255, 255, 255, 0.6);
      background-color: rgba(0, 0, 0, 0.5);
    }
  }
}
</style>
