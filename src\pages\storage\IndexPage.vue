<template>
  <TableCard
    :columns="columns"
    :params="{ url: path }"
    :rows-per-page="0"
    title="文件列表"
    api-url="backend/storages/list/"
    result-key="data.files"
    ref="table"
  >
    <template #action>
      <q-btn
        @click="addFolder"
        label="新建文件夹"
        icon="add"
        color="primary"
        text-color="black"
        padding="8px 40px"
        class="skew-both"
      />

      <q-btn
        @click="$refs.fileInput.click()"
        :loading="uploadLoading"
        label="上传文件"
        icon="upload"
        color="primary"
        text-color="black"
        padding="8px 40px"
        class="skew-both"
      >
        <input ref="fileInput" @change="upload" type="file" hidden multiple />
      </q-btn>
    </template>

    <template #header>
      <q-breadcrumbs class="q-mb-sm">
        <q-breadcrumbs-el @click="changePath('/', true)" :label="`${home}`" class="cursor-pointer" />
        <q-breadcrumbs-el
          v-for="(item, idx) in breadcrumbs"
          @click="changePath(item.path, true)"
          :label="item.label"
          :key="idx"
          class="cursor-pointer"
        />
      </q-breadcrumbs>
    </template>

    <template #body-cell-name="props">
      <q-td
        @click="changePath(props.row.path, props.row.is_dir)"
        :props="props"
        :class="props.row.is_dir ? 'cursor-pointer' : ''"
      >
        <q-icon :name="props.row.is_dir ? 'folder' : 'description'" color="primary" />
        {{ props.row.name }}
      </q-td>
    </template>

    <template #body-cell-action="props">
      <q-td :props="props">
        <q-btn @click="downloadFile(props.row)" label="下载" color="primary" flat dense />
        <q-btn @click="onDelete(props.row.path)" label="删除" color="primary" class="q-ml-md" flat dense />
      </q-td>
    </template>
  </TableCard>
</template>

<script setup>
import { ref, computed } from 'vue'
import { date, exportFile } from 'quasar'
import { api, axios } from 'boot/axios'
import { usePlugin } from 'composables/plugin'
import TableCard from 'components/TableCard.vue'
import { formatFileSize } from 'assets/utils'

const { dialog, notify, localStorage } = usePlugin()

const home = '/home/' + localStorage.getItem('username') || ''

const columns = [
  {
    name: 'name',
    label: '文件名',
    align: 'left',
    slot: true,
  },
  {
    name: 'type',
    field: 'is_dir',
    format: val => (val ? '文件夹' : '文件'),
    label: '文件类型',
    align: 'center',
  },
  {
    name: 'size',
    field: 'size',
    format: (val, row) => (row.is_dir ? '-' : formatFileSize(val)),
    label: '文件大小',
    align: 'center',
  },
  {
    name: 'createTime',
    field: 'create_time',
    sortable: true,
    format: val => date.formatDate(val, 'YYYY-MM-DD HH:mm:ss'),
    label: '创建时间',
    align: 'center',
  },
  {
    name: 'updateTime',
    field: 'update_time',
    sortable: true,
    format: val => date.formatDate(val, 'YYYY-MM-DD HH:mm:ss'),
    label: '修改时间',
    align: 'center',
  },
  {
    name: 'action',
    label: '操作栏',
    align: 'center',
    slot: true,
  },
]

const table = ref(null)
const path = ref('/')
const uploadLoading = ref(false)
const breadcrumbs = computed(() => {
  let arr = path.value.split('/').filter(item => item)

  return arr.map((item, idx) => {
    return {
      label: item,
      path: `/${arr.slice(0, idx + 1).join('/')}`,
    }
  })
})

function addFolder() {
  dialog('请输入文件夹名称', {
    title: '新建文件夹',
    prompt: {
      name: '',
      isValid: val => !!val,
      type: 'text',
    },
    cancel: true,
    persistent: true,
  }).onOk(folderName => {
    api
      .post(`backend/storages/createdir/`, {
        url: path.value,
        dir: folderName,
      })
      .then(() => {
        notify('新建成功', 'positive')
        table.value.getRows()
      })
      .catch(() => {
        notify('新建失败')
      })
  })
}

function downloadFile(row) {
  api
    .post(
      `backend/storages/download/`,
      {
        url: row.path,
      },
      {
        responseType: 'blob',
      }
    )
    .then(res => {
      exportFile(row.name + (row.is_dir ? '.zip' : ''), res)
    })
    .catch(error => {
      const reader = new FileReader()

      reader.onloadend = function () {
        const res = JSON.parse(reader.result)
        notify(res.code == 'empty_dir_delete_failed' ? '空文件夹不支持下载' : '下载失败')
      }

      reader.readAsText(error)
    })
}

function upload(e) {
  const files = e.target.files
  if (!files.length) {
    return
  }

  uploadLoading.value = true

  axios
    .all(
      new Array(files.length).fill(null).map((_, idx) => {
        return api.post(
          'backend/storages/upload/',
          {
            files: files[idx],
            url: path.value,
          },
          {
            headers: { 'Content-Type': 'multipart/form-data' },
          }
        )
      })
    )
    .then(() => {
      uploadLoading.value = false
      notify('上传成功', 'positive')
      table.value.getRows()
    })
    .catch(error => {
      uploadLoading.value = false
      notify('上传失败')
    })
}

function onDelete(path) {
  dialog('确认删除吗？').onOk(() => {
    api
      .post(`backend/storages/delete/`, {
        url: path,
      })
      .then(() => {
        notify('删除成功', 'positive')
        table.value.getRows()
      })
      .catch(() => {
        notify('删除失败')
      })
  })
}

function changePath(target, is_dir) {
  if (!is_dir) {
    return
  }

  path.value = target
}
</script>
