<template>
  <TableCard :columns="columns" :rows="rows" :rows-per-page="0" hide-top>
    <template #body-cell-action="props">
      <q-td :props="props">
        <q-btn
          @click="addModel(props.row)"
          :loading="props.row.loading"
          label="添加至模型管理"
          color="info"
          flat
          dense
        />
      </q-td>
    </template>
  </TableCard>
</template>

<script setup>
import { date } from 'quasar'
import { api } from 'boot/axios'
import { usePlugin } from 'composables/plugin'
import TableCard from 'components/TableCard.vue'
import { formatFileSize } from 'assets/utils'

const props = defineProps({
  id: String,
  rows: {
    type: Array,
    default: () => [],
  },
})

const { notify } = usePlugin()

const columns = [
  {
    name: 'file',
    field: 'file',
    label: '模型名称',
    align: 'center',
  },
  {
    name: 'steps',
    field: 'steps',
    label: '训练局数',
    align: 'center',
  },
  {
    name: 'size',
    field: 'size',
    format: val => formatFileSize(val),
    label: '文件大小',
    align: 'center',
  },
  {
    name: 'createTime',
    field: 'create_time',
    format: val => date.formatDate(val, 'YYYY-MM-DD HH:mm:ss'),
    label: '生成时间',
    align: 'center',
  },
  {
    name: 'action',
    label: '操作栏',
    align: 'center',
    slot: true,
  },
]

function addModel(row) {
  row.loading = true
  api
    .post('backend/models/', {
      name: row.file,
      file: row.file,
      task: props.id,
    })
    .then(() => {
      row.loading = false
      notify('添加成功', 'positive')
    })
    .catch(() => {
      row.loading = false
      notify('添加失败')
    })
}
</script>
