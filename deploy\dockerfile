FROM node:18-alpine as build

ARG MODE='prd'

COPY . /app/
RUN mv /app/.env.${MODE} /app/.env
RUN ls

WORKDIR /app/
RUN npm config set registry https://registry.npmmirror.com/
RUN npm install -g pnpm
RUN pnpm config set registry https://registry.npmmirror.com/
RUN pnpm install
RUN pnpm run build 

FROM nginx:latest

COPY --from=build /app/dist/spa /usr/share/nginx/html/spa

EXPOSE 5173

COPY ./deploy/nginx.conf /etc/nginx/conf.d/default.conf

CMD ["nginx", "-g", "daemon off;"]