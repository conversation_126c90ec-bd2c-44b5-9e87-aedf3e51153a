<template>
  <div class="flex flex-center window-height" style="gap: 48px;">
    <!-- Left Side -->
    <div class="rounded-borders shadow-2 flex flex-center" style="width: 28%; min-width: 480px; min-height: 480px;">
      <q-img
        :src="smartModelImage"
        style="width: 100%; height: 100%; object-fit: contain;"
        contain
      />
    </div>

    <!-- Right Side Register Form -->
    <q-card flat bordered class="q-pa-lg shadow-2" style="width: 30%; max-width: 500px">
      <q-card-section class="text-center">
        <div class="text-h6">注册</div>
      </q-card-section>

      <q-card-section>
        <q-input v-model="username" label="账号" outlined class="q-mb-sm" />
        <q-input v-model="realName" label="姓名" outlined class="q-mb-sm" />
        <q-input v-model="idCard" label="身份证" outlined class="q-mb-sm"
                mask="##################" :rules="[val => val.length === 18 || '身份证号必须为18位']" />
        <q-input v-model="company" label="单位" outlined class="q-mb-sm" />
        <q-input v-model="phone" label="电话" outlined class="q-mb-sm"
                mask="###########" :rules="[val => val.length === 11 || '手机号必须为11位']" />
        <q-input v-model="password" label="密码" type="password" outlined class="q-mb-sm" />
        <q-input v-model="confirmPassword" label="确认密码" type="password" outlined class="q-mb-sm" />

        <div class="row items-center q-mb-sm">
          <q-checkbox v-model="agreement" label="我已阅读并同意服务条款" />
          <q-space />
          <q-btn flat label="返回登录" size="sm" @click="backToLogin" class="text-primary" />
        </div>

        <q-btn :loading="loading" label="注册" color="primary" class="full-width" @click="register" />
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { api } from 'boot/axios'
import { usePlugin } from 'composables/plugin.js'

const { notify } = usePlugin()
const router = useRouter()

import smartModelImage from '../../assets/images/smart_model_image.png'

const username = ref('')
const realName = ref('')
const idCard = ref('')
const company = ref('')
const phone = ref('')
const password = ref('')
const confirmPassword = ref('')
const agreement = ref(false)
const loading = ref(false)

function register() {
  if (!username.value || !realName.value || !idCard.value || !company.value ||
      !phone.value || !password.value || !confirmPassword.value) {
    notify('请填写完整信息')
    return
  }

  if (idCard.value.length !== 18) {
    notify('请输入正确的身份证号码')
    return
  }

  if (phone.value.length !== 11) {
    notify('请输入正确的手机号码')
    return
  }

  if (password.value !== confirmPassword.value) {
    notify('两次输入的密码不一致')
    return
  }

  if (!agreement.value) {
    notify('请阅读并同意服务条款')
    return
  }

  loading.value = true
  api
    .post('user/register/', {
      username: username.value,
      real_name: realName.value,
      id_card: idCard.value,
      company: company.value,
      phone: phone.value,
      password: password.value,
    })
    .then(() => {
      loading.value = false
      notify('注册成功，请登录', 'positive')
      router.push('/user/login')
    })
    .catch(error => {
      loading.value = false
      notify(error.response?.data?.msg || '注册失败，请稍后重试')
    })
}

function backToLogin() {
  router.push('/user/login')
}
</script>

<style scoped>
.text-primary {
  color: #409eff;
}
</style>
