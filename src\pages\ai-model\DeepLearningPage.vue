<template>
  <div class="q-pa-md">
    <!-- 面包屑导航 -->
    <!-- <div class="text-caption q-mb-md">
      <q-breadcrumbs>
        <q-breadcrumbs-el label="算法模型" />
        <q-breadcrumbs-el label="深度学习" />
      </q-breadcrumbs>
    </div> -->

    <!-- 训练算法编辑 -->
    <q-card flat bordered class="q-mb-md">
      <q-card-section>
        <div class=" q-mb-md labelColor font24">训练算法编辑</div>
        <div class="row q-col-gutter-md items-center">
          <!-- <div class="col-1">
            <div class=" labelColor">版本选择</div>
          </div> -->
          <!-- <div class="col-2">
            <q-select outlined dense v-model="algorithm.version" :options="algorithmVersions" />
          </div> -->
          <div class="col-1">
            <div class=" labelColor font16 textRight">模型选择</div>
          </div>
          <div class="col-5">
            <q-select outlined dense v-model="algorithm.modelPath" :options="modelOptions"
              :label="algorithm.modelPath ? '' : '请选择模型'" @update:model-value="onModelChange" />
          </div>
          <!-- <div class="col-2">
            <q-btn flat label="编辑神经网络" color="primary" class="q-mr-sm" />
          </div> -->
        </div>
      </q-card-section>
    </q-card>

    <!-- 训练数据设置 -->
    <q-card flat bordered class="q-mb-md">
      <q-card-section>
        <div class=" q-mb-md labelColor font24">训练数据设置</div>
        <div class="row q-col-gutter-md items-center">
          <div class="col-1">
            <div class=" labelColor font16 textRight">数据集选择</div>
          </div>
          <div class="col-5">
            <q-select outlined dense v-model="training.dataset" :options="datasetOptions" :disable="disableDataset"
              :label="training.dataset ? '' : '请选择数据集'" option-label="label" option-value="value" />
          </div>
          <div class="col-1">
            <div class=" labelColor font16 textRight">验证集比例</div>
          </div>
          <div class="col-2">
            <div class="row items-center customAdd">
              <q-btn flat dense icon="remove" size="sm" @click="decreaseValidationRatio" />
              <div class="q-mx-md">{{ training.validationRatio }}</div>
              <q-btn flat dense icon="add" size="sm" @click="increaseValidationRatio" />
            </div>
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- 训练资源设置 -->
    <q-card flat bordered class="q-mb-md">
      <q-card-section>
        <div class=" q-mb-md labelColor font24">训练资源设置</div>
        <div class="row q-col-gutter-md items-center">
          <div class="col-1">
            <div class=" labelColor font16 textRight">CPU个数</div>
          </div>
          <div class="col-2">
            <q-input outlined dense v-model="resources.cpuCount" placeholder="请输入" />
          </div>
          <div class="col-1">
            <div class="labelColor font16 textRight">NPU个数</div>
          </div>
          <div class="col-2">
            <q-input outlined dense v-model="resources.npuCount" placeholder="请输入" />
          </div>
          <div class="col-1">
            <div class=" labelColor font16 textRight">存储资源大小</div>
          </div>
          <div class="col-2 test">
            <q-input outlined dense v-model="resources.storageSize" placeholder="请输入" suffix="GB" />
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- 训练参数设置 -->
    <q-card flat bordered class="q-mb-md">
      <q-card-section>
        <div class="row justify-between items-center">
          <div class=" labelColor font24">训练参数设置</div>
          <q-btn class="labelColor" flat round icon="keyboard_arrow_down" @click="showTrainParams = !showTrainParams" />
        </div>

        <div v-show="showTrainParams">
          <div class="row q-col-gutter-md q-mt-md items-center">
            <div class="col-1">
              <div class=" labelColor font16 textRight">学习率</div>
            </div>
            <div class="col-2">
              <q-input outlined dense v-model="parameters.learningRate" placeholder="1e-5" />
            </div>
            <div class="col-1">
              <div class=" labelColor font16 textRight">训练轮数</div>
            </div>
            <div class="col-2">
              <q-input outlined dense v-model="parameters.epochs" placeholder="5" />
            </div>
            <div class="col-1">
              <div class="labelColor font16 textRight">梯度范数</div>
            </div>
            <div class="col-2">
              <q-input outlined dense v-model="parameters.maxGradNorm" placeholder="1" />
            </div>
            <div class="col-1">
              <div class=" labelColor font16 textRight">最大样本数</div>
            </div>
            <div class="col-2">
              <q-input outlined dense v-model="parameters.maxSamples" placeholder="100000" />
            </div>
          </div>

          <div class="row q-col-gutter-md q-mt-sm items-center">
            <div class="col-1">
              <div class=" labelColor font16 textRight">梯度累计</div>
            </div>
            <div class="col-2">
              <q-input outlined dense v-model="parameters.gradAccumulation" placeholder="1e-5" />
            </div>
            <div class="col-1">
              <div class=" labelColor font16 textRight">批处理大小</div>
            </div>
            <div class="col-2">
              <q-input outlined dense v-model="parameters.batchSize" placeholder="5" />
            </div>
            <div class="col-1">
              <div class=" labelColor font16 textRight">学习率策略</div>
            </div>
            <div class="col-2">
              <q-select outlined dense v-model="parameters.learningRateStrategy" :options="learningRateStrategies" />
            </div>
            <div class="col-1">
              <div class="labelColor font16 textRight">计算类型</div>
            </div>
            <div class="col-2">
              <q-select outlined dense v-model="parameters.computeType" :options="computeTypes" />
            </div>
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- 其他参数设置 -->
    <q-card flat bordered class="q-mb-md">
      <q-card-section>
        <div class="row justify-between items-center">
          <div class=" labelColor font24">其他参数设置</div>
          <q-btn class="labelColor" flat round icon="keyboard_arrow_down" v-model="showOtherParams"
            @click="showOtherParams = !showOtherParams" />
        </div>

        <div v-show="showOtherParams">
          <!-- 优化器设置 -->
          <div class="q-mt-md q-mb-sm labelColor font20 ">优化器设置</div>
          <div class="row q-col-gutter-md items-center">
            <div class="col-1">
              <div class=" labelColor font16 textRight">优化器</div>
            </div>
            <div class="col-2">
              <q-select outlined dense v-model="otherParams.optimizer" :options="optimizerOptions" />
            </div>
            <div class="col-1">
              <div class="labelColor font16 textRight">动量</div>
            </div>
            <div class="col-2">
              <q-input outlined dense v-model="otherParams.momentum" placeholder="0.9" />
            </div>
            <div class="col-1">
              <div class=" labelColor font16 textRight">权重衰减</div>
            </div>
            <div class="col-2">
              <q-input outlined dense v-model="otherParams.weightDecay" placeholder="1e-4" />
            </div>
            <div class="col-1">
              <div class=" labelColor font16 textRight">epsilon</div>
            </div>
            <div class="col-2">
              <q-input outlined dense v-model="otherParams.epsilon" placeholder="1e-8" />
            </div>
          </div>

          <!-- 正则化参数 -->
          <div class="q-mt-md q-mb-sm labelColor font20">正则化参数</div>
          <div class="row q-col-gutter-md items-center">
            <div class="col-1">
              <div class=" labelColor font16 textRight">Dropout率</div>
            </div>
            <div class="col-2">
              <q-input outlined dense v-model="otherParams.dropout" placeholder="0.1" />
            </div>
            <div class="col-1">
              <div class=" labelColor font16 textRight">Label平滑</div>
            </div>
            <div class="col-2">
              <q-input outlined dense v-model="otherParams.labelSmoothing" placeholder="0.1" />
            </div>
            <div class="col-1">
              <div class=" labelColor font16 textRight">梯度裁剪</div>
            </div>
            <div class="col-2">
              <q-toggle v-model="otherParams.useGradientClipping" />
            </div>
            <div class="col-1">
              <div class="labelColor font16 textRight">混合精度</div>
            </div>
            <div class="col-2">
              <q-toggle v-model="otherParams.useMixedPrecision" />
            </div>
          </div>

          <!-- 训练控制 -->
          <div class="q-mt-md q-mb-sm labelColor font20">训练控制</div>
          <div class="row q-col-gutter-md items-center">
            <div class="col-1">
              <div class=" labelColor font16 textRight">早停耐心</div>
            </div>
            <div class="col-2">
              <q-input outlined dense v-model="otherParams.earlyStopping" placeholder="5" type="number" />
            </div>
            <div class="col-1">
              <div class=" labelColor font16 textRight">保存频率</div>
            </div>
            <div class="col-2">
              <q-input outlined dense v-model="otherParams.checkpointFreq" placeholder="10" suffix="轮" />
            </div>
            <div class="col-1">
              <div class=" labelColor font16 textRight">预热步数</div>
            </div>
            <div class="col-2">
              <q-input outlined dense v-model="otherParams.warmupSteps" placeholder="1000" />
            </div>
            <div class="col-1">
              <div class="labelColor font16 textRight">日志频率</div>
            </div>
            <div class="col-2">
              <q-input outlined dense v-model="otherParams.logFreq" placeholder="100" suffix="步" />
            </div>
          </div>

          <!-- 模型特定参数 -->
          <div class="q-mt-md q-mb-sm labelColor font20">模型特定参数</div>
          <div class="row q-col-gutter-md items-center">
            <div class="col-1">
              <div class=" labelColor font16 textRight">激活函数</div>
            </div>
            <div class="col-2">
              <q-select outlined dense v-model="otherParams.activation" :options="activationOptions" />
            </div>
            <div class="col-1">
              <div class=" labelColor font16 textRight">初始化</div>
            </div>
            <div class="col-2">
              <q-select outlined dense v-model="otherParams.initialization" :options="initOptions" />
            </div>
            <div class="col-1">
              <div class=" labelColor font16 textRight">归一化</div>
            </div>
            <div class="col-2">
              <q-select outlined dense v-model="otherParams.normalization" :options="normOptions" />
            </div>
            <div class="col-1">
              <div class=" labelColor font16 textRight">注意力头数</div>
            </div>
            <div class="col-2">
              <q-input outlined dense v-model="otherParams.attentionHeads" placeholder="8" type="number" />
            </div>
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- 训练过程可视化 -->
    <q-card flat bordered class="q-mb-md">
      <q-card-section>
        <div class=" q-mb-md labelColor font24">训练过程可视化</div>
        <div class="row q-col-gutter-md">
          <div class="col-12 col-md-6">
            <q-card flat class="visualization-card">
              <q-card-section class="q-pa-none">
                <div ref="lossChartRef" style="height: 3.5rem; width: 100%;"></div>
              </q-card-section>
            </q-card>
          </div>
          <div class="col-12 col-md-6">
            <q-card flat class="visualization-card">
              <q-card-section class="q-pa-none">
                <div ref="resourceChartRef" style="height:3.5rem; width: 100%;"></div>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- 操作按钮 -->
    <div class="row q-gutter-md justify-center q-mt-lg">
      <q-btn class="deepBtn roundBox" color="primary" label="保存训练配置" :loading="loading" @click="saveTrainingConfig" />
      <q-btn class="deepBtn roundBox" color="primary" label="批量导入配置" :loading="loading" @click="() => uploaderRef?.pickFiles()">
        <q-uploader ref="uploaderRef" style="display: none;" accept=".json" max-files="1"
          @added="(files) => importBatchConfig(files[0])" hide-upload-btn />
      </q-btn>
      <q-btn class="deepBtn roundBox" color="secondary" label="开始训练" @click="startTraining" />
      <q-btn class="deepBtn roundBox" color="grey" label="取消训练" @click="cancelTraining" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, reactive, watch } from 'vue'
import { api } from 'boot/axios'
import * as echarts from 'echarts'
import { usePlugin } from 'composables/plugin'
import { Notify } from 'quasar'

const { notify } = usePlugin()

// Training state
const trainingId = ref(null)
const trainingStatus = ref(null)
const metricsPollingInterval = ref(null)
const loading = ref(false)

// 文件上传引用
const uploaderRef = ref(null)

// 算法版本数据
const algorithmVersions = [
  'torch-1.8.1',
  'torch-1.9.0',
  'torch-1.10.0',
  'torch-1.11.0'
]

// 学习率策略选项
const learningRateStrategies = [
  '余弦衰减',
  '线性衰减',
  '阶梯衰减',
  '固定学习率'
]

// 计算类型选项
const computeTypes = [
  'bf16',
  'fp16',
  'fp32',
  'int8'
]

// 算法配置
const algorithm = ref({
  version: 'torch-1.8.1',
  modelPath: null
})

// 训练数据配置
const training = ref({
  dataset: null,
  validationRatio: 0
})

// 训练资源配置
const resources = ref({
  cpuCount: '',
  npuCount: '',
  storageSize: ''
})

// 训练参数配置
const showTrainParams = ref(false)
const parameters = ref({
  learningRate: '1e-5',
  epochs: '5',
  maxGradNorm: '1',
  maxSamples: '100000',
  gradAccumulation: '1e-5',
  batchSize: '5',
  learningRateStrategy: '余弦衰减',
  computeType: 'bf16'
})

// 其他参数配置
const showOtherParams = ref(false)
const otherParams = ref({
  // 优化器设置
  optimizer: 'Adam',
  momentum: '0.9',
  weightDecay: '1e-4',
  epsilon: '1e-8',

  // 正则化参数
  dropout: '0.1',
  labelSmoothing: '0.1',
  useGradientClipping: true,
  useMixedPrecision: true,

  // 训练控制
  earlyStopping: 5,
  checkpointFreq: 10,
  warmupSteps: 1000,
  logFreq: 100,

  // 模型特定参数
  activation: 'ReLU',
  initialization: 'He初始化',
  normalization: 'BatchNorm',
  attentionHeads: 8
})

// 优化器选项
const optimizerOptions = [
  'Adam',
  'AdamW',
  'SGD',
  'RMSprop',
  'Adagrad'
]

// 激活函数选项
const activationOptions = [
  'ReLU',
  'LeakyReLU',
  'GELU',
  'Swish',
  'Sigmoid',
  'Tanh'
]

// 初始化方法选项
const initOptions = [
  'He初始化',
  'Xavier初始化',
  'Normal初始化',
  'Uniform初始化'
]

// 归一化选项
const normOptions = [
  'BatchNorm',
  'LayerNorm',
  'InstanceNorm',
  'GroupNorm'
]

// 数据集选项
const datasetOptions = ref([
  // { value: 1, label: '数据集1' },
  // { value: 2, label: '数据集2' },
  // { value: 3, label: '数据集3' }
])

// 模型选项
let modelOptions = ref([
  { label: '模型A', value: '模型A' },
  { label: '模型B', value: '模型B' },
  { label: '模型C', value: '模型C' }
  // TODO: 可根据实际需求动态获取
])

let disableDataset = ref(true)

// 获取模型名称  
const getModelOption = async () => {
  let { code, results: res } = await api.get('backend/models/?fields=name')
  if (code == 200) {
    console.log('模型选项>>', res)
    modelOptions.value = res.map(item => {
      return {
        value: item.id,
        label: item.name
      }
    })
  }
}

// 选择模型 > 获取对应数据集选项
const onModelChange = async () => {
  training.value.dataset = null;
  let id = algorithm.value.modelPath.value
  let { code, data } = await api.get(`backend/models/${id}/compatible_datasets/`)
  if (code == 200) {
    console.log('模型选择>>', algorithm.value.modelPath)
    datasetOptions.value = data.map((item) => {
      return {
        label: item.name,
        value: item.id
      }
    })
    // datasetOptions.value.length > 0 ? training.value.dataset = datasetOptions.value[0] : null
    disableDataset.value = datasetOptions.value.length === 0;
  }
}

// 图表引用
const lossChartRef = ref(null)
const resourceChartRef = ref(null)
let lossChart = null
let resourceChart = null

// 使用响应式数据存储图表数据
const chartData = reactive({
  loss: {
    epochs: Array.from({ length: 100 }, (_, i) => i + 1),
    trainLoss: Array.from({ length: 100 }, () => Math.random() * 0.5 + 0.5).map((val, idx) => val * Math.exp(-idx / 20)),
    valLoss: Array.from({ length: 100 }, () => Math.random() * 0.7 + 0.7).map((val, idx) => val * Math.exp(-idx / 25))
  },
  resource: {
    timestamps: Array.from({ length: 100 }, (_, i) => `${i * 10}分钟`),
    cpuUsage: Array.from({ length: 100 }, () => Math.random() * 30 + 50),
    gpuUsage: Array.from({ length: 100 }, () => Math.random() * 40 + 60),
    memoryUsage: Array.from({ length: 100 }, () => Math.random() * 20 + 70)
  }
})

// 抽离损失图表的配置选项
const getLossChartOption = () => {
  return {
    title: {
      text: '损失函数曲线',
      left: 'center',
      textStyle: {
        color: '#fff'
      }
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['训练损失', '验证损失'],
      bottom: 5,
      left: 'center',
      itemGap: 20,
      textStyle: {
        fontSize: window.screen.width <= 1536 ? 12 : 16, 
        color: '#fff', // 设置为白色（与你的主题一致）
      },
    },
    grid: {
      left: '10%',
      right: '4%',
      bottom: '20%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: chartData.loss.epochs,
      name: '迭代轮次',
      nameLocation: 'middle',
      nameGap: 30,
      axisLabel: {
        fontSize: window.screen.width <= 1536 ? 12: 16,  // 文字大小
      },
      nameTextStyle: {
        fontSize: window.screen.width <= 1536 ? 12: 16,  // 文字大小
      },
      axisLine: {
        show: true,
        lineStyle: {
          type: 'solid',
          color: '#fff'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '损失值',
      nameRotate: 0,  // 添加这行，设置名称为水平显示
      nameLocation: 'middle',
      nameGap: 40,
      axisLabel: {
        fontSize:window.screen.width <= 1536 ? 12: 16,  // 文字大小
      },
      nameTextStyle: {
        fontSize:window.screen.width <= 1536 ? 12: 16,  // 文字大小
      },
      axisLine: {
        show: true,
        lineStyle: {
          type: 'solid',
          color: '#fff'
        }
      }
    },
    series: [
      {
        name: '训练损失',
        type: 'line',
        data: chartData.loss.trainLoss,
        symbolSize: 6,
        sampling: 'lttb',
        itemStyle: {
          color: '#FF5722'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(255, 87, 34, 0.5)' },
            { offset: 1, color: 'rgba(255, 87, 34, 0.1)' }
          ])
        },
        smooth: true
      },
      {
        name: '验证损失',
        type: 'line',
        data: chartData.loss.valLoss,
        symbolSize: 6,
        sampling: 'lttb',
        itemStyle: {
          color: '#2196F3'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(33, 150, 243, 0.5)' },
            { offset: 1, color: 'rgba(33, 150, 243, 0.1)' }
          ])
        },
        smooth: true
      }
    ]
  }
}

// 抽离资源图表的配置选项
const getResourceChartOption = () => {
  return {
    title: {
      text: '集群资源利用率',
      left: 'center',
      textStyle: {
        color: '#fff'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    legend: {
      data: ['CPU利用率', 'NPU利用率', '内存利用率'],
      bottom: 5,
      left: 'center',
      itemGap: 20,
      textStyle: {
        fontSize:window.screen.width <= 1536 ? 12: 16,
        color: '#fff', // 设置为白色（与你的主题一致）
      },
    },
    grid: {
      left: '10%',
      right: '4%',
      bottom: '20%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: chartData.resource.timestamps,
      name: '时间',
      nameLocation: 'middle',
      nameGap: 30,
      axisLabel: {
        formatter: function (value) {
          // 如果是完整的ISO时间戳，格式化为简洁的时间显示
          if (value && value.includes('T')) {
            const date = new Date(value)
            return date.toLocaleTimeString('zh-CN', {
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit'
            })
          }
          // 如果已经是简单格式，直接返回
          return value
        },
        fontSize:window.screen.width <= 1536 ? 12: 16,  // 文字大小
      },
      nameTextStyle: {
        fontSize:window.screen.width <= 1536 ? 12: 16,  // 文字大小
      },
      axisLine: {
        show: true,
        lineStyle: {
          type: 'solid',
          color: '#fff'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '利用率(%)',
      nameRotate: 0,  // 添加这行，设置名称为水平显示
      min: 0,
      max: 100,
      nameLocation: 'middle',
      nameGap: 30,
      axisLabel: {
        fontSize: window.screen.width <= 1536 ? 12:16,  // 文字大小
      },
      nameTextStyle: {
        fontSize: window.screen.width <= 1536 ? 12:16,  // 文字大小
      },
      axisLine: {
        show: true,
        lineStyle: {
          type: 'solid',
          color: '#fff'
        }
      }
    },
    series: [
      {
        name: 'CPU利用率',
        type: 'line',
        data: chartData.resource.cpuUsage,
        symbolSize: 6,
        sampling: 'lttb',
        itemStyle: {
          color: '#4CAF50'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(76, 175, 80, 0.5)' },
            { offset: 1, color: 'rgba(76, 175, 80, 0.1)' }
          ])
        },
        smooth: true
      },
      {
        name: 'NPU利用率',
        type: 'line',
        data: chartData.resource.gpuUsage,
        symbolSize: 6,
        sampling: 'lttb',
        itemStyle: {
          color: '#FF9800'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(255, 152, 0, 0.5)' },
            { offset: 1, color: 'rgba(255, 152, 0, 0.1)' }
          ])
        },
        smooth: true
      },
      {
        name: '内存利用率',
        type: 'line',
        data: chartData.resource.memoryUsage,
        symbolSize: 6,
        sampling: 'lttb',
        itemStyle: {
          color: '#9C27B0'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(156, 39, 176, 0.5)' },
            { offset: 1, color: 'rgba(156, 39, 176, 0.1)' }
          ])
        },
        smooth: true
      }
    ]
  }
}

// 初始化损失曲线图
function initLossChart() {
  console.log('开始初始化损失图表')
  console.log('损失图表容器元素:', lossChartRef.value)

  if (lossChartRef.value) {
    lossChart = echarts.init(lossChartRef.value)
    console.log('损失图表实例创建成功:', lossChart)

    // 使用抽离的图表配置
    lossChart.setOption(getLossChartOption())
    
    console.log('损失图表配置已设置')
  } else {
    console.error('损失图表容器元素未找到')
  }
}

// 初始化资源利用率图
function initResourceChart() {
  console.log('开始初始化资源图表')
  console.log('资源图表容器元素:', resourceChartRef.value)

  if (resourceChartRef.value) {
    resourceChart = echarts.init(resourceChartRef.value)
    console.log('资源图表实例创建成功:', resourceChart)

    // 使用抽离的图表配置
    resourceChart.setOption(getResourceChartOption())
    
    console.log('资源图表配置已设置')
  } else {
    console.error('资源图表容器元素未找到')
  }
}

// 窗口大小改变时重置图表大小
function resizeCharts() {
  lossChart?.resize()
  resourceChart?.resize()
}

// 增减验证集比例
function increaseValidationRatio() {
  if (training.value.validationRatio < 1) {
    training.value.validationRatio += 0.1
    training.value.validationRatio = parseFloat(training.value.validationRatio.toFixed(1))
  }
}

function decreaseValidationRatio() {
  if (training.value.validationRatio > 0) {
    training.value.validationRatio -= 0.1
    training.value.validationRatio = parseFloat(training.value.validationRatio.toFixed(1))
  }
}

// 开始训练
async function startTraining() {

  try {
    const response = await api.post('backend/training/start', {
      algorithm: {
        // version: 'v8',
        // modelPath: 'yolov8n.pt',
        version: 'v8',
        modelPath: algorithm.value.modelPath.label
      },
      training: {
        dataset: {
          id: training.value.dataset.value,
          name: training.value.dataset.label,
        },
        validationRatio: training.value.validationRatio
      },
      resources: {
        cpuCount: resources.value.cpuCount,
        npuCount: resources.value.npuCount,
        storageSize: resources.value.storageSize
      },
      parameters: {
        learningRate: parameters.value.learningRate,
        epochs: parameters.value.epochs,
        maxGradNorm: parameters.value.maxGradNorm,
        maxSamples: parameters.value.maxSamples,
        gradAccumulation: parameters.value.gradAccumulation,
        batchSize: parameters.value.batchSize,
        learningRateStrategy: parameters.value.learningRateStrategy,
        computeType: parameters.value.computeType
      },
      otherParams: {
        // 优化器设置
        optimizer: otherParams.value.optimizer,
        momentum: otherParams.value.momentum,
        weightDecay: otherParams.value.weightDecay,
        epsilon: otherParams.value.epsilon,

        // 正则化参数
        dropout: otherParams.value.dropout,
        labelSmoothing: otherParams.value.labelSmoothing,
        useGradientClipping: otherParams.value.useGradientClipping,
        useMixedPrecision: otherParams.value.useMixedPrecision,

        // 训练控制
        earlyStopping: otherParams.value.earlyStopping,
        checkpointFreq: otherParams.value.checkpointFreq,
        warmupSteps: otherParams.value.warmupSteps,
        logFreq: otherParams.value.logFreq,

        // 模型特定参数
        activation: otherParams.value.activation,
        initialization: otherParams.value.initialization,
        normalization: otherParams.value.normalization,
        attentionHeads: otherParams.value.attentionHeads
      }
    })

    // 保存训练ID并开始轮询指标
    trainingId.value = response.trainingId || response.id || 'training_' + Date.now()
    trainingStatus.value = 'running'
    startMetricsPolling()
    notify('训练任务已启动', 'positive')
  } catch (error) {
    console.error('启动训练失败:', error)
    notify('启动训练失败: ' + (error.response?.data?.message || error.message), 'negative')
  }
}

// 获取训练指标数据
async function fetchTrainingMetrics() {
  if (!trainingId.value) return

  try {
    console.log('训练ID:', trainingId.value)
    const response = await api.get(`backend/training/${trainingId.value}/metrics`)
    console.log('获取到的训练数据:', response)

    // 更新训练状态
    trainingStatus.value = response.status

    // 更新损失曲线图表数据
    if (response.loss) {
      console.log('更新损失曲线数据:', response.loss)
      
      // 只更新数据部分，图表会通过watch自动更新
      chartData.loss.epochs = response.loss.epochs
      chartData.loss.trainLoss = response.loss.train_loss  // 修正字段名称
      chartData.loss.valLoss = response.loss.val_loss     // 修正字段名称
      
    } else {
      console.log('没有损失数据')
    }

    // 更新资源使用率图表数据
    if (response.resources) {
      console.log('更新资源使用率数据:', response.resources)

      // 如果有timestamps字段，也进行更新
      if (response.loss.timestamps) {
        chartData.loss.timestamps = response.loss.timestamps
      }
      
      // 只更新数据部分，图表会通过watch自动更新
      chartData.resource.timestamps = response.resources.timestamps
      chartData.resource.cpuUsage = response.resources.cpuUsage
      chartData.resource.gpuUsage = response.resources.gpuUsage
      chartData.resource.memoryUsage = response.resources.memoryUsage
    } else {
      console.log('没有资源数据')
    }

    // 如果训练已完成或失败,停止轮询
    if (response.status === 'completed' || response.status === 'failed') {
      stopMetricsPolling()
      notify(
        response.status === 'completed' ? '训练已完成' : '训练失败',
        response.status === 'completed' ? 'positive' : 'negative'
      )
    }
  } catch (error) {
    console.error('获取训练指标失败:', error)
    notify('获取训练指标失败: ' + (error.response?.data?.message || error.message), 'negative')
  }
}

// 开始轮询训练指标
function startMetricsPolling() {
  // 确保只有一个轮询实例
  stopMetricsPolling()

  // 立即获取一次数据
  fetchTrainingMetrics()

  // 每5秒轮询一次
  metricsPollingInterval.value = setInterval(fetchTrainingMetrics, 5000)
}

// 停止轮询训练指标
function stopMetricsPolling() {
  if (metricsPollingInterval.value) {
    clearInterval(metricsPollingInterval.value)
    metricsPollingInterval.value = null
  }
}

// 取消训练
async function cancelTraining() {
  if (!trainingId.value) {
    Notify.create({
      message: '当前没有正在进行的训练',
      type: 'warning'
    })
    return
  }

  try {
    await api.post(`backend/training/${trainingId.value}/cancel`)
    stopMetricsPolling()
    trainingStatus.value = 'canceled'
    notify('训练任务已取消', 'warning')
  } catch (error) {
    console.error('取消训练失败:', error)
    notify('取消训练失败: ' + (error.response?.data?.message || error.message), 'negative')
  }
}

// 保存训练配置
async function saveTrainingConfig() {
  try {
    loading.value = true

    const config = {
      algorithm: algorithm.value,
      training: training.value,
      resources: resources.value,
      parameters: parameters.value,
      otherParams: otherParams.value
    }

    const response = await api.post('/backend/training/dl/config', config)

    if (response.success) {
      Notify.create({
        message: '配置保存成功',
        type: 'positive'
      })
    }
  } catch (error) {
    console.error('保存配置失败:', error)
    Notify.create({
      message: '保存配置失败',
      type: 'negative'
    })
  } finally {
    loading.value = false
  }
}

// 批量导入配置
async function importBatchConfig(file) {
  try {
    loading.value = true

    const formData = new FormData()
    formData.append('config', file)

    const response = await api.post('/backend/training/dl/config/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    if (response.success) {
      // 更新本地配置
      const importedConfig = response.data
      algorithm.value = importedConfig.algorithm || algorithm.value
      training.value = importedConfig.training || training.value
      resources.value = importedConfig.resources || resources.value
      parameters.value = importedConfig.parameters || parameters.value
      otherParams.value = importedConfig.otherParams || otherParams.value

      Notify.create({
        message: '配置导入成功',
        type: 'positive'
      })
    }
  } catch (error) {
    console.error('导入配置失败:', error)
    Notify.create({
      message: '导入配置失败',
      type: 'negative'
    })
  } finally {
    loading.value = false
  }
}



// 重置图表
function resetCharts() {
  console.log('重置图表')

  // 重新初始化图表
  if (lossChart) {
    lossChart.dispose()
    lossChart = null
  }
  if (resourceChart) {
    resourceChart.dispose()
    resourceChart = null
  }

  // 重新创建图表
  setTimeout(() => {
    initLossChart()
    initResourceChart()
    notify('图表已重置', 'positive')
  }, 100)
}

// 生命周期钩子
onMounted(() => {
  getModelOption()
  // 初始化图表
  initLossChart()
  initResourceChart()

  // 监听窗口大小变化
  window.addEventListener('resize', resizeCharts)
  
  // 监听损失图表数据变化
  watch(() => [chartData.loss.epochs, chartData.loss.trainLoss, chartData.loss.valLoss], () => {
    if (lossChart) {
      lossChart.setOption({
        xAxis: {
          data: chartData.loss.epochs
        },
        series: [
          {
            data: chartData.loss.trainLoss
          },
          {
            data: chartData.loss.valLoss
          }
        ]
      })
    }
  }, { deep: true })
  
  // 监听资源图表数据变化
  watch(() => [chartData.resource.timestamps, chartData.resource.cpuUsage, chartData.resource.gpuUsage, chartData.resource.memoryUsage], () => {
    if (resourceChart) {
      resourceChart.setOption({
        xAxis: {
          data: chartData.resource.timestamps
        },
        series: [
          {
            data: chartData.resource.cpuUsage
          },
          {
            data: chartData.resource.gpuUsage
          },
          {
            data: chartData.resource.memoryUsage
          }
        ]
      })
    }
  }, { deep: true })
})

onBeforeUnmount(() => {
  // 停止轮询
  stopMetricsPolling()

  // 销毁图表实例
  if (lossChart) {
    lossChart.dispose()
  }
  if (resourceChart) {
    resourceChart.dispose()
  }

  // 移除事件监听
  window.removeEventListener('resize', resizeCharts)

  // 销毁图表实例
  lossChart?.dispose()
  resourceChart?.dispose()

  // 清理轮询
  stopMetricsPolling()
})
</script>

<style lang="scss" scoped>
.visualization-card {
  border: 1px solid #eaeaea;
  border-radius: 4px;
}

::v-deep.q-field--auto-height.q-field--dense.q-field--labeled .q-field__control-container {
  padding-top: 0;
}

/* 编辑神经网络 按钮 */
.purpleBtn {
  background-color: #4a2e66 !important;
  border: 1px solid #8962ff;
}

.test {
  :deep(.q-field__suffix) {
    color: white !important;
  }
}

.font24 {
  // font-size: .275rem;
}

.font20 {
  font-size: .225rem;
}

.font16 {
  font-size: .2rem;
}

:deep(.q-field__suffix) {
  color: white !important;
}
.deepBtn{
  height: .45rem;
}
:deep(.q-btn__content) {
  font-size: .175rem;
}

.textRight{
  text-align: right;
}

:deep(.q-toggle__inner){
  font-size: .5rem;
}
// :deep(.q-field){
//   font-size: .175rem;
// }

// :deep(.q-field--dense .q-field__control){
//   height: .5rem;
// }
</style>
