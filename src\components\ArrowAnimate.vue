<template>
    <!-- 小孙自创svg流向动画 -->
    <!-- <svg t="1753862689093" class="icon customArrow" viewBox="0 0 1024 1024" version="1.1"
        xmlns="http://www.w3.org/2000/svg" p-id="12295" width="32" height="32">
        <path
            d="M405.2 527.9c0.3-0.8 1-1.5 1.4-2.2 6.8-14 4.6-31.3-7.4-42.8L126.5 225.1c-14.9-14-38.2-13.4-52.4 1.5-14 14.9-13.4 38.2 1.5 52.4l244.6 231.4L76.6 745.3c-14.7 14.2-15.2 37.6-1 52.4 7.3 7.6 16.9 11.3 26.6 11.3 9.3 0 18.4-3.6 25.7-10.5l270-260.4c0.5-0.5 0.8-1.2 1.2-1.7 0.5-0.3 0.8-0.7 1.2-1 2.3-2.3 3.5-5 4.9-7.5z"
            fill="#f4ea2a" opacity=".5" p-id="12296"></path>
        <path
            d="M680 528c0.3-0.8 1-1.5 1.4-2.2 6.8-14 4.6-31.3-7.4-42.8L401.4 225.1c-14.9-14-38.2-13.4-52.4 1.5-14 14.9-13.4 38.2 1.5 52.4L595 510.3 351.3 745.2c-14.7 14.2-15.2 37.6-1 52.4 7.3 7.6 16.9 11.3 26.6 11.3 9.3 0 18.4-3.6 25.7-10.5L672.8 538c0.5-0.5 0.8-1.2 1.2-1.7 0.5-0.3 0.8-0.7 1.2-1 2.3-2.1 3.3-4.8 4.8-7.3z"
            fill="#f4ea2a" opacity=".7" p-id="12297"></path>
        <path
            d="M954.9 527.9c0.3-0.8 1-1.5 1.4-2.2 6.8-14 4.6-31.3-7.4-42.8L676.2 225.1c-14.9-14-38.2-13.4-52.4 1.5-14 14.9-13.4 38.2 1.5 52.4l244.6 231.4-243.6 234.9c-14.7 14.2-15.2 37.6-1 52.4 7.3 7.6 16.9 11.3 26.6 11.3 9.3 0 18.4-3.6 25.7-10.5l270-260.4c0.5-0.5 0.8-1.2 1.2-1.7 0.5-0.3 0.8-0.7 1.2-1 2.3-2.3 3.3-5 4.9-7.5z"
            fill="#f4ea2a" p-id="12298"></path>
    </svg> -->
    <div style="display: flex;">
        <div class="arrow"></div>
        <div class="arrow"></div>
        <div class="arrow"></div>
    </div>
</template>

<script setup>

</script>

<style scoped>
.arrow {
    position: relative;
    width: .2rem;
    height: .2rem;
    border-top: .0375rem solid #f4ea2a;
    border-right: .0375rem solid #f4ea2a;
    transform: rotate(45deg);
    animation: arrow-load 2s infinite;
}

.customArrow {
    width: .45rem;
    height: .45rem;
    animation: arrow-load 2.4s infinite;
}


.arrow:nth-child(1){
    animation-delay: 0s;

}
.arrow:nth-child(2) {
    animation-delay: 0.2s;
}
.arrow:nth-child(3) {
    animation-delay: 0.4s;

}

@keyframes arrow-load {
    0% {
        opacity: 0;
        transform: rotate(45deg);
    }

    50% {
        opacity: 1;
    }

    100% {
        opacity: 0;
        transform: rotate(45deg);
    }
}
</style>