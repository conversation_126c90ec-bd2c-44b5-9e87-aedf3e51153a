server {
    listen 80 default_server;

    index index.html;

    location / {
        root /usr/share/nginx/html/spa;
        try_files $uri $uri/ /index.html;
    }
   
    location /backend/ {
        proxy_pass http://rl-platform-service.rl-platform.svc:8000;
    }
    
    location /user/ {
        proxy_pass http://rl-platform-service.rl-platform.svc:8000;
    }
    
    location /common/ {
        proxy_pass http://rl-platform-service.rl-platform.svc:8000;
    }

    location /ws/ {
        proxy_pass http://rl-platform-ws.rl-platform.svc:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }

}
