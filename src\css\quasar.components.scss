.q-table__container {
  max-height: 100%;
  padding: 10px;
  box-sizing: border-box;
  border: 1px solid transparent;
  // border-image: linear-gradient(to right, #3c66a4, #b2d7f1) 1;
  border-image: linear-gradient(to right, #64778a, #293f64) 1 !important;
  background: $tableBg;

  .q-table {
    thead {
      // background-color: #cfcfcf;//origin
      // background: linear-gradient(to right, #2fc7b8, #094f8a);
      background: linear-gradient(to right, #216f6e, #003366);
      background: linear-gradient(to right, #216f6e 30%, #003366 70%);

      position: sticky;
      top: 0;
      z-index: 1;
      color: #63d4ff;

      th {
        font-size: 14px;
      }

      // 表头选择框 默认
      .q-checkbox__inner {
        // color: rgba(0, 0, 0, 0.54); origin
        color: #ffd77f;
      }

      /* 选中状态 */
      .q-checkbox__inner--truthy {
        color: #ffd77f !important;
      }

      // 仅选择部分
      .q-checkbox__inner--indet {
        color: #ffd77f !important;
      }
    }

    tbody {
      // color: #000;  origin
      color: white;

      tr:nth-child(odd) {
        //奇数行
        // background-color: #fff; origin
        background-color: transparent;
      }

      tr:nth-child(even) {
        //偶数行
        // background-color: #e0e0e0; origin
        // background-color: #005982;
        background-color: rgba(0, 89, 130, .15);
      }



      // 内容选择框
      .q-checkbox__inner {
        // color: rgba(0, 0, 0, 0.54); origin
        color: #ffd77f;
      }

      /* 选中状态 */
      .q-checkbox__inner--truthy {
        color: #ffd77f !important;
      }


    }
  }

  .q-table__top .q-table__control {
    color: $slideText;
  }

  .q-table__bottom {
    color: white;
    margin-top: 10px;
  }

  .q-table tbody td {
    font-size: 14px;
  }
}

// 卡片
.q-card {
  background: $tableBg;
  border: 1px solid transparent;
  // border-image: linear-gradient(to right, #3c66a4, #b2d7f1) 1;
  border-image: linear-gradient(to right, #64778a, #293f64) 1;
}

// 输入框placeholder文字颜色 （默认状态）
.q-field__label {
  color: $test;
}

.q-checkbox__inner {
  color: $slideText !important;
}

// 分割线
.q-separator {
  background: $separator !important;
}

// 下拉框
.q-field__inner {
  background: $inputBG;
  border: 1px solid transparent;
  border-image: linear-gradient(to right, #64778a, #293f64) 1;

  border: .0125rem solid transparent;
  border-radius: .0625rem;
  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  background-image: linear-gradient(to right, $inputBG,$inputBG), linear-gradient(to right,#64778a,  #293f64);

  .q-field__native {
    //文字
    color: white;
  }

  .q-field__append {
    //箭头
    color: white;
  }

  .q-field__control {
    //聚焦
    // color: #2fc7b8;
    border: none;
  }
}

// 取消外边框！
.q-field--outlined .q-field__control:before {
  border: none;
}

.q-field--outlined.q-field--highlighted .q-field__control:after {
  border-color: transparent !important;
}


.q-dialog__backdrop {
  background: transparent;
}

.q-dialog__inner--minimized {
  padding: 0;
}

.q-inner-loading {
  background-color: transparent;
}

// Dialog
.q-card__section {
  color: $slideText !important;
}

.q-card__actions {
  color: $slideText !important;
}

.q-card__actions .q-btn {
  color: $slideText !important;
  border-radius: 5px;
}

.q-dialog__title.q-card__section--vert {
  line-height: 1 !important;
  font-size: .225rem !important;
}

.dialog-card .q-card__actions .q-btn--rectangle {
  padding: 0 .375rem !important;
}

.q-dialog__inner>.q-card>.q-card__actions .q-btn--rectangle {
  min-width: .8rem !important;
}

.q-card__actions .q-btn {
  border-radius: .0625rem;
}

.q-dialog__inner>.q-card>.q-card__actions .q-btn--rectangle {
  width: 1.225rem !important;
}

.dialog-card {
  min-width: 5.375rem;
  box-shadow: -0.0625rem 0px .25rem 0px rgba(0, 0, 0, 0.12) !important;
}

.dialog-card .q-card__actions {
  padding: .2rem !important;
}

.q-card__section--vert {
  padding-left: .2rem !important;
  padding-right: .2rem !important;
  padding-bottom: .2rem !important;
  font-size: .15rem !important;
}