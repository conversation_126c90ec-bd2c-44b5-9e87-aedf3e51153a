import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useUserStore = defineStore(
  'User',
  () => {
    // State（状态）
    let userAvatar = ref('')

    const changeUserAvatar = avatar => {
      userAvatar.value = avatar
    }
    // 使用默认头像
    const userAvatarUrl = computed(() => {
      if (!userAvatar.value) {
        return new URL('../assets/images/default.png', import.meta.url).href
      }
      return userAvatar.value
    })

    return {
      userAvatar,
      userAvatarUrl,
      changeUserAvatar,
    }
  },
  {
    persist: true,
  }
)
