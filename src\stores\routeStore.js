import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useRouteStore = defineStore(
  'route',
  () => {
    // State（状态）
    const activeRoute = ref('')

    // Getters（计算属性）
    //   const doubleCount = computed(() => counter.value * 2)

    // Actions（方法）
    const setActiveRoute = route => {
      activeRoute.value = route
    }

    // 返回 state + getters + actions
    return {
      activeRoute,
      setActiveRoute
    }
  },
  {
    persist: true,
  }
)
