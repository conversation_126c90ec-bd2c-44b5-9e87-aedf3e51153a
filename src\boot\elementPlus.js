import {
  ElButton,
  ElDatePicker,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElSwitch,
  ElDropdown,
  ElDropdownMenu,
  ElUpload,
  ElIcon,
  ElTreeSelect,
  ElDropdownItem,
} from 'element-plus'
import { Clock, Search, Plus } from '@element-plus/icons-vue'
import 'element-plus/dist/index.css' // 必须手动导入全局样式

/* WorkFlow Style */
import '@vue-flow/core/dist/style.css'
import '@vue-flow/core/dist/theme-default.css'

// Animate
import 'animate.css';

const components = [
  ElButton,
  ElDatePicker,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElSwitch,
  ElDropdown,
  ElDropdownMenu,
  ElDropdownItem,
  ElUpload,
  ElIcon,
  ElTreeSelect,
]

const icons = {
  Clock,
  Search,
  Plus,
}

export default ({ app }) => {
  components.forEach(component => {
    app.component(component.name, component)
  })

  Object.entries(icons).forEach(([name, component]) => {
    app.component(name, component)
  })
}
