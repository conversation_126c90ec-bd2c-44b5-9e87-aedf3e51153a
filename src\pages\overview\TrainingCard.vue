<template>
  <div class="training-card">
    <TableCard :columns="columns" api-url="backend/tasks/" :params="{ status: 2 }" :rows-per-page="0">
      <template #top>
        <div class="text-primary text-huangyou fs-30 q-mb-md">当前运行训练</div>
      </template>

      <template #body-cell-action="props">
        <q-td :props="props">
          <q-btn :to="`/training/detail/${props.row.id}`" label="查看详情" color="primary" flat dense />
        </q-td>
      </template>
    </TableCard>
  </div>
</template>

<script setup>
import { convertSecondsToHMS } from 'assets/utils'
import TableCard from 'components/TableCard.vue'

const columns = [
  {
    name: 'name',
    field: 'name',
    label: '训练名称',
    align: 'center',
  },
  {
    label: '资源配置',
    format: (val, row) =>
      `${row.actor_num * row.actor_per_cpu + row.learner_num * row.learner_per_cpu} CPU,
      ${row.actor_num * row.actor_per_gpu + row.learner_num * row.learner_per_gpu} GPU
      ${row.actor_num * row.actor_per_memory + row.learner_num * row.learner_per_memory}G 内存`,
    align: 'center',
  },
  {
    name: 'runningTime',
    field: 'running_time',
    format: val => convertSecondsToHMS(val),
    label: '训练时长',
    align: 'center',
  },
  {
    name: 'action',
    label: '训练详情',
    align: 'center',
    slot: true,
  },
]
</script>

<style lang="scss" scoped>
.training-card {
  padding: 10px 20px 20px 20px;
  background-color: #eaeaea;
  height: 450px;
}

.body--dark .training-card {
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(2px);
}
</style>
