// Quasar SCSS (& Sass) Variables
// --------------------------------------------------
// To customize the look and feel of this app, you can override
// the Sass/SCSS variables found in Quasar's source Sass/SCSS files.

// Check documentation for full list of Quasar variables

// Your own variables (that are declared here) and Quasar's own
// ones will be available out of the box in your .vue/.scss/.sass files

// It's highly recommended to change the default colors
// to match your app's branding.
// Tip: Use the "Theme Builder" on Quasar's documentation website.

// $primary: #ff9000;
$primary: #396ea4;
$secondary: #26a69a;
$accent: #c2c2c2;

// my custom
$test: #c2c2c2;
$inputBG: #1e334a; //输入框背景色
$testhover: #000;
$testactive: #ff9000;
$tableBg: #0a1828; //表格背景色

$slideText: white; //菜单文字颜色
$gray: #b1afad;
$separator: #2a3f63; //分割线颜色

$black: #000;

$dark: #1d1d1d;
$dark-page: #121212;

$space-base: 20px;
$generic-border-radius: 0;
$button-border-radius: 0;
$menu-box-shadow-dark: none;
$typography-font-family: 'OPPOSans,-apple-system,Avenir,BlinkMacSystemFont,Segoe UI,Helvetica,Arial,sans-serif';

:root {
  --q-test: #{$test}; // 手动转换为 CSS 变量
  --q-testhover: #{$testhover};
  --q-testactive: #{$testactive};
  --q-inputBG: #{$inputBG};
  --q-tableBg: #{$tableBg};
  --q-slideText: #{$slideText};
  --q-gray: #{$gray};
  --q-separator:#{$separator};
}


/* 当屏幕宽度 ≥ 2000px 时 */
@media (min-width: 2000px) {
  html {
    font-size: 20px;
  }

  .q-btn {
    font-size: 16px;
  }

  .btnContainer {
    .q-btn {
      font-size: 18px;
    }
  }

  .q-card {
    font-size: 16px;
  }

  .q-table {
    font-size: 16px !important;
  }
}