<template>
  <div>
    <div ref="editor" class="full-height"></div>
    <q-inner-loading :showing="loading" />
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { usePlugin } from 'composables/plugin'
import { monacoEditor } from 'assets/customMonaco'

const props = defineProps(['language'])

const emit = defineEmits(['shown'])

defineExpose({
  setValueFromUrl,
  setValue,
  getValue,
})

const { dark } = usePlugin()

const editor = ref(null)
const loading = ref(false)

let codeEditor = null

onMounted(() => {
  codeEditor = monacoEditor.editor.create(editor.value, {
    language: props.language,
    theme: dark.isActive ? 'vs-dark' : 'vs-light',
    fontSize: 14,
    automaticLayout: true,
    minimap: {
      enabled: false,
    },
  })

  emit('shown')
})

watch(
  () => dark.isActive,
  val => {
    monacoEditor.editor.setTheme(val ? 'vs-dark' : 'vs-light')
  }
)

function setValueFromUrl(url) {
  loading.value = true
  fetch(url)
    .then(response => response.text())
    .then(content => {
      codeEditor && codeEditor.setValue(content)
      loading.value = false
    })
    .catch(() => {
      loading.value = false
    })
}

function setValue(values) {
  codeEditor && codeEditor.setValue(values)
}

function getValue() {
  return codeEditor ? codeEditor.getValue() : ''
}
</script>
