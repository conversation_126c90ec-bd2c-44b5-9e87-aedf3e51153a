import { LocalStorage, Notify, throttle } from 'quasar'
import { boot } from 'quasar/wrappers'
import axios from 'axios'

const api = axios.create({ baseURL: import.meta.env.VITE_API || `${location.origin}/` })
api.defaults.timeout = 3000

export default boot(({ router }) => {
  const handleTokenInvalid = throttle(function () {
    Notify.create({
      message: '请重新登录',
      type: 'negative',
    })
    router.push('/login')
  }, 500)

  api.interceptors.request.use(
    config => {
      config.headers.Authorization = `Bearer ${LocalStorage.getItem('token')}`
      return config
    },
    error => {
      return Promise.reject(error)
    }
  )

  api.interceptors.response.use(
    res => {
      const { data } = res
      return data
    },
    error => {
      const { data } = error.response

      if (data.code == 'token_not_valid' || data.code == 'user_not_found') {
        handleTokenInvalid()
      }

      if (error.code == 'ECONNABORTED' && ~error.message.indexOf('timeout')) {
        Notify.create({
          message: '请求超时',
          type: 'negative',
        })
      }

      return Promise.reject(data)
    }
  )

  // 路由拦截器
  router.beforeEach((to, from, next) => {

    if (to.meta.notRequireAuth || LocalStorage.getItem('token')) {
      next()
    } else {
      next({
        path: '/login',
      })
    }
  })
})

export { api, axios }
