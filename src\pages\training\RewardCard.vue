<template>
  <div ref="chartRef" class="reward-chart"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  steps: {
    type: Array,
    default: () => []
  }
})

const chartRef = ref(null)
let chart = null

// 生成随机上升的数据
function generatePerformanceData(length = 100) {
  const data = []
  let value = 0
  let steps = []

  for (let i = 0; i < length; i++) {
    // 生成一个随机增量，保证总体趋势向上
    const increment = Math.random() * 0.5 // 基础增量
    const noise = (Math.random() - 0.3) * 0.3 // 添加一些波动
    value += increment + noise

    // 确保值在合理范围内
    value = Math.max(0, Math.min(value, 100))

    // 生成步数
    const step = i * 1000
    steps.push(step)
    data.push(Number(value.toFixed(2)))
  }

  return {
    data,
    steps
  }
}

// 初始化图表
function initChart() {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)
  updateChart()
}

// 更新图表数据
function updateChart() {
  if (!chart) return

  // 如果没有真实数据，使用生成的数据
  const performanceData = generatePerformanceData()
  const data = props.data.length ? props.data : performanceData.data
  const steps = props.steps.length ? props.steps : performanceData.steps

  const option = {
    title: {
      text: '训练奖励值',
      left: 'center',
      top: 10,
      textStyle: {
        color: '#666'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: function(params) {
        return `训练步数: ${params[0].name}<br/>
                奖励值: ${params[0].value.toFixed(2)}`
      }
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '10%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      name: '训练步数',
      nameLocation: 'middle',
      nameGap: 30,
      data: steps,
      axisLabel: {
        formatter: value => (value / 1000).toFixed(1) + 'k'
      },
      splitLine: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      name: '奖励值',
      nameLocation: 'middle',
      nameGap: 40,
      splitLine: {
        lineStyle: {
          type: 'dashed'
        }
      }
    },
    series: [
      {
        type: 'line',
        name: '奖励值',
        data: data,
        showSymbol: false,
        smooth: true,
        lineStyle: {
          width: 2,
          color: '#2185d0'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(33, 133, 208, 0.3)'
            },
            {
              offset: 1,
              color: 'rgba(33, 133, 208, 0.1)'
            }
          ])
        }
      }
    ]
  }

  chart.setOption(option)
}

// 处理窗口大小变化
function handleResize() {
  chart && chart.resize()
}

// 监听数据变化
watch(() => [props.data, props.steps], () => {
  updateChart()
}, { deep: true })

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (chart) {
    chart.dispose()
    chart = null
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.reward-chart {
  width: 100%;
  height: 100%;
  min-height: 300px;
}
</style>
