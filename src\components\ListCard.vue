<template>
  <div class="sticky q-mb-md header">
    <div class="flex items-center">
      <div class="q-py-xs text-huangyou fs-28">{{ name }}列表</div>
      <q-input
        v-model.trim="filter"
        @update:model-value="getList"
        debounce="500"
        class="q-ml-auto q-mr-xl search-input"
        borderless
        dense
        clearable
      >
        <template v-slot:prepend>
          <q-icon name="search" color="primary" size="24px" />
        </template>
      </q-input>
      <q-btn
        @click="$router.push(createRoute)"
        :label="`新增${name}`"
        icon="add"
        color="primary"
        text-color="black"
        padding="8px 40px"
        class="skew-both"
      />
    </div>
    <q-separator color="primary" size="2px" />
  </div>

  <ListItem @item:click="id => $router.push(detailRoute + id)" :list="list" />

  <q-inner-loading :showing="loading" />
</template>

<script setup>
import { ref } from 'vue'
import { api } from 'boot/axios'
import ListItem from 'components/ListItem.vue'

const props = defineProps(['name', 'apiUrl', 'createRoute', 'detailRoute'])

const list = ref([])
const loading = ref(false)
const filter = ref('')

getList()

function getList() {
  loading.value = true

  api
    .get(props.apiUrl, {
      params: {
        name: filter.value || null,
      },
    })
    .then(res => {
      list.value = res?.results ?? []
      loading.value = false
    })
    .catch(() => {
      loading.value = false
    })
}
</script>

<style lang="scss" scoped>
.header {
  backdrop-filter: blur(5px);
}
.search-input {
  border-bottom: 6px solid #000;
  transform: translateY(3.5px);
}

.body--dark {
  .search-input {
    border-bottom: 6px solid #fff;
  }
}
</style>
