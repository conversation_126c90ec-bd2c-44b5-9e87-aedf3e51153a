<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide" position="right" full-height>
    <q-card class="dialog-card">
      <q-card-section class="flex items-center">
        <div class="title">训练设置</div>
        <q-btn v-close-popup icon="close" class="q-ml-auto" round flat dense />
      </q-card-section>

      <q-card-section class="flex items-center fs-16 text-bold">
        <SvgAim />
        <template v-for="(item, idx) in resourceInfo" :key="idx">
          <span class="q-ml-md">{{ item.name }}</span>
          <div class="q-px-sm">
            <span class="text-positive">{{ item.allocatable }}</span>
            <span>/{{ item.capacity }}</span>
          </div>
          <span class="fs-12 text-grey-7">{{ item.unit }}</span>
        </template>
      </q-card-section>

      <q-stepper v-model="step" color="primary" ref="stepper" class="bg-transparent" keep-alive flat>
        <q-step :name="1" :done="step > 1" prefix="1" title="基础设置">
          <q-form ref="basicForm">
            <div class="row q-col-gutter-md">
              <div class="col-4">
                <div class="text-bold q-mb-sm require">训练名称</div>
                <q-input
                  v-model.trim="training.name"
                  :rules="[val => !!val || '请输入训练名称']"
                  placeholder="请输入训练名称"
                  maxlength="20"
                  lazy-rules
                  outlined
                />
                </div>
                <div class="col-4">
                  <div class="text-bold q-mb-sm require">仿真器</div>
                  <q-select
                    v-model="training.emulator"
                    @update:model-value="onEmulatorChange"
                    :options="emulatorList"
                    :rules="[val => !!val || '请选择仿真']"
                    :use-input="!training.emulator"
                    @filter="lazyLoadEmulatorOptions"
                    placeholder="仿真"
                    option-value="id"
                    option-label="name"
                    map-options
                    emit-value
                    lazy-rules
                    outlined
                  >
                    <template v-slot:no-option>
                      <div class="text-grey q-pa-sm">暂无仿真</div>
                    </template>
                  </q-select>
                </div>
            </div>
            <div class="row q-col-gutter-md">
                <div class="col-4">
                  <div class="text-bold q-mb-sm require">想定</div>
                  <q-select
                    v-model="training.scenario"
                    @update:model-value="onScenarioChange"
                    :options="scenarioList"
                    :rules="[val => !!val || '请选择想定']"
                    :use-input="!training.scenario"
                    @filter="lazyLoadScenarioOptions"
                    placeholder="请选择想定"
                    option-value="id"
                    option-label="name"
                    map-options
                    emit-value
                    lazy-rules
                    outlined
                  >
                    <template v-slot:no-option>
                      <div class="text-grey q-pa-sm">暂无想定</div>
                    </template>
                  </q-select>
              </div>
              <div class="col-4">
                  <div class="text-bold q-mt-lg">
                    智能体配置
                    <q-btn v-if="showingAgents" @click="toggleAgents" size="xs" flat round>
                      <SvgEye />
                    </q-btn>
                    <q-btn v-else @click="toggleAgents" size="xs" flat round>
                      <SvgSlash />
                    </q-btn>
                  </div>
                <div v-if="showingAgents">
                  <q-list class="text-bold q-mt-md" bordered>
                    <template v-for="agent in agentsList" :key="agent.id">
                      <q-expansion-item
                        :label="agent.name"
                        :caption="'包含 ' + agent.platforms.length + ' 个平台'"
                        :header-class="getAgentHeaderClass(agent.name)"
                      >
                        <!-- 平台列表 -->
                        <q-list>
                          <template v-for="platform in agent.platforms" :key="platform.id">
                            <q-expansion-item
                              :label="platform.type"
                              header-class="text-secondary"
                            >
                              <!-- 状态空间 -->
                              <q-item>
                                <q-item-section>
                                  <q-item-label class="text-bold">状态空间</q-item-label>
                                  <q-list dense>
                                    <template v-for="(value, key) in platform.observation" :key="key">
                                      <q-item>
                                        <q-item-section>
                                          <q-item-label>{{ key }}</q-item-label>
                                          <q-item-label caption>{{ value }}</q-item-label>
                                        </q-item-section>
                                      </q-item>
                                    </template>
                                  </q-list>
                                </q-item-section>
                              </q-item>

                              <!-- 动作空间 -->
                              <q-item v-if="platform.actions">
                                <q-item-section>
                                  <q-item-label class="text-bold">动作空间</q-item-label>
                                  <q-list dense>
                                    <template v-for="(action, key) in platform.actions" :key="key">
                                      <q-expansion-item :label="action.description">
                                        <q-list dense>
                                          <q-item v-for="choice in action.choices" :key="choice.value">
                                            <q-item-section>
                                              <q-item-label caption>
                                                {{ choice.description }}
                                              </q-item-label>
                                            </q-item-section>
                                          </q-item>
                                        </q-list>
                                      </q-expansion-item>
                                    </template>
                                  </q-list>
                                </q-item-section>
                              </q-item>
                            </q-expansion-item>
                          </template>
                        </q-list>

                        <!-- 奖励函数 -->
                        <q-item v-if="agent.rewards">
                          <q-item-section>
                            <q-item-label class="text-bold">奖励函数</q-item-label>
                            <q-list dense>
                              <!-- 行为奖励 -->
                              <q-expansion-item
                                v-if="agent.rewards.behavior_rewards"
                                :label="agent.rewards.behavior_rewards.description"
                              >
                                <q-list dense>
                                  <q-item v-for="reward in agent.rewards.behavior_rewards.items" :key="reward.event">
                                    <q-item-section>
                                      <div class="row items-center">
                                        <div class="col-8">{{ reward.description }}</div>
                                        <div class="col-4">
                                          <q-input
                                            v-model.number="reward.value"
                                            type="number"
                                            dense
                                            outlined
                                            :rules="[
                                              val => !isNaN(val) || '请输入数字',
                                            ]"
                                          />
                                        </div>
                                      </div>
                                    </q-item-section>
                                  </q-item>
                                </q-list>
                              </q-expansion-item>

                              <!-- 目标奖励 -->
                              <q-expansion-item
                                v-if="agent.rewards.objective_rewards"
                                :label="agent.rewards.objective_rewards.description"
                              >
                                <q-list dense>
                                  <q-item v-for="reward in agent.rewards.objective_rewards.items" :key="reward.event">
                                    <q-item-section>
                                      <div class="row items-center">
                                        <div class="col-8">{{ reward.description }}</div>
                                        <div class="col-4">
                                          <q-input
                                            v-model.number="reward.value"
                                            type="number"
                                            dense
                                            outlined
                                            :rules="[
                                              val => !isNaN(val) || '请输入数字',
                                            ]"
                                          />
                                        </div>
                                      </div>
                                    </q-item-section>
                                  </q-item>
                                </q-list>
                              </q-expansion-item>
                            </q-list>
                          </q-item-section>
                        </q-item>
                      </q-expansion-item>
                    </template>
                  </q-list>
                </div>
              </div>
            </div>

            <div class="row q-col-gutter-md">
              <div class="col-4">
                <div class="text-bold q-mb-sm require">智能体类型</div>
                <q-select
                  v-model="training.agentType"
                  :options="agentList"
                  :rules="[val => !!val || '请选择智能体类型']"
                  placeholder="请选择智能体类型"
                  option-label="name"
                  option-value="id"
                  map-options
                  emit-value
                  lazy-rules
                  outlined
                >
                  <template v-slot:no-option>
                    <div class="text-grey q-pa-sm">暂无智能体类型</div>
                  </template>
                </q-select>
              </div>
            </div>
          </q-form>
        </q-step>

        <q-step :name="2" :done="step > 2" prefix="2" title="资源设置">
          <q-form ref="resourceForm">
            <div class="text-bold q-mb-sm require">Actor</div>
            <div class="flex">
              <q-input
                v-model.number="training.actorNum"
                :rules="positiveIntegerRule"
                placeholder="请输入Actor数量"
                type="number"
                maxlength="20"
                lazy-rules
                outlined
              />
            </div>

            <div class="text-bold q-mt-md q-mb-sm require">每个Actor配置资源数量</div>
            <div class="row">
              <q-input
                v-model.number="training.actorPerCpu"
                :rules="positiveIntegerRule"
                placeholder="请输入CPU数量"
                type="number"
                maxlength="20"
                lazy-rules
                outlined
              >
                <template v-slot:before>
                  <div :class="beforeClass">CPU</div>
                </template>
              </q-input>

              <q-input
                v-model.number="training.actorPerGpu"
                :rules="integerRule"
                placeholder="请输入GPU数量"
                type="number"
                maxlength="20"
                class="q-mx-md"
                lazy-rules
                outlined
              >
                <template v-slot:before>
                  <div :class="beforeClass">GPU</div>
                </template>
              </q-input>

              <q-input
                v-model.number="training.actorPerMemory"
                :rules="positiveIntegerRule"
                placeholder="请输入内存数量单位Gi"
                type="number"
                maxlength="20"
                lazy-rules
                outlined
              >
                <template v-slot:before>
                  <div :class="beforeClass">内存</div>
                </template>
                <template v-slot:after>
                  <div :class="beforeClass">G</div>
                </template>
              </q-input>
            </div>

            <div class="text-bold q-mt-md q-mb-sm require">Learner</div>
            <div class="flex">
              <q-input
                v-model.number="training.learnerNum"
                :rules="positiveIntegerRule"
                placeholder="请输入Learner数量"
                type="number"
                maxlength="20"
                lazy-rules
                outlined
              />
            </div>

            <div class="text-bold q-mt-md q-mb-sm require">每个Learner配置资源数量</div>
            <div class="row">
              <q-input
                v-model.number="training.learnerPerCpu"
                :rules="positiveIntegerRule"
                placeholder="请输入CPU数量"
                type="number"
                maxlength="20"
                lazy-rules
                outlined
              >
                <template v-slot:before>
                  <div :class="beforeClass">CPU</div>
                </template>
              </q-input>

              <q-input
                v-model.number="training.learnerPerGpu"
                :rules="integerRule"
                placeholder="请输入GPU数量"
                type="number"
                maxlength="20"
                class="q-mx-md"
                lazy-rules
                outlined
              >
                <template v-slot:before>
                  <div :class="beforeClass">GPU</div>
                </template>
              </q-input>

              <q-input
                v-model.number="training.learnerPerMemory"
                :rules="positiveIntegerRule"
                placeholder="请输入内存数量单位Gi"
                type="number"
                maxlength="20"
                lazy-rules
                outlined
              >
                <template v-slot:before>
                  <div :class="beforeClass">内存</div>
                </template>
                <template v-slot:after>
                  <div :class="beforeClass">G</div>
                </template>
              </q-input>
            </div>
          </q-form>
        </q-step>
      </q-stepper>

      <q-card-actions align="right">
        <q-btn v-close-popup label="取消" color="grey-9" />
        <q-btn v-if="step > 1" @click="stepper.previous()" label="上一步" color="grey-9" />
        <q-btn v-if="step < 2" @click="nextStep" label="下一步" color="primary" text-color="black" />
        <q-btn
          v-if="step > 1"
          @click="onSubmit"
          :loading="loading"
          label="开始训练"
          color="primary"
          text-color="black"
        />
      </q-card-actions>

      <q-inner-loading :showing="resourceLoading" />
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useDialogPluginComponent } from 'quasar'
import { api } from 'boot/axios'
import { useOverview } from 'composables/overview'
import { usePlugin } from 'composables/plugin'
import MonacoEditor from 'components/MonacoEditor.vue'
import { emulatorOptions } from 'assets/const'
import SvgAim from 'assets/images/svg/aim.svg'
import SvgSlash from 'assets/images/svg/eye-slash.svg'
import SvgEye from 'assets/images/svg/eye.svg'
import _ from 'lodash'

const props = defineProps({
  params: Object,
})

defineEmits([...useDialogPluginComponent.emits])

const beforeClass = 'text-roboto text-bold flex items-center full-height q-px-md fs-20 bg-white'

const { dialogRef, onDialogOK, onDialogHide } = useDialogPluginComponent()
const { loading: resourceLoading, resource } = useOverview()
const { notify } = usePlugin()

const stepper = ref(null)
const editor = ref(null)
const basicForm = ref(null)
const resourceForm = ref(null)

const step = ref(1)
const loading = ref(false)
const scenarioList = ref([])
const agentList = ref([
  { id: 1, name: '电子对抗智能体' },
  { id: 2, name: '通信组网智能体' },
  { id: 3, name: '平台决策智能体' },
  { id: 4, name: '预警探测智能体' }
])
const stateList = ref([])
const actionList = ref([])

const emulatorList = ref([])
const algorithmList = ref([])
const algoList = ref([])
const rewardList = ref([])
const networkList = ref([])
const paramsList = ref([])

const showingOBS = ref(false)
const showingActionSpace = ref(false)
const showingAgents = ref(false)
const showingRewards = ref(false)
const agentsList = ref([])
const behaviorRewards = ref([])
const objectiveRewards = ref([])

const positiveIntegerRule = [
  val => !_.isNull(val) || '该项不能为空',
  val => (_.isInteger(val) && val > 0) || `该项须为正整数`,
]

const integerRule = [
  val => !_.isNull(val) || '该项不能为空',
  val => (_.isInteger(val) && val >= 0) || `该项须为整数且不能小于0`,
]

const resourceInfo = computed(() => [
  {
    name: 'GPU资源可用/总量:',
    unit: '卡',
    capacity: resource.value?.gpu?.capacity,
    allocatable: resource.value?.gpu?.allocatable,
  },
  {
    name: 'CPU资源可用/总量:',
    unit: '核',
    capacity: resource.value?.cpu?.capacity,
    allocatable: resource.value?.cpu?.allocatable,
  },
  {
    name: '内存资源可用/总量:',
    unit: 'GB',
    capacity: resource.value?.memory?.capacity,
    allocatable: resource.value?.memory?.allocatable,
  },
  {
    name: '存储资源可用/总量:',
    unit: 'TB',
    capacity: resource.value?.disk?.capacity,
    allocatable: resource.value?.disk?.allocatable,
  },
])

const sceneCustomConfig = ref({
  observation: null,
  action: null,
  reward: null,
  network: null,
  algorithm: null,
})

const training = ref({
  id: null,
  name: '',
  emulator: null,
  scenario: null,
  agentType: null,

  actorNum: 3,
  actorPerCpu: 1,
  actorPerGpu: 0,
  actorPerMemory: 4,
  learnerNum: 1,
  learnerPerCpu: 1,
  learnerPerGpu: 0,
  learnerPerMemory: 4,

  algorithm: null,
})

function lazyLoadEmulatorOptions(val, update, abort) {
  if (emulatorList.value.length) {
    update()
    return
  }

  api.get('backend/emulators/').then(res => {
    update(() => {
      emulatorList.value = res?.results ?? []
    })
  })
}

function lazyLoadScenarioOptions(val, update, abort) {
  if (scenarioList.value.length) {
    update()
    return
  }

  if (!training.value.emulator) {
    notify('请先选择模拟器')
    return
  }

  const emulator = emulatorList.value.find(item => item.id == training.value.emulator)
  const emulatorID = emulator.emulator_id
  api.get(`backend/scenarios/?emulator_id=${emulatorID}`).then(res => {
    scenarioList.value = res?.results ?? []
  })

}

function lazyLoadAlgorithmOptions(val, update, abort) {
  if (algorithmList.value.length) {
    update()
    return
  }

  api.get('backend/algorithms/').then(res => {
    update(() => {
      algorithmList.value = res?.results ?? []
    })
  })
}

function lazyLoadRewardOptions(val, update, abort) {
  if (rewardList.value.length) {
    update()
    return
  }

}

function lazyLoadNetworkOptions(val, update, abort) {
  if (networkList.value.length) {
    update()
    return
  }

}


function setParameter() {
  if (paramsList.value.length) {
    editor.value.setValue(JSON.stringify(paramsList.value, null, 2))
  }
}

function showAtionSpace() {
  showingActionSpace.value = !showingActionSpace.value
  if (actionList.value.length) {
    console.log(showingActionSpace.value)
  }
}

function showObs() {
  showingOBS.value = !showingOBS.value

  if (stateList.value.length) {
    console.log(showingOBS.value)
  }
}

function nextStep() {
  if (step.value == 1) {
    basicForm.value.validate().then(success => {
      if (success) {
        step.value = 2
      }
    })
  }
}

function onEmulatorChange(emulatorId) {
  const emulator = emulatorList.value.find(item => item.id == training.value.emulator)
  const emulatorID = emulator.emulator_id

  api.get(`backend/scenarios/?emulator_id=${emulatorID}`).then(res => {
    scenarioList.value = res?.results ?? []
  })
}

function onScenarioChange(scenarioId) {
  const scenario = scenarioList.value.find(item => item.id == scenarioId);
  if (scenario?.config?.agents) {
    agentsList.value = scenario.config.agents;
  }
}

function onAlgorithmChange(algorithmId) {

  const algorithm = algorithmList.value.find(item => item.id == training.value.algorithm)

  algoList.value = algorithm?.config?.agents?.[0]?.algorithms ?? [];
  rewardList.value = algorithm?.config?.agents?.[0]?.rewards ?? [];
  networkList.value = algorithm?.config?.agents?.[0]?.networks ?? [];
  paramsList.value = algorithm?.config?.agents?.[0]?.parameters ?? [];

  sceneCustomConfig.value.algorithm = algoList.value[algoList.value.length - 1];
  sceneCustomConfig.value.reward = rewardList.value[rewardList.value.length - 1];
  sceneCustomConfig.value.network = networkList.value[networkList.value.length - 1];

  setParameter()

}

function onNetworkChange(networkId) {
  const network = networkList.value.find(item => item.id == networkId)
  sceneCustomConfig.value.network = network

  console.log(sceneCustomConfig.value.network.value)
}

function onRewardChange(rewardId) {
  const reward = rewardList.value.find(item => item.id == rewardId)
  sceneCustomConfig.value.reward = reward

  console.log(sceneCustomConfig.value.reward.value)
}

function toggleAgents() {
  showingAgents.value = !showingAgents.value
}

function onSubmit() {
  loading.value = true

  const {
    id,
    name,
    emulator,
    scenario,
    agentType: agent_type,
    algorithm,

    actorNum: actor_num,
    actorPerCpu: actor_per_cpu,
    actorPerGpu: actor_per_gpu,
    actorPerMemory: actor_per_memory,

    learnerNum: learner_num,
    learnerPerCpu: learner_per_cpu,
    learnerPerGpu: learner_per_gpu,
    learnerPerMemory: learner_per_memory,
  } = training.value

  // 获取修改后的奖励配置
  const scenarioConfig = scenarioList.value.find(item => item.id == scenario)?.config

  api
    .post('backend/tasks/', {
      id,
      name,
      emulator,
      scenario,
      agent_type,
      algorithm,
      is_debug: true,

      actor_num,
      actor_per_cpu,
      actor_per_gpu,
      actor_per_memory,

      learner_num,
      learner_per_cpu,
      learner_per_gpu,
      learner_per_memory,

      scene_custom_config: JSON.stringify(scenarioConfig),
    })
    .then(() => {
      loading.value = false
      notify('创建成功', 'positive')
      onDialogOK()
    })
    .catch(error => {
      loading.value = false
      if (error.code == 'object_exists') {
        notify('名称已存在')
      }
      if (error.code == 'invalid_training_name') {
        notify('训练名称不合法')
      }
    })
}

function getAgentHeaderClass(agentName) {
  const name = agentName.toLowerCase();
  if (name.includes('enemy') || name.includes('敌方')) {
    return 'text-negative agent-header';  // 敌方红色
  } else if (name.includes('chinese') || name.includes('自身')) {
    return 'text-primary agent-header';   // 自身蓝色
  } else if (name.includes('friendly') || name.includes('友军')) {
    return 'text-positive agent-header';  // 友军绿色
  }
  return 'text-grey agent-header';        // 默认灰色
}
</script>

<style lang="scss" scoped>
.dialog-card {
  width: 1400px;
  padding: 40px 80px;
}

.parameter-editor {
  height: 600px;
}

@media (min-width: $breakpoint-lg-min) {
  .dialog-card {
    padding: 80px 100px;
  }
}

.text-secondary {
  color: #666;
}

.q-input {
  .q-field__native {
    padding: 4px 8px;
  }
}

.agent-header {
  font-weight: bold;
  font-size: 16px;

  &.text-negative {
    color: #db2828 !important;  // 红色
    background-color: rgba(219, 40, 40, 0.1);
  }

  &.text-primary {
    color: #2185d0 !important;  // 蓝色
    background-color: rgba(33, 133, 208, 0.1);
  }

  &.text-positive {
    color: #21ba45 !important;  // 绿色
    background-color: rgba(33, 186, 69, 0.1);
  }

  &.text-grey {
    color: #767676 !important;  // 灰色
    background-color: rgba(118, 118, 118, 0.1);
  }
}

.q-expansion-item {
  margin-bottom: 8px;
  border-radius: 4px;
  overflow: hidden;
}
</style>
