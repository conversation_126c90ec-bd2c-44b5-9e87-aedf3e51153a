<template>
  <div class="q-pa-md">
    <!-- User Management Header -->
    <q-card flat bordered class="q-mb-md">
      <q-card-section>
        <div class="row q-col-gutter-md">
          <div class="col-md-3 col-sm-6 col-12 flexbox customFlexBox">
            <div class="labelT">单位</div>
            <q-select style="flex: 1;" v-model="selectedDept" :options="deptOptions" option-value="value" option-label="label"
              :label="selectedDept ? '' : '请输入'" outlined dense emit-value map-options clearable />
          </div>

          <div class="col-md-3 col-sm-6 col-12 flexbox ">
            <div class="labelT">用户名</div>
            <q-input style="flex: 1;" v-model="searchName" :label="searchName ? '' : '请输入'" outlined dense  />
          </div>

          <div class="col-md-3 col-sm-6 col-12 flexbox ">
            <div class="labelT">手机号</div>
            <q-input style="flex: 1;" v-model="searchPhone" :label="searchPhone ? '' : '请输入'" outlined dense />
          </div>

          <div class="col-md-3 col-sm-6 col-12  ">
            <div class="labelT">用户状态</div>
            <q-select style="flex: 1;" v-model="userStatus" :options="statusOptions" :label="userStatus ? '' : '请输入'" outlined dense
              emit-value map-options clearable />
          </div>
        </div>

        <div class="q-my-md flexCenter">
          <q-btn class="roundBox" color="primary" label="查询" @click="queryUsers" />
          <q-btn class="roundBox grayBox" outline color="primary" label="重置" @click="resetFilters" />
          <q-btn color="primary" label="新增用户" @click="addUser" class="roundBox" />
          <q-btn color="secondary" label="导出用户" @click="exportUsers" class="roundBox" />
        </div>
      </q-card-section>
    </q-card>

    <!-- 流程图 -->
    <!-- <WorkFlow></WorkFlow> -->

    <!-- User Table -->
    <q-table title="用户管理" :rows="userList" :columns="columns" row-key="id" selection="multiple"
      v-model:selected="selected" :pagination="pagination" :loading="loading" :rows-per-page-options="[0]"
      hide-selection-column>
      <template v-slot:body-cell-status="props">
        <q-td>
          <q-chip :color="props.row.is_active ? 'positive' : 'negative'" text-color="white" dense>
            {{ props.row.is_active ? '启用' : '禁用' }}
          </q-chip>
        </q-td>
      </template>

      <template v-slot:body-cell-role="props">
        <q-td class="text-center">
          <q-badge color="purple" v-if="props.row.is_superuser">超级管理员</q-badge>
          <q-badge color="blue" v-else-if="props.row.is_staff">管理员</q-badge>
          <q-badge color="grey" v-else>普通用户</q-badge>
        </q-td>
      </template>

      <template v-slot:body-cell-actions="props">
        <q-td style="display: flex; justify-content: center;">
          <q-btn class="customText" flat label="编辑" size="sm" color="primary" @click="editUser(props.row)" />
          <q-btn class="customText" flat :label="props.row.is_active ? '禁用' : '启用'" size="sm"
            :color="props.row.is_active ? 'negative' : 'positive'" @click="toggleUserStatus(props.row)" />
          <q-btn class="customText" flat label="删除" size="sm" color="negative" @click="confirmDeleteUser(props.row)" />
        </q-td>
      </template>

      <template v-slot:bottom>
        <div class="row items-center full-width paginationEl">
          <div style="margin-right: 20px;">总计 {{ pagination.total }} 条</div>
            <div>
              <q-select v-model="pagination.rowsPerPage" :options="[5, 10,]" outlined dense options-dense
                emit-value map-options style="min-width: 100px" @update:model-value="onRowsPerPageChange">
                <!-- 自定义显示值 -->
                <template v-slot:selected>
                  {{ pagination.rowsPerPage }}/页
                </template>
              </q-select>
            </div>

          <q-pagination style="margin-left: 80px;margin-right: 80px;" v-model="pagination.page"
            :max="Math.ceil(pagination.total / pagination.rowsPerPage)" :max-pages="5" boundary-numbers
            direction-links />

          <div class="flexbox">
            <div style="margin-right: 10px;">跳到</div>
            <div class="roundBox">
              <q-input class="dynamic-label-input" v-model="jumpText" style="width: 50px;" dense
                @keyup.enter="goJump">
              </q-input>
            </div>
            <q-btn class="custom-btn" label="跳转" @click="goJump" />
          </div>

        </div>
      </template>
    </q-table>

    <!-- 删除确认对话框 -->
    <q-dialog v-model="deleteDialog" persistent>
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="delete" color="negative" text-color="white" />
          <span class="q-ml-md labelColor">确定要删除该用户吗？</span>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" color="primary" v-close-popup />
          <q-btn flat label="删除" color="negative" @click="deleteUser" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 状态切换确认对话框 -->
    <q-dialog v-model="statusDialog" persistent>
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar :icon="userToToggle?.is_active ? 'block' : 'check_circle'"
            :color="userToToggle?.is_active ? 'negative' : 'positive'" text-color="white" />
          <span class="q-ml-md labelColor">确定要{{ userToToggle?.is_active ? '禁用' : '启用' }}该用户吗？</span>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" color="primary" v-close-popup />
          <q-btn flat :label="userToToggle?.is_active ? '禁用' : '启用'"
            :color="userToToggle?.is_active ? 'negative' : 'positive'" @click="confirmToggleStatus" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 编辑用户对话框 -->
    <q-dialog v-model="ss" persistent>
      <q-card style="min-width: 400px">
        <q-card-section class="row items-center">
          <div class="text-h6 labelColor">编辑用户</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section>
          <q-form @submit="submitEditUser" class="q-gutter-md">
            <q-input v-model="editingUser.username" :label="editingUser.username ? '' : '账号'" placeholder="请输入账号"
              :rules="[val => !!val || '请输入账号']" outlined dense clearable autocomplete="off" disable />

            <q-input v-model="editingUser.real_name" :label="editingUser.real_name ? '' : '姓名'"
              :rules="[val => !!val || '请输入姓名']" outlined dense />

            <q-input v-model="editingUser.company" :label="editingUser.company ? '' : '单位'" outlined dense />

            <q-input v-model="editingUser.phone" :label="editingUser.phone ? '' : '手机号'"
              :rules="[val => !val || /^1[3-9]\d{9}$/.test(val) || '请输入正确的手机号']" outlined dense />

            <q-input v-model="editingUser.mobile" :label="editingUser.mobile ? '' : '电话'" outlined dense />

            <q-select v-model="editingUser.role" :options="roleOptions" :label="editingUser.role ? '' : '用户权限'"
              :rules="[val => !!val || '请选择用户权限']" outlined dense emit-value map-options />

            <q-toggle class="labelColor" v-model="editingUser.is_active" label="启用用户" />

            <div class="row justify-end q-mt-md">
              <q-btn label="取消" color="primary" flat v-close-popup />
              <q-btn label="确定" type="submit" color="primary" :loading="submitting" />
            </div>
          </q-form>
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- 新增用户对话框 -->
    <q-dialog  v-model="addUserDialog" persistent>
      <q-card  style="min-width: 400px">
        <q-card-section class="row items-center">
          <div class="text-h6 labelColor">新增用户</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section>
          <q-form @submit="submitNewUser" class="q-gutter-md">
            <q-input v-model="newUser.username" :label="newUser.username ? '' : '账号'" placeholder="请输入账号"
              :rules="[val => !!val || '请输入账号']" outlined dense clearable autocomplete="off" />

            <q-input v-model="newUser.password" :label="newUser.password ? '' : '密码'" type="password"
              :rules="[val => !!val || '请输入密码', val => val.length >= 8 || '密码长度至少8位']" outlined dense />

            <q-input v-model="newUser.real_name" :label="newUser.real_name ? '' : '姓名'"
              :rules="[val => !!val || '请输入姓名']" outlined dense />

            <q-input v-model="newUser.company" :label="newUser.company ? '' : '单位'" outlined dense />

            <q-input v-model="newUser.phone" :label="newUser.phone ? '' : '手机号'"
              :rules="[val => !val || /^1[3-9]\d{9}$/.test(val) || '请输入正确的手机号']" outlined dense />

            <q-input v-model="newUser.mobile" :label="newUser.mobile ? '' : '电话'" outlined dense />

            <q-select v-model="newUser.role" :options="roleOptions" :label="newUser.role ? '' : '用户权限'"
              :rules="[val => !!val || '请选择用户权限']" outlined dense emit-value map-options />

            <q-toggle class="labelColor" v-model="newUser.is_active" label="启用用户" />

            <div class="row justify-end q-mt-md">
              <q-btn class="grayBox roundBox" label="取消" color="primary" flat v-close-popup />
              <q-btn style="margin-left: 20px;" class="roundBox" label="确定" type="submit" color="primary"
                :loading="submitting" />
            </div>
          </q-form>
        </q-card-section>
      </q-card>
    </q-dialog>
  </div>
</template>
<script setup>
import WorkFlow from '../workFlow/WorkFlow.vue'
import { ref, reactive, onMounted, watch } from 'vue'
import { api } from 'boot/axios'
import { usePlugin } from 'composables/plugin.js'
import { date, useQuasar, Dialog } from 'quasar'

const { notify } = usePlugin()
const $q = useQuasar()
const loading = ref(false)
const selected = ref([])  // 选中的行
const selectedDept = ref('')
const searchName = ref('')
const searchPhone = ref('')
const userStatus = ref('')
const pagination = ref({
  rowsPerPage: 5,//每页几条数据
  page: 1,
  total: 0,
})

const jumpText = ref('')//跳转

// 删除对话框相关
const deleteDialog = ref(false)
const userToDelete = ref(null)

// 编辑用户相关
const editUserDialog = ref(false)
const editingUser = ref({
  id: null,
  username: '',
  real_name: '',
  company: '',
  phone: '',
  mobile: '',
  role: '',
  is_active: true
})

// 状态切换相关
const statusDialog = ref(false)
const userToToggle = ref(null)

// 使用动态生成的单位选项
const deptOptions = ref([])
const statusOptions = [
  { label: '启用', value: 'true' },
  { label: '禁用', value: 'false' }
]

// 新增用户相关
const addUserDialog = ref(false)
const submitting = ref(false)
const newUser = reactive({
  username: '',
  password: '',
  real_name: '',
  company: '',
  phone: '',
  mobile: '',
  role: '',
  is_active: true
})

const roleOptions = [
  { label: '普通用户', value: 'user' },
  { label: '管理员', value: 'staff' },
  { label: '超级管理员', value: 'superuser' }
]

const userList = ref([])

const columns = [
  // { name: 'id', label: '用户编号', field: 'id', align: 'center', sortable: false },
  { name: 'username', label: '账号', field: 'username', align: 'center', sortable: false },
  { name: 'real_name', label: '姓名', field: 'real_name', align: 'center', sortable: false },
  { name: 'company', label: '单位', field: 'company', align: 'center', sortable: false },
  { name: 'phone', label: '手机号', field: 'phone', align: 'center', sortable: false },
  { name: 'mobile', label: '电话', field: 'mobile', align: 'center', sortable: false },
  { name: 'role', label: '用户权限', field: 'role', align: 'center', sortable: false },
  { name: 'status', label: '状态', field: 'is_active', align: 'center', sortable: false },
  { name: 'date_joined', label: '创建时间', field: 'date_joined', align: 'center', sortable: false, format: val => date.formatDate(val, 'YYYY-MM-DD HH:mm:ss') },
  { name: 'actions', label: '操作', field: 'actions', align: 'center', }
]

// 获取所有单位选项
async function fetchCompanies() {
  try {
    // 也可以从专门的API获取单位列表，这里使用用户数据提取
    const response = await api.get('/user/users/', { params: { page_size: 1000 } })

    if (response && response.results) {
      // 提取所有用户的单位，过滤掉null或空值
      const companies = response.results
        .map(user => user.company)
        .filter(company => company) // 过滤null和空字符串

      // 去重
      const uniqueCompanies = [...new Set(companies)]

      // 转换为选项格式
      deptOptions.value = uniqueCompanies.map(company => ({
        label: company,
        value: company
      }))
    }
  } catch (error) {
    console.error('获取单位列表失败', error)
  }
}

// 获取用户列表数据
async function fetchUsers() {
  loading.value = true

  try {
    const params = {
      page: pagination.value.page,
      page_size: pagination.value.rowsPerPage
    }

    // 只添加有值的过滤条件
    if (selectedDept.value) {
      params.company = selectedDept.value
    }

    if (searchName.value) {
      params.username = searchName.value
    }

    if (searchPhone.value) {
      params.phone = searchPhone.value
    }

    if (userStatus.value) {
      params.is_active = userStatus.value
    }

    const response = await api.get('/user/users/', { params })
    console.log('API响应:', response) // 调试输出

    // 安全地访问响应数据
    if (response && response.results) {
      userList.value = response.results || []
      pagination.value.total = response.count || 0
    } else {
      console.error('API响应格式不符合预期:', response)
      notify('获取用户数据格式错误')
      userList.value = []
      pagination.value.total = 0
    }
  } catch (error) {
    console.error('获取用户列表失败', error)
    notify('获取用户列表失败：' + (error.response?.data?.message || error.message))
    userList.value = []
    pagination.value.total = 0
  } finally {
    loading.value = false
  }
}

const onRowsPerPageChange = (e) => {
  console.log('每页条数变化:', e)
  pagination.value.rowsPerPage = e
  pagination.value.page = 1 // 重置到第一页
  fetchUsers()
}

// 跳转（支持回车键）
const goJump = () => {
  console.log("跳转到页面:", jumpText.value)
  const targetPage = Number(jumpText.value)
  if (targetPage > 0 && targetPage <= Math.ceil(pagination.value.total / pagination.value.rowsPerPage)) {
    pagination.value.page = targetPage
    fetchUsers()
  } else {
    notify('页码超出范围', 'warning')
  }
}

function addUser() {
  // Reset form data
  Object.assign(newUser, {
    username: '',
    password: '',
    real_name: '',
    company: '',
    phone: '',
    mobile: '',
    role: '',
    is_active: true
  })
  addUserDialog.value = true
}

function exportUsers() {
  if (selected.value.length === 0) {
    notify('请选择要导出的用户', 'warning')
    return
  }

  // Get all users data
  api.get('/user/users/', {
    params: {
      page_size: 1000,
      id: selected.value.map(user => user.id).join(',')
    }
  })
    .then(response => {
      if (!response || !response.results) {
        notify('导出失败', 'negative')
        return
      }

      // Convert users data to CSV format
      const headers = ['用户编号', '账号', '姓名', '单位', '手机号', '电话', '用户权限', '状态', '创建时间']
      const rows = response.results.map(user => [
        user.id,
        user.username,
        user.real_name || '',
        user.company || '',
        user.phone || '',
        user.mobile || '',
        user.is_superuser ? '超级管理员' : (user.is_staff ? '管理员' : '普通用户'),
        user.is_active ? '启用' : '禁用',
        user.date_joined
      ])

      // Create CSV content
      const csvContent = [
        headers.join(','),
        ...rows.map(row => row.join(','))
      ].join('\n')

      // Create and download the file
      const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `用户列表_${date.formatDate(new Date(), 'YYYY-MM-DD_HH-mm')}.csv`)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      notify('导出成功', 'positive')
    })
    .catch(error => {
      notify('导出失败', 'negative')
    })
}

function queryUsers() {
  // 重置到第一页
  pagination.value.page = 1
  fetchUsers()
}

function resetFilters() {
  selectedDept.value = ''
  searchName.value = ''
  searchPhone.value = ''
  userStatus.value = ''

  // 重置后自动查询
  queryUsers()
}

function editUser(row) {
  editingUser.value = {
    id: row.id,
    username: row.username,
    real_name: row.real_name || '',
    company: row.company || '',
    phone: row.phone || '',
    mobile: row.mobile || '',
    role: row.is_superuser ? 'superuser' : (row.is_staff ? 'staff' : 'user'),
    is_active: row.is_active
  }
  editUserDialog.value = true
}

async function submitEditUser() {
  submitting.value = true
  try {
    const userData = {
      real_name: editingUser.value.real_name,
      company: editingUser.value.company,
      phone: editingUser.value.phone,
      mobile: editingUser.value.mobile,
      is_active: editingUser.value.is_active,
      is_staff: editingUser.value.role === 'staff',
      is_superuser: editingUser.value.role === 'superuser'
    }

    await api.patch(`/user/users/${editingUser.value.id}/`, userData)
    editUserDialog.value = false

    Dialog.create({
      title: '成功',
      message: '用户信息更新成功！',
      ok: {
        label: '确定',
        color: 'primary'
      },
      persistent: false,
      color: 'primary'
    }).onOk(() => {
      // 刷新用户列表
      fetchUsers()
    })
  } catch (error) {
    Dialog.create({
      title: '错误',
      message: '更新失败：' + (error.response?.data?.message || error.message),
      ok: {
        label: '确定',
        color: 'negative'
      },
      persistent: false,
      color: 'negative'
    })
  } finally {
    submitting.value = false
  }
}

function toggleUserStatus(user) {
  userToToggle.value = user
  statusDialog.value = true
}

async function confirmToggleStatus() {
  try {
    await api.patch(`/user/users/${userToToggle.value.id}/`, {
      is_active: !userToToggle.value.is_active
    })

    $q.notify({
      message: `用户${userToToggle.value.is_active ? '禁用' : '启用'}成功`,
      color: 'green',
      icon: 'check_circle',
      position: 'top',
      timeout: 2000
    })

    userToToggle.value.is_active = !userToToggle.value.is_active
    // 刷新用户列表以确保数据同步
    fetchUsers()
  } catch (error) {
    $q.notify({
      message: `操作失败：${error.response?.data?.message || error.message}`,
      color: 'red',
      icon: 'warning',
      position: 'top',
      timeout: 2000
    })
  } finally {
    statusDialog.value = false
    userToToggle.value = null
  }
}

// 批量修改用户状态
async function batchToggleStatus(status) {
  try {
    // 显示确认对话框
    Dialog.create({
      title: '确认',
      message: `确定要${status ? '启用' : '禁用'}选中的 ${selected.value.length} 个用户吗？`,
      cancel: true,
      persistent: true
    }).onOk(async () => {
      loading.value = true
      try {
        // 并行处理所有请求
        await Promise.all(
          selected.value.map(user =>
            api.patch(`/user/users/${user.id}/`, { is_active: status })
          )
        )

        $q.notify({
          message: `批量${status ? '启用' : '禁用'}成功`,
          color: 'green',
          icon: 'check_circle',
          position: 'top',
          timeout: 2000
        })

        // 清空选择并刷新列表
        selected.value = []
        fetchUsers()
      } catch (error) {
        $q.notify({
          message: `操作失败：${error.response?.data?.message || error.message}`,
          color: 'red',
          icon: 'warning',
          position: 'top',
          timeout: 2000
        })
      } finally {
        loading.value = false
      }
    })
  } catch (error) {
    console.error('批量操作失败:', error)
  }
}

function confirmDeleteUser(user) {
  userToDelete.value = user
  deleteDialog.value = true
}

function deleteUser() {
  if (!userToDelete.value) return

  api.delete(`/user/users/${userToDelete.value.id}/`)
    .then(() => {
      notify('用户删除成功')
      // 重新加载数据
      fetchUsers()
    })
    .catch(error => {
      notify(`删除失败：${error.response?.data?.message || error.message}`)
    })
    .finally(() => {
      deleteDialog.value = false
      userToDelete.value = null
    })
}

async function submitNewUser() {
  submitting.value = true
  try {
    const userData = {
      username: newUser.username,
      password: newUser.password,
      real_name: newUser.real_name,
      company: newUser.company,
      phone: newUser.phone,
      mobile: newUser.mobile,
      is_active: newUser.is_active,
      is_staff: newUser.role === 'staff',
      is_superuser: newUser.role === 'superuser'
    }

    await api.post('/user/users/', userData)
    addUserDialog.value = false

    // 使用导入的 Dialog 显示成功提示
    Dialog.create({
      title: '成功',
      message: '用户创建成功！',
      ok: {
        label: '确定',
        color: 'primary'
      },
      persistent: false,
      color: 'primary'
    }).onOk(() => {
      // 刷新用户列表
      fetchUsers()
    })
  } catch (error) {
    Dialog.create({
      title: '错误',
      message: '创建失败：' + (error.response?.data?.message || error.message),
      ok: {
        label: '确定',
        color: 'negative'
      },
      persistent: false,
      color: 'negative'
    })
  } finally {
    submitting.value = false
  }
}

// 监听分页变化，自动查询
watch(() => pagination.value.page, () => {
  fetchUsers()
})

// 监听每页条数变化，自动查询
watch(() => pagination.value.rowsPerPage, () => {
  fetchUsers()
})

// 页面初始化时加载数据
onMounted(() => {
  fetchCompanies()
  fetchUsers()
})
</script>

<style scoped>
.q-page {
  background: #f5f7fa;
}

::v-deep.q-field--auto-height.q-field--labeled .q-field__control-container {
  padding-top: 0;
}

::v-deep.q-field--labeled .q-field__native {
  padding-top: 0;
  padding-bottom: 0;
}

::v-deep.q-placeholder {
  background: pink !important;
}

.flexbox {
  display: flex;
  align-items: center;
}

.labelT {
  font-size: 14px;
  font-weight: 500;
  color: var(--q-slideText);
  line-height: 22px;
  margin-right: 10px;
}

.flexCenter {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}

.paginationEl {
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-btn {
  width: 60px;
  background-color: #396ea4;
  margin-left: 70px;
  border-radius: 6px;
}

/* 有值或聚焦时隐藏 label */
.dynamic-label-input.q-field--with-content :deep(.q-field__label),
.dynamic-label-input.q-field--focused :deep(.q-field__label) {
  display: none;
}

.dynamic-label-input :deep(.q-field__inner) {
  border: none; /* 设置圆角的 必须单独取消 */
}

/* 单独设置圆角和边框色 */
.dynamic-label-input :deep(.q-field__inner) {
  border-radius: 8px;
}

/* 文字居中 */
.dynamic-label-input :deep(.q-field__native) {
  text-align: center;
}

/* 分页器 */
:deep(.q-pagination .q-btn) {
  color: white !important;
}

:deep(.q-pagination .q-btn--standard) {
  background: #396ea4 !important;
}

:deep(.q-pagination .q-btn:hover) {
  background: #396ea4 !important;
}
.customFlexBox{
  width: 28%;
}
.blackCard{
  background: red !important;
}
</style>
