<template>
  <div class="q-pa-md">
    <div class="text-h5 q-mb-md">模型管理</div>

    <div class="row q-col-gutter-lg">
      <!-- 左侧：模型文件管理 -->
      <div class="col-12 col-md-8">
        <q-card flat bordered class="q-mb-md">
          <q-card-section class="q-pb-none">
            <div class="text-h6">模型文件管理</div>
          </q-card-section>

          <q-card-section>
            <div class="row q-col-gutter-md">
              <div class="col-md-3 col-sm-6 col-12">
                <q-input
                  outlined
                  dense
                  v-model="filterOptions.creator"
                   :label="filterOptions.creator?'':'上传人'"
                  clearable
                >
                  <template v-slot:append>
                    <q-icon name="person" />
                  </template>
                </q-input>
              </div>

              <div class="col-md-3 col-sm-6 col-12">
                <q-input
                  outlined
                  dense
                  v-model="filterOptions.name"
                 :label="filterOptions.name?'':'模型名称'"
                  clearable
                >
                  <template v-slot:append>
                    <q-icon name="search" />
                  </template>
                </q-input>
              </div>

              <div class="col-md-3 col-sm-6 col-12">
                <q-select
                  outlined
                  dense
                  v-model="filterOptions.model_type"
                  :options="modelTypeOptions"
                  :label="filterOptions.model_type?'':'模型类型'"
                  emit-value
                  map-options
                  clearable
                >
                  <template v-slot:append>
                    <q-icon name="category" />
                  </template>
                </q-select>
              </div>

              <div class="col-md-3 col-sm-6 col-12">
                <q-select
                  outlined
                  dense
                  v-model="filterOptions.status"
                  :options="statusOptions"
                  :label="filterOptions.status?'':'模型状态'"
                  emit-value
                  map-options
                  clearable
                >
                  <template v-slot:append>
                    <q-icon name="info" />
                  </template>
                </q-select>
              </div>

              <div class="col-md-7 col-12">
                <div class="row q-col-gutter-sm">
                  <div class="col-5">
                    <q-input
                      outlined
                      dense
                      v-model="filterOptions.start_date"
                      :label="filterOptions.start_date?'':'开始日期'"
                      type="date"
                      clearable
                    />
                  </div>
                  <div class="text">至</div>
                  <div class="col-5 inputel">
                    <q-input
                      outlined
                      dense
                      v-model="filterOptions.end_date"
                      :label="filterOptions.end_date?'':'结束日期'"
                      type="date"
                      clearable
                    />
                  </div>
                </div>
              </div>

              <div class="col-md-5 col-12 flex justify-start">
                <q-btn color="primary" label="查询" class="q-mr-sm" @click="handleSearch" />
                <q-btn outline color="primary" label="重置" @click="resetSearch" />
              </div>
            </div>
          </q-card-section>
          
        </q-card>

        <q-table
          flat
          bordered
          :rows="modelFiles"
          :columns="columns"
          row-key="id"
          selection="multiple"
          v-model:selected="selected"
          :pagination="pagination"
          :loading="loading"
        >
          <template v-slot:body-cell-status="props">
            <q-td :props="props">
              <q-badge :color="getStatusColor(props.row.model_status)" :label="getStatusLabel(props.row.model_status)" />
            </q-td>
          </template>

          <template v-slot:body-cell-operations="props">
            <q-td :props="props">
              <q-btn flat dense size="sm" color="primary" label="下载" @click="downloadModel(props.row)" v-if="props.row.download_url" />
              <q-btn flat dense size="sm" color="primary" label="编辑" @click="viewFile(props.row)" />
              <q-btn flat dense size="sm" color="negative" label="删除" @click="confirmDelete(props.row)" />
            </q-td>
          </template>

          <template v-slot:bottom>
            <div class="row items-center justify-between full-width">
              <div>总计 {{ modelFiles.length }} 条</div>
              <q-pagination
                v-model="pagination.page"
                :max="Math.ceil(modelFiles.length / pagination.rowsPerPage)"
                :max-pages="5"
                boundary-numbers
                direction-links
              />
              <div>
                <q-select
                  v-model="pagination.rowsPerPage"
                  :options="[10, 20, 50]"
                  :label="pagination.rowsPerPage?'':'每页行数'"
                  outlined
                  dense
                  options-dense
                  emit-value
                  map-options
                  style="min-width: 100px"
                />
              </div>
            </div>
          </template>
        </q-table>

        <div class="q-mt-md flex justify-end">
          <q-btn color="primary" label="上传模型" class="q-mr-sm" @click="uploadDialog = true" />
          <q-btn color="secondary" label="导出模型" @click="exportModel" />
        </div>
      </div>

      <!-- 右侧：模型存量 -->
      <div class="col-12 col-md-4">
        <q-card flat bordered class="full-height">
          <q-card-section class="q-pb-none">
            <div class="text-h6">模型存量</div>
          </q-card-section>

          <q-card-section>
            <!-- 模型类型分布饼图 -->
            <div class="text-subtitle2 q-mb-sm">模型类型分布</div>
            <div class="q-pa-md flex justify-center">
              <div class="model-distribution-chart">
                <div class="pie-chart">
                  <svg width="200" height="200" viewBox="0 0 100 100">
                    <circle cx="50" cy="50" r="45" fill="#f0f0f0" />
                    <!-- 这里将通过CSS和SVG生成饼图扇区 -->
                    <template v-if="modelTypeDistribution.length > 0">
                      <path
                        v-for="(slice, index) in generatePieSlices()"
                        :key="index"
                        :d="slice.path"
                        :fill="slice.color"
                        :stroke="slice.color"
                        stroke-width="0.5"
                      />
                    </template>
                    <circle cx="50" cy="50" r="25" fill="white" />
                  </svg>
                </div>
                <div class="pie-chart-legend q-mt-md">
                  <div v-if="modelTypeDistribution.length === 0" class="text-center text-grey">
                    暂无数据
                  </div>
                  <div v-for="(item, index) in modelTypeDistribution" :key="index" class="flex items-center q-mb-xs">
                    <div :style="`width: 12px; height: 12px; background-color: ${item.color}; margin-right: 8px; border-radius: 2px;`"></div>
                    <span class="text-caption">
                      {{ item.name }}: {{ item.percentage }}% ({{ item.count }}个)
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div class="text-subtitle2 q-mb-sm">内存使用量</div>
            <div class="text-center q-mb-md">
              <q-knob
                v-model="diskUsage"
                size="180px"
                :thickness="0.15"
                color="primary"
                track-color="grey-3"
                :min="0"
                :max="100"
                readonly
                show-value
                font-size="24px"
              >
                {{ diskUsage }}%
              </q-knob>
              <div class="q-mt-sm">
                <div class="text-caption">总计: {{ totalStorage }}</div>
                <div class="text-caption">已使用: {{ usedStorage }}</div>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- 上传模型弹窗 -->
    <q-dialog v-model="uploadDialog">
      <q-card style="width: 600px; max-width: 95vw;">
        <q-card-section class="row items-center">
          <div class="text-h6">上传模型</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section class="q-ml-md q-pa-md">
          <q-form @submit="onSubmit" class="q-gutter-md">
            <div class="row q-col-gutter-md">
              <div class="col-12">
                <q-input
                  v-model="newModel.name"
                  :label="newModel.name?'':'模型名称'"
                  outlined
                  dense
                  :rules="[val => !!val || '请输入模型名称']"
                />
              </div>

              <div class="col-12">
                <q-input
                  v-model="newModel.desc"
                  :label="newModel.desc?'':'模型描述'"
                  outlined
                  dense
                  type="textarea"
                  autogrow
                />
              </div>

              <div class="col-sm-6 col-xs-12">
                <q-select
                  v-model="newModel.model_type"
                  :options="modelTypeOptions"
                  :label="newModel.model_type?'':'模型类型'"
                  outlined
                  dense
                  map-options
                  emit-value
                  :rules="[val => !!val || '请选择模型类型']"
                />
              </div>

              <div class="col-sm-6 col-xs-12">
                <q-select
                  v-model="newModel.model_status"
                  :options="statusOptions"
                  :label="newModel.model_status?'':'模型状态'"
                  outlined
                  dense
                  map-options
                  emit-value
                  :rules="[val => !!val || '请选择模型状态']"
                />
              </div>

              <div class="col-sm-6 col-xs-12">
                <q-input
                  v-model="newModel.model_size"
                  :label="newModel.model_size?'':'模型大小 (MB)'"
                  outlined
                  dense
                  type="number"
                  :rules="[
                    val => !!val || '请输入模型大小',
                    val => val > 0 || '模型大小必须大于0'
                  ]"
                />
              </div>
            </div>

            <q-separator class="q-my-md" />

            <div class="row q-col-gutter-md">
              <div class="col-12">
                <q-file
                  v-model="uploadFiles.model_file"
                  :label="uploadFiles.model_file?'':'模型文件'"
                  outlined
                  dense
                  counter
                  accept=".pb,.h5,.pth,.pt,.onnx,.tflite,.weights"
                  :rules="[val => !!val || '请选择模型文件']"
                  hint="上传模型文件"
                >
                  <template v-slot:prepend>
                    <q-icon name="description" />
                  </template>
                </q-file>
              </div>

              <div class="col-12">
                <q-file
                  v-model="uploadFiles.config_file"
                  :label="uploadFiles.config_file?'':'配置文件 (可选)'"
                  outlined
                  dense
                  counter
                  accept=".json,.yaml,.yml,.xml,.txt,.config"
                  hint="上传模型配置文件"
                >
                  <template v-slot:prepend>
                    <q-icon name="settings" />
                  </template>
                </q-file>
              </div>
            </div>

            <div class="row justify-end q-mt-md">
              <q-btn label="取消" color="grey-7" v-close-popup class="q-mr-sm" />
              <q-btn label="上传" type="submit" color="primary" :loading="submitting" />
            </div>
          </q-form>
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- 删除确认弹窗 -->
    <q-dialog v-model="deleteDialog" persistent>
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="delete" color="negative" text-color="white" />
          <span class="q-ml-sm">确认删除此模型文件?</span>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" color="primary" v-close-popup />
          <q-btn flat label="删除" color="negative" @click="deleteFile" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { usePlugin } from 'composables/plugin'
import { api } from 'boot/axios'
import { date } from 'quasar'
import { formatFileSize } from 'assets/utils'
import { useRoute } from 'vue-router'

const { notify, dialog } = usePlugin()
const route = useRoute()

// 搜索过滤器
const filterOptions = reactive({
  creator: '',
  name: '',
  model_type: null,
  status: null,
  start_date: '',
  end_date: ''
})

// 模型类型选项
const modelTypeOptions = [
  { label: '目标检测车', value: '目标检测车' },
  { label: '野战指挥车', value: '野战指挥车' },
  { label: '远程精式火箭炮', value: '远程精式火箭炮' },
  { label: '无人机', value: '无人机' },
  { label: '智能火控', value: '智能火控' },
  { label: '无人车', value: '无人车' }
]

// 状态选项
const statusOptions = [
  { label: '训练中', value: '训练中' },
  { label: '启用', value: '启用' },
  { label: '禁用', value: '禁用' },
  { label: '完成', value: '完成' },
  { label: '中断', value: '中断' }
]

// 表格配置
const loading = ref(false)
const selected = ref([])
const pagination = ref({
  rowsPerPage: 10,
  page: 1
})

const columns = [
  { name: 'select', align: 'center', label: '选择', field: 'select' },
  { name: 'name', align: 'left', label: '模型名称', field: 'name', sortable: true },
  { name: 'size', align: 'center', label: '模型大小', field: 'size', sortable: true },
  { name: 'uploader', align: 'center', label: '上传人', field: 'creater_name' },
  { name: 'task', align: 'center', label: '关联训练', field: 'task_name' },
  { name: 'status', align: 'center', label: '模型状态', field: 'model_status' },
  { name: 'model_type', align: 'center', label: '模型类型', field: 'model_type' },
  { name: 'create_time', align: 'center', label: '创建时间', field: 'model_create_time', sortable: true,
    format: val => date.formatDate(val, 'YYYY-MM-DD HH:mm:ss') },
  { name: 'operations', align: 'center', label: '操作', field: 'operations' }
]

// 模型文件数据
const modelFiles = ref([])

// 模型类型分布数据
const modelTypeDistribution = ref([
  { name: '目标检测车', percentage: 30, color: '#1976D2' },
  { name: '野战指挥车', percentage: 20, color: '#26A69A' },
  { name: '远程精式火箭炮', percentage: 15, color: '#FFC107' },
  { name: '无人机', percentage: 25, color: '#673AB7' },
  { name: '智能火控', percentage: 10, color: '#F44336' }
])

// 仿真环境存量数据
const diskUsage = ref(74)
const totalStorage = ref('384TB')
const usedStorage = ref('284.16TB')

// 弹窗控制
const uploadDialog = ref(false)
const deleteDialog = ref(false)
const fileToDelete = ref(null)
const submitting = ref(false)

// 上传相关
const uploadFiles = reactive({
  model_file: null,
  config_file: null
})

const newModel = reactive({
  name: '',
  desc: '',
  model_type: '',
  model_size: 20,
  model_status: '训练中'
})

// 获取状态颜色
function getStatusColor(status) {
  const colors = {
    '训练中': 'blue',
    '启用': 'green',
    '禁用': 'orange',
    '完成': 'teal',
    '中断': 'red'
  }
  return colors[status] || 'grey'
}

// 获取状态标签
function getStatusLabel(status) {
  return status || '未知'
}

// 获取模型文件列表
function fetchModelFiles() {
  loading.value = true
  console.log('开始获取模型列表')

  // 构建查询参数
  const params = new URLSearchParams()
  if (filterOptions.creator) params.append('creator', filterOptions.creator)
  if (filterOptions.name) params.append('name', filterOptions.name)
  if (filterOptions.model_type) params.append('model_type', filterOptions.model_type)
  if (filterOptions.status) params.append('status', filterOptions.status)
  if (filterOptions.start_date) params.append('start_date', filterOptions.start_date)
  if (filterOptions.end_date) params.append('end_date', filterOptions.end_date)

  api.get(`backend/models/?${params.toString()}`)
    .then(res => {
      // console.log('API返回数据:', res.data)

      if (res && Array.isArray(res.results)) {
        modelFiles.value = res.results.map(item => {
          console.log('处理模型项:', item)
          return {
            id: item.id,
            name: item.name || item.file || '未命名模型',
            size: formatFileSize(item.size || 0),
            creater_name: item.creater_name || '-',
            task_name: item.task_name || '-',
            model_status: item.model_status || '未知',
            model_type: item.model_type || '未分类',
            model_create_time: item.model_create_time || item.create_time,
            address: item.address || '',
            download_url: item.download_url || ''
          }
        })
        console.log('处理后的模型列表:', modelFiles.value)
      } else {
        console.error('API返回数据格式不正确:', res.data)
        notify('获取数据格式异常', 'negative')
        modelFiles.value = []
      }

      loading.value = false

      // 更新模型类型分布
      updateModelTypeDistribution()
    })
    .catch(err => {
      console.error('获取模型文件列表失败:', err)
      notify('获取模型文件列表失败')
      loading.value = false
      modelFiles.value = []
    })
}

// 更新模型类型分布数据
function updateModelTypeDistribution() {
  // 创建类型计数对象
  const typeCounts = {}
  let total = 0

  // 计算各类型数量
  modelFiles.value.forEach(file => {
    if (!typeCounts[file.model_type]) {
      typeCounts[file.model_type] = 0
    }
    typeCounts[file.model_type]++
    total++
  })

  if (total === 0) return

  // 固定的颜色映射
  const typeColorMap = {
    '目标检测车': '#1976D2',  // 蓝色
    '野战指挥车': '#26A69A',  // 青色
    '远程精式火箭炮': '#FFC107', // 黄色
    '无人机': '#673AB7',      // 紫色
    '智能火控': '#F44336',    // 红色
    '无人车': '#FF9800',      // 橙色
    '水面舰艇': '#4CAF50',    // 绿色
    '平台决策': '#E91E63',    // 粉色
    '通信组网': '#9C27B0',    // 深紫色
    '预警探测': '#607D8B'     // 蓝灰色
  }

  // 其他类型的备用颜色
  const fallbackColors = ['#795548', '#9E9E9E', '#3F51B5', '#009688', '#CDDC39', '#03A9F4', '#8BC34A', '#FF5722'];

  // 生成分布数据
  const distribution = [];
  let colorIndex = 0;

  for (const type in typeCounts) {
    const color = typeColorMap[type] || fallbackColors[colorIndex % fallbackColors.length];
    colorIndex++;

    distribution.push({
      name: type,
      percentage: Math.round((typeCounts[type] / total) * 100),
      color: color,
      count: typeCounts[type]
    });
  }

  // 按百分比从大到小排序
  distribution.sort((a, b) => b.percentage - a.percentage);

  modelTypeDistribution.value = distribution;
}

// 获取模型类型标签
function getModelTypeLabel(value) {
  const option = modelTypeOptions.find(opt => opt.value === value)
  return option ? option.label : value
}

// 查看文件详情
function viewFile(file) {
  console.log('查看文件', file)
  notify(`已加载"${file.name}"的详情信息`, 'positive')
}

// 下载模型
function downloadModel(file) {
  if (file.download_url) {
    window.open(import.meta.env.VITE_API + file.download_url)
  } else if (file.address) {
    window.open(import.meta.env.VITE_API + file.address)
  } else {
    notify('无法下载，文件地址不存在', 'negative')
  }
}

// 删除文件
function confirmDelete(file) {
  fileToDelete.value = file
  deleteDialog.value = true
}

function deleteFile() {
  if (fileToDelete.value) {
    loading.value = true

    api.delete(`backend/models/${fileToDelete.value.id}/`)
      .then(() => {
        const index = modelFiles.value.findIndex(f => f.id === fileToDelete.value.id)
        if (index !== -1) {
          modelFiles.value.splice(index, 1)
        }
        notify('删除成功', 'positive')
        fileToDelete.value = null
        loading.value = false

        // 更新模型类型分布
        updateModelTypeDistribution()
      })
      .catch(err => {
        console.error('删除文件失败:', err)
        notify('删除文件失败')
        loading.value = false
      })
  }
}

// 生成唯一的task_id
function generateTaskId() {
  const timestamp = new Date().getTime()
  const random = Math.floor(Math.random() * 10000)
  return `${timestamp}${random}`
}

// 上传模型文件
function onSubmit() {
  if (!uploadFiles.model_file) {
    notify('请选择模型文件')
    return
  }

  submitting.value = true

  const formData = new FormData()
  formData.append('name', newModel.name)
  formData.append('desc', newModel.desc || newModel.name)
  formData.append('model_type', newModel.model_type)
  formData.append('model_size', newModel.model_size)
  formData.append('model_status', newModel.model_status)
  formData.append('task_id', generateTaskId()) // 添加唯一的task_id

  if (uploadFiles.model_file) {
    formData.append('model_file', uploadFiles.model_file)
  }

  if (uploadFiles.config_file) {
    formData.append('config_file', uploadFiles.config_file)
  }

  api.post('backend/models/', formData)
    .then(res => {
      const newFile = {
        id: res.data.id,
        name: res.data.name,
        size: formatFileSize(res.data.size),
        creater_name: res.data.creater_name,
        model_status: res.data.model_status,
        model_type: res.data.model_type,
        model_create_time: res.data.model_create_time,
        address: res.data.address,
        download_url: res.data.download_url
      }

      modelFiles.value.unshift(newFile)
      uploadDialog.value = false
      notify('上传成功', 'positive')

      // 重置表单
      resetNewModelForm()

      // 更新模型类型分布
      updateModelTypeDistribution()
    })
    .catch(err => {
      console.error('上传失败:', err)
      notify('上传失败: ' + (err.message || '未知错误'), 'negative')
    })
    .finally(() => {
      submitting.value = false
    })
}

// 导出模型
function exportModel() {
  if (selected.value.length === 0) {
    notify('请选择要导出的模型', 'warning')
    return
  }

  const ids = selected.value.map(item => item.id).join(',')

  // 获取模型数据
  api.get('backend/models/', { 
    params: { 
      page_size: 1000,
      ids: ids
    } 
  })
    .then(response => {
      if (!response || !response.results) {
        notify('导出失败', 'negative')
        return
      }

      // 转换为CSV格式
      const headers = ['模型编号', '模型名称', '模型类型', '版本', '大小', '创建人', '状态', '创建时间', '描述']
      const rows = response.results.map(model => [
        model.id,
        model.name || '',
        model.model_type || '',
        model.version || '',
        model.size || '',
        model.creator || '',
        model.status || '',
        model.created_at || '',
        model.description || ''
      ])

      // 创建CSV内容
      const csvContent = [
        headers.join(','),
        ...rows.map(row => row.join(','))
      ].join('\n')

      // 创建并下载文件
      const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `模型列表_${date.formatDate(new Date(), 'YYYY-MM-DD_HH-mm')}.csv`)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      notify('导出成功', 'positive')
    })
    .catch(error => {
      console.error('导出失败:', error)
      notify('导出失败', 'negative')
    })
}

function resetNewModelForm() {
  newModel.name = ''
  newModel.desc = ''
  newModel.model_type = ''
  newModel.model_size = 20
  newModel.model_status = '训练中'
  uploadFiles.model_file = null
  uploadFiles.config_file = null
}

// 搜索功能
function handleSearch() {
  pagination.value.page = 1 // 重置到第一页
  fetchModelFiles()
}

function resetSearch() {
  // 重置所有过滤条件
  Object.keys(filterOptions).forEach(key => {
    if (typeof filterOptions[key] === 'boolean') {
      filterOptions[key] = null
    } else {
      filterOptions[key] = ''
    }
  })

  pagination.value.page = 1 // 重置到第一页
  fetchModelFiles()
}

// 生成饼图切片的SVG路径
function generatePieSlices() {
  const slices = [];
  const radius = 45;
  const centerX = 50;
  const centerY = 50;

  let startAngle = 0;

  modelTypeDistribution.value.forEach(item => {
    const angle = (item.percentage / 100) * 360;
    const endAngle = startAngle + angle;

    // 计算SVG路径
    const startRad = (startAngle - 90) * Math.PI / 180;
    const endRad = (endAngle - 90) * Math.PI / 180;

    const x1 = centerX + radius * Math.cos(startRad);
    const y1 = centerY + radius * Math.sin(startRad);
    const x2 = centerX + radius * Math.cos(endRad);
    const y2 = centerY + radius * Math.sin(endRad);

    // 确定是大弧还是小弧
    const largeArcFlag = angle > 180 ? 1 : 0;

    // 构建SVG path
    const path = `M ${centerX} ${centerY} L ${x1} ${y1} A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2} Z`;

    slices.push({
      path: path,
      color: item.color
    });

    startAngle = endAngle;
  });

  return slices;
}

// 初始加载
onMounted(() => {
  fetchModelFiles()

  // 检查URL参数，如果有openUploadDialog=true则自动打开上传对话框
  if (route.query.openUploadDialog === 'true') {
    uploadDialog.value = true
  }
})
</script>

<style scoped>
.full-height {
  height: 100%;
}

.model-distribution-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.pie-chart {
  width: 200px;
  height: 200px;
  position: relative;
  margin-bottom: 20px;
}

.pie-chart svg {
  width: 100%;
  height: 100%;
}

.pie-chart path {
  transition: transform 0.2s;
}

.pie-chart path:hover {
  transform: translateX(2px) translateY(2px);
  filter: drop-shadow(0px 0px 3px rgba(0,0,0,0.3));
}

.pie-chart-legend {
  width: 100%;
  max-width: 300px;
  display: flex;
  flex-direction: column;
  margin: 0 auto;
}

.pie-chart-legend .flex {
  justify-content: flex-start;
  margin-bottom: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.pie-chart-legend .flex:hover {
  background-color: rgba(0,0,0,0.05);
}

.text{
    padding-left: 0;
    position: relative;
    left: 4px;
    top: 6px;
    width: 11px;
}
@media (max-width: 599px) {
  .col-md-7 {
    margin-bottom: 16px;
  }
  
  .col-md-7 .row {
    margin: 0;
  }
  
  .col-5 {
    padding: 0 4px;
  }
  
  .text {
    line-height: 40px;
  }
  
  .flex.justify-start {
    justify-content: flex-start;
  }
  
  .q-btn {
    min-width: 80px;
  }
}

:deep(.q-field--auto-height.q-field--labeled .q-field__control-container) {
  padding-top: 0;
}

:deep(.q-field--labeled .q-field__native) {
  padding-top: 0;
  padding-bottom: 0;
}

:deep(.q-textarea.q-field--dense.q-field--labeled .q-field__control-container) {
  padding: 0 !important;
}

:deep(.q-textarea.q-field--dense.q-field--labeled .q-field__native) {
  height: 100% !important;
  line-height: 36px;
}
</style>
