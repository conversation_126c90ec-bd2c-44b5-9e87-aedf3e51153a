<!--
 * @Author: Szc
 * @Date: 2025-08-01 08:55:51
 * @LastEditors: Szc
 * @LastEditTime: 2025-08-02 10:15:38
 * @Description: 
-->

<template>
    <div class="fixed-container">
        <transition name="ai-window" appear
            enter-active-class="animate__animated animate__bounceInRight"
            leave-active-class="animate__animated animate__bounceOutRight">
            <Vue3DraggableResizable v-if="showAiWindow" class="DrageContainer" class-name-handle="my-handle-class"
                :w="originW" :h="aiImHeight" :x="originPosX" :y="originPosY" @dragging="onDrag" @resizing="onResize">
                <AiIm ref="aiImRef" class="smallWindow" :width="originW" :height="aiImHeight">
                </AiIm>
            </Vue3DraggableResizable>
        </transition>
    </div>
</template>

<script setup>
import { ref, nextTick, onMounted, computed, onUnmounted, watch } from 'vue'
import AiIm from '../ai-interaction/IndexPage.vue'
import { useAiStore } from '../../stores/aiImStore'
import { storeToRefs } from 'pinia'

const AiStore = useAiStore()
const { showAiWindow } = storeToRefs(AiStore)


const aiImRef = ref(null);//实例

const originW = ref(500);
const aiImHeight = ref(null);//高度

const baseRight = 50; // 右边距
const baseBottom = 100; // 下边距(px)

const originPosX = ref(0)
const originPosY = ref(0)


const initializePosition = () => {
    nextTick(() => {
        if (aiImRef.value && aiImRef.value.$el) {
            const height = aiImRef.value.$el.clientHeight;
            aiImHeight.value = height;
            console.log("获取到组件高度:", aiImHeight.value);

            if (aiImHeight.value > 0) {
                calculateInitialPosition();
            } else {
                // 因false变为true时，第一时间没有宽高，我这里利用宏任务再次计算。
                setTimeout(() => {
                    if (aiImRef.value && aiImRef.value.$el) {
                        const retryHeight = aiImRef.value.$el.clientHeight;
                        if (retryHeight > 0) {
                            aiImHeight.value = retryHeight;
                            console.log("重试获取到组件高度:", aiImHeight.value);
                            calculateInitialPosition();
                        }
                    }
                }, 100);
            }
        }
    });
}


watch(showAiWindow, (newVal) => {
    if (newVal) {
        initializePosition();
    }
})

onMounted(() => {
    if (showAiWindow.value) {
        initializePosition();
    }
    
    window.addEventListener('resize', calculateInitialPosition);
})

onUnmounted(() => {
    window.removeEventListener('resize', calculateInitialPosition);//防止内存溢出
})

// 初始位置 
const calculateInitialPosition = () => {
    if (!aiImHeight.value) {
        console.log("组件高度未获取到，不计算位置");
        return;
    }

    console.log("窗口尺寸:", window.innerWidth, "x", window.innerHeight);
    console.log("组件尺寸:", originW.value, "x", aiImHeight.value);
    console.log("期望边距 - 右:", baseRight + "px", "下:", baseBottom + "px");

    originPosX.value = window.innerWidth - originW.value - baseRight;
    originPosY.value = window.innerHeight - aiImHeight.value - baseBottom;

    originPosX.value = Math.max(0, originPosX.value);
    originPosY.value = Math.max(0, originPosY.value);
    console.log(" originPosX.value>>>", originPosX.value)
    console.log(" originPosY.value>>>", originPosY.value)
    console.log("右下角位置>>>", {
        right: window.innerWidth - (originPosX.value + originW.value) + "px",
        bottom: window.innerHeight - (originPosY.value + aiImHeight.value) + "px"
    });
};

const onDrag = (x, y) => {
    console.log('Dragging', x, y)
    // document.body.classList.add('resizing');
}

const onResize = ({ x, y, w, h }) => {
    console.log('Resizing', x, y, w, h)
    originW.value = w;
    aiImHeight.value = h;
    // document.body.classList.add('resizing');
}
</script>

<style lang="scss" scoped>
.fixed-container{

}
.DrageContainer {
    position: fixed;
    /* 基于父容器定位 */
    z-index: 999;
}

.smallWindow {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 666;
    // border: 1px solid greenyellow;
}
</style>

<style>
.my-handle-class {
    position: absolute;
    background-color: pink;
    border: 1px solid black;
    border-radius: 50%;
    height: 14px;
    width: 14px;
    box-model: border-box;
    -webkit-transition: all 300ms linear;
    -ms-transition: all 300ms linear;
    transition: all 300ms linear;
}

.my-handle-class-tl {
    top: -14px;
    left: -14px;
    cursor: nw-resize;
}

.my-handle-class-tm {
    top: -14px;
    left: 50%;
    margin-left: -7px;
    cursor: n-resize;
}

.my-handle-class-tr {
    top: -14px;
    right: -14px;
    cursor: ne-resize;
}

.my-handle-class-ml {
    top: 50%;
    margin-top: -7px;
    left: -14px;
    cursor: w-resize;
}

.my-handle-class-mr {
    top: 50%;
    margin-top: -7px;
    right: -14px;
    cursor: e-resize;
}

.my-handle-class-bl {
    bottom: -14px;
    left: -14px;
    cursor: sw-resize;
}

.my-handle-class-bm {
    bottom: -14px;
    left: 50%;
    margin-left: -7px;
    cursor: s-resize;
}

.my-handle-class-br {
    bottom: -14px;
    right: -14px;
    cursor: se-resize;
}

.my-handle-class-tl:hover,
.my-handle-class-tr:hover,
.my-handle-class-bl:hover,
.my-handle-class-br:hover {
    background-color: rgba(51, 102, 255, 1);
    transform: scale(1.4);
}
</style>