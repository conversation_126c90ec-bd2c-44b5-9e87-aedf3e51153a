<template>
  <q-layout style="padding-top: 20px;">
    <q-page-container>
      <q-page class="ai-interaction-page">
        <div class="labelColor PageTitle">综合管理/人机智能交互</div>
        <div class="row">
          <!-- Sidebar Navigation -->
          <div style="width:auto; height:inherit" class="leftSlide">

            <div class="flexbox" style="height: 36px !important;">
              <q-input v-model="searchHistory" outlined dense :label="searchHistory ? '' : '请输入'">
                <template v-slot:append>
                  <q-btn flat round size="sm">
                    <img src="../../assets/images/search.png" alt="">
                  </q-btn>
                </template>
              </q-input>

              <q-btn outline style="flex: 0 0 auto;margin-left: 10px;" class="roundBox grayBox"
                @click="resetSearch">清空</q-btn>
            </div>


            <q-list bordered class="q-mt-md">
              <q-item v-for="(item, index) in menuItems" :key="index" clickable @click="selectMenuItem(item)">
                <q-item-section class="font14">{{ item }}</q-item-section>
              </q-item>
            </q-list>

          </div>


          <!-- Content Panel -->
          <div class="q-pa-md col" style="display: flex;flex-direction: column;min-height:9rem;">

            <div style="overflow-y: auto;flex: 1;">

              <div v-for="(message, index) in messages" :key="index" class="message">
                <div v-if="message.type == 'question'" class="question">
                  <div class="que-content">{{ message.content }}</div>
                </div>

                <q-card v-else flat bordered class="content-card answer" style="background: transparent !important;">
                  <div class="cutomReply" style="white-space: pre-line">{{ message.content }}</div>
                </q-card>
              </div>

            </div>

            <div class="buttonArea">
              <q-btn class="roundBox" color="primary" label="文件" style="margin-right: 12px;"></q-btn>
              <q-btn class="roundBox" color="primary" label="图片"></q-btn>
            </div>


            <div class="operationArea">
              <div class="flexbox">
                <q-btn flat rounded class="noP" color="primary" @click="startVoiceRecognition">
                  <img src="../../assets/images/voice.png" alt="">
                </q-btn>
                <q-input v-model="inputContent" @keyup.enter="sendMessage" filled autogrow dense type="textarea"
                  :label="inputContent ? '' : '请输入消息...'" class="col" />
                <q-btn flat color="primary" @click="sendMessage">
                  <img src="../../assets/images/sendMsg.png" alt="">
                </q-btn>
              </div>
            </div>


          </div>


        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script setup>
import { ref } from 'vue'
const searchHistory = ref('')
const inputContent = ref('')
const articleTitle = ref('介绍一下远程箱式火箭炮')
const articleContent = ref(`远程箱式火箭炮是一种现代化、高性能的远程打击武器系统。以下是对其的详细介绍：

一、定义与特点
远程箱式火箭炮，顾名思义，是一种采用箱式发射装置的远程火箭炮系统。它通过将多枚火箭弹封存于一个或多个发射单元内，实现了高密度、高速度的火力输出。这种武器系统具有射程远、精度高、机动性强以及生存能力强等显著特点。

二、射程与精度
远程箱式火箭炮的射程取决于弹型，能够覆盖广阔的区域。根据不同的弹药类型，其射程可达数十至数百公里，甚至可以与一些常规的、中程弹术导弹相媲美。同时，该武器系统还具备精确打击能力，数公里范围内的打击精度可达米级，能够实现对固定目标、移动自主寻的目标和动态目标的精确打击。

三、模块化设计
模块化设计是远程箱式火箭炮的又一突出特点。它采用多个独立发射架的结构，可以根据不同的作战任务和需求，灵活选择弹药类型。这种设计使得火箭炮面对不同类型的目标时，能够有针对性地选择最合适的弹药，实现多层次火力打击效果和总体设计性。

四、弹药兼容性
远程箱式火箭炮具备强大的弹药兼容性，能够发射多种不同类型的弹药。这些弹药包括常规的破甲弹、子母弹、反装甲末敏弹、云爆弹药、各具特点且具有魔力的打击效能弹药。丰富的弹药选择使得火箭炮的实际战斗力得到进一步提升。

五、高效装填与再装填系统
远程箱式火箭炮拥有高效的装填和再装填系统。火箭弹的发射管和储藏管是二为一，并且每个发射储藏管对应在一个发射箱内。这种设计使得新的箱式装填只分方位，只需将发射箱吊装到位，确保在激烈的战斗中能够持续对敌人进行打击。

六、战场应用与战略意义
远程箱式火箭炮在现代战场上有广泛应用。它可以作为远程火力支援的工具，为地面部队提供火力掩护与深度打击；也可以独立行动，对敌方关键目标进行精准攻击。此外，该武器系统还具备迅速部署的能力，能够在短时间内实现部署与转移，提高战争中的生存生存率。

从战略意义上看，远程箱式火箭炮的出现改变了地面战场的作战模式，使得车在战场上具有了更强大的远程打击能力。同时，它也为中国在国际军事市场上赢得了更多的话语权，展示了中国军事科技的进步。

综上所述，远程箱式火箭炮是一种现代化、高性能的远程打击武器系统，具有射程远、精度高、机动性强、生存能力高以及强大弹药兼容性等显著特点。它在战场上的应用极为广泛，具有重要的战略意义。`)

const menuItems = [
  '介绍一下本软件操作流程',
  '介绍一下远程箱式火箭炮',
  '目标识别模型存储位置',
  '如何训练模型',
  '如何新建算法',
  '如何验证算法',
  '如何查看服务器状态',
  '查看摄像头',
  '介绍一下目标识别算法',
  '说明能力情况'
]

let messages = ref([
  {
    type: 'question',
    content: '介绍一下远程箱式火箭炮'
  },
  {
    type: 'answer',
    content: `远程箱式火箭炮是一种现代化、高性能的远程打击武器系统...` // 保持原有内容
  }
])


const resetSearch = () => {

}


// 语音识别相关变量
const recognizing = ref(false) // 是否正在识别
const voiceResult = ref('')   // 语音识别结果
let recognition = null        // 语音识别实例

/**
 * 点击语音按钮，启动语音识别
 * 识别结果通过console.log输出，并保存到voiceResult变量
 */
function startVoiceRecognition() {
  // 检查浏览器是否支持Web Speech API
  if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
    alert('当前浏览器不支持语音识别')
    return
  }
  // 如果正在识别，则停止
  if (recognizing.value) {
    recognition && recognition.stop()
    recognizing.value = false
    return
  }
  // 创建语音识别实例
  const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
  recognition = new SpeechRecognition()
  recognition.lang = 'zh-CN' // 设置识别语言为中文
  recognition.interimResults = false // 不要实时，只要最终结果
  recognition.maxAlternatives = 1 // 最多一个候选

  // 识别开始时回调
  recognition.onstart = () => {
    recognizing.value = true
    console.log("语音识别开始👇")
  }
  // 识别到结果时回调
  recognition.onresult = (event) => {
    if (event.results && event.results[0] && event.results[0][0]) {
      voiceResult.value = event.results[0][0].transcript
      console.log('语音识别结果:', voiceResult.value)
    }
  }
  // 识别出错时回调
  recognition.onerror = (event) => {
    console.log("event", event)
    alert('语音识别失败')
    recognizing.value = false
  }
  // 识别结束时回调
  recognition.onend = () => {
    recognizing.value = false
    console.log('语音识别结束..')
  }
  // 启动识别
  recognition.start()
}

function goBack() {
  window.close()
  // 如果window.close()不起效果，则返回到上一页
  window.history.back()
}

function selectMenuItem(item) {
  console.log('item>>>', item)
  articleTitle.value = item
  if (item === '介绍一下远程箱式火箭炮') {
    // 保持现有内容
  } else {
    articleContent.value = `${item} 的内容正在加载中...`//这么写可能是获取到历史回答内容，然后展示
  }
}

function sendMessage() {
  if (inputContent.value.trim()) {
    // 添加用户问题
    messages.value.push({
      type: 'question',
      content: inputContent.value
    })

    // 添加系统回复（这里只是示例，实际应该等待API响应）
    messages.value.push({
      type: 'answer',
      content: '正在处理您的请求1...'
    })
    inputContent.value = ''
  }
}
</script>

<style lang="scss" scoped>
.q-card>div:not(:last-child) {
  border-bottom-left-radius: 5px !important;
  border-bottom-right-radius: 5px !important;
}

.q-layout {
  min-height: 0 !important;
}

.q-page-container {
  background: #0a1828;
}

.ai-interaction-page {
  border: 1px solid transparent !important;
  border-image: linear-gradient(to right, #64778a, #293f64) 1 !important;
  min-height:0 !important;
}

.content-card {
  display: flex;
  flex-direction: column;
  // height: calc(100vh - 170px);
  border-radius: 5px !important;
  border: none !important;

  .q-card__section:first-child {
    flex: 1;
    overflow-y: auto;
  }
}

// 偶数行背景色
.q-list .q-item:nth-child(even) {
  background-color: rgba(0, 89, 130, .15);
}

.PageTitle {
  font-size: .25rem;
  padding: .2rem .3rem;
  border-bottom: .0125rem solid rgba(60, 102, 164, 1);
}

.leftSlide {
  padding: .1875rem;
  color: white;
  position: relative;

  &::after {
    position: absolute;
    content: '';
    width: .025rem;
    height: 95%;
    background: linear-gradient(to bottom, #64778a, #293f64) !important;
    right: 0px;
    top: 2%;
    z-index: 100;
  }
}

// 问题容器
.question {
  padding-left: .45rem !important;
  color: white;
  margin-bottom: .1875rem;

  .que-content {
    //内容
    padding:.1875rem;
    border-radius: .0625rem;
    background: #2f3a48;
    font-size: .2rem;
  }
}

.answer {
  padding-right: .45rem !important;
  margin-bottom: .125rem;
}

.cutomReply {
  padding: .1875rem;
  background: #2f3a48;
  border-radius: .0625rem;
  color: white;
  font-size: .175rem;
}

.buttonArea {
  margin-top: .3rem;
}

.operationArea {
  margin-top:.2rem;
  padding: .2125rem .25rem;
  border-radius: .0625rem !important;
  background: rgba(0, 0, 0, .65);
}

.noP {
  padding-left: 0 !important;
}

.flexbox {
  :deep(.q-focus-helper) {
    width: fit-content;
  }

  :deep(.q-field__native) {
    // padding-top: 10px !important;
  }

  :deep(.q-textarea.q-field--dense.q-field--labeled .q-field__native) {
    padding-top: .125rem !important;
  }
}

.font14{
  font-size: .175rem;
}
</style>
