<template>
  <div class="q-pa-md">
    <!-- Header Section - 改为卡片式布局，智能模型和快捷操作左右分隔 -->
    <!-- <q-card flat bordered class="q-mb-md myCard"> -->
    <div class="row mb">
      <!-- 左侧：智能模型介绍 -->
      <div class="col-12 col-md-6 showBorder">
        <q-card-section class="h-100">
          <div class="labelColor">
            <span class="title"> 智能模型：</span>
            <span class="content">
              具有人机交互能力、业务模型调用能力，通过与指挥员语音交互，自动调用其它装备的算法模型完成任务。
            </span>
          </div>
        </q-card-section>
      </div>

      <div style="width: 20px;"></div>

      <!-- 右侧：快捷操作 -->

        <div class="col-12 col-md-5 showBorder cus-bg" style="flex: 1;position: relative;">
          
          <q-btn style="flex:1;width:100%">
        <q-card-section class="cusCard" style="flex:1">
          <!-- <q-btn  style="background:#ffb700">开始训练→</q-btn> -->
          <div class="row q-col-gutter-sm cardContent" style="justify-content: space-evenly;">

            <div class="flexbox">
               <div class="labelColor mr textSize" @click="startTraining">开始训练</div>
              <img src="../../assets/images/entry.png" alt="">
            </div>

            <!-- <div class="flexbox">
              <img src="../../assets/images/logo2.png" alt="">
              <div class="labelColor mr font14" @click="importModel">导入算法模型</div>
            </div>

            <div class="flexbox">
              <img src="../../assets/images/logo3.png" alt="">
              <div class="labelColor mr font14"  @click="uploadDataset">上传数据集</div>
            </div>

            <div class="flexbox">
              <img src="../../assets/images/logo4.png" alt="">
              <div class="labelColor mr font14" @click="startValidation">开始验证</div>
            </div> -->

          </div>
        </q-card-section>
        </q-btn>
        </div>

    </div>
    <!-- </q-card> -->

    <!-- 算法版本展示 - 跑马灯效果，保留原卡片结构和rem宽高 -->
    <div class="marquee-container" @click="resetMarquee">
      <div class="labelColor font16 algotitle botLine">综合管理</div>
      <div class="marquee-content" ref="marqueeContent" :style="{ transform: `translateX(-${translateX}px)` }">
        <div class="quard-item marquee-item" v-for="(algo, index) in displayedItems" :key="index"
          @click="toggleImageSize(algo)" :class="{ 'enlarged': selectedItem === algo || isCenterImage(index) }">
          <q-card class="algorithm-card" :class="{ 'selected-algorithm': currentAlgorithm === algo.id }"
            @click="currentAlgorithm = algo.id">
            <q-img :src="algo.image_url || defaultBackground" style="height:100%">
              <template v-slot:error>
                <div class="absolute-full flex flex-center bg-grey-3 text-grey-7">
                  <q-icon name="image" size="2em" />
                </div>
              </template>
            </q-img>
            <div class="imgType">{{ algo.version }}</div>
            <div class="imgName">{{ algo.name }}</div>
          </q-card>
        </div>
      </div>
    </div>

    <!-- 训练任务和模型评估 - 使用卡片式布局 -->
    <div class="row q-col-gutter-md">
      <!-- 训练任务表格 -->
      <div class="col-6">
        <q-card flat bordered>
          <q-card-section style="padding-left:0 !important;padding-right:0 !important;">
            <div class="labelColor font16 botLine">训练任务</div>
            <!-- <q-separator class="line" /> -->

            <div v-if="loadingTasks" class="flex flex-center q-pa-lg">
              <q-spinner color="primary" size="3em" />
              <div class="q-ml-sm labelColor font16">加载训练任务中...</div>
            </div>

            <div v-else-if="taskError" class="text-negative q-pa-md">
              {{ taskError }}
              <q-btn flat color="primary" label="重试" @click="fetchTrainTasks" class="q-ml-sm" />
            </div>

            <q-table v-else :rows="trainTasks" :columns="trainColumns" row-key="id" flat bordered
              :loading="loadingTasks" :pagination="taskPagination" @request="onTaskRequest" no-data-label="暂无训练任务数据">
              <template v-slot:body-cell-status="props">
                <q-td :props="props">
                  <span :class="`text-${trainingStatus[props.value]?.color}`" class="fs-16">
                    {{ trainingStatus[props.value]?.label }}
                  </span>
                </q-td>
              </template>
              <template v-slot:body-cell-action="props">
                <q-td :props="props">
                  <q-btn flat label="日志" color="primary" size="sm" class="q-ml-sm" @click="showLog(props.row.id)" />
                  <q-btn flat label="中断" color="primary" size="sm" class="q-ml-sm" v-if="props.row.status == 2"
                    @click="suspendTraining(props.row)" />
                  <q-btn flat label="恢复" color="primary" size="sm" class="q-ml-sm"
                    v-if="props.row.status == 11 || props.row.status == 3" @click="resumeTraining(props.row)" />
                  <q-btn flat label="详情" color="primary" size="sm" class="q-ml-sm"
                    :to="`/training/detail/${props.row.id}`" />
                  <q-btn flat label="删除" color="negative" size="sm" class="q-ml-sm" @click="onDelete(props.row.id)" />
                </q-td>
              </template>
            </q-table>
          </q-card-section>
        </q-card>
      </div>

      <!-- 模型评估 -->
      <div class="col-6">
        <q-card flat bordered>
          <q-card-section style="padding-left:0 !important;padding-right:0 !important;">
            <div class="mb10 labelColor font16 botLine">模型评估</div>
            <!-- <q-separator style="margin-bottom:.25rem;width:100% !important;" /> -->
            <div style="min-height: 5rem;display: flex;flex-wrap: nowrap;">
              <!-- 雷达图：评估指标 -->
              <div ref="radarChart" style="flex: 1;height:4.5rem;top:.25rem"></div>
              <!-- 柱状图：总体评价 -->
              <div ref="barChart" style="flex: 1;height:4.5rem;top:.25rem"></div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </div>

  <!-- 添加算法弹窗 -->
  <q-dialog v-model="showAddAlgorithmDialog">
    <q-card style="width: 600px; max-width: 95vw;">
      <q-card-section class="row items-center">
        <div class="labelColor ">添加算法</div>
        <q-space />
        <q-btn icon="close" flat round dense v-close-popup />
      </q-card-section>

      <q-card-section class="q-pt-none">
        <q-form @submit="onSubmitAlgorithm" class="q-gutter-md">
          <q-input v-model="newAlgorithm.name" :label="newAlgorithm.name ? '' : '算法名称'"
            :rules="[val => !!val || '请输入算法名称']" outlined dense />

          <q-input v-model="newAlgorithm.artifact_name" :label="newAlgorithm.artifact_name ? '' : '镜像名称'"
            :rules="[val => !!val || '请输入镜像名称']" outlined dense />

          <q-input v-model="newAlgorithm.desc" :label="newAlgorithm.desc ? '' : '算法描述'" type="textarea"
            :rules="[val => !!val || '请输入算法描述']" outlined autogrow />

          <div class="row justify-end q-mt-md">
            <q-btn label="取消" color="grey-7" v-close-popup class="q-mr-sm roundBox" />
            <q-btn label="确定" type="submit" color="primary" class="roundBox" :loading="submitting" />
          </div>
        </q-form>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed, nextTick } from 'vue'
import { api } from 'boot/axios'
import { usePlugin } from 'composables/plugin.js'
import { date } from 'quasar'
import { useRouter } from 'vue-router'
import { trainingStatus } from 'assets/const'
import { convertSecondsToHMS } from 'assets/utils'
import defaultBackground from 'assets/images/smart_model_backgroud.png'
import logo1 from 'assets/images/logo1.png'
import algoImg from 'assets/images/algo.png'
import * as echarts from 'echarts'
import { colProps } from 'element-plus'

const { notify, dialog, customComponentDialog } = usePlugin()
const router = useRouter()

// 添加算法相关的状态
const showAddAlgorithmDialog = ref(false)
const submitting = ref(false)
const newAlgorithm = ref({
  name: '',
  artifact_name: '',
  desc: ''
})

// 图表引用
const radarChart = ref(null)
const barChart = ref(null)
let radarChartInstance = null
let barChartInstance = null

// 算法列表数据
const algorithms = ref([])
const currentAlgorithm = ref(null)
const loadingAlgorithms = ref(false)
const algorithmError = ref('')

// 训练任务数据
const trainTasks = ref([])
const loadingTasks = ref(false)
const taskError = ref('')
const taskPagination = ref({
  sortBy: 'start_time',
  descending: true,
  page: 1,
  rowsPerPage: 5,
  rowsNumber: 0
})

// 跑马灯相关状态
const marqueeContent = ref(null)
const translateX = ref(0)
const speed = 3
let intervalId = null
const selectedItem = ref(null)
const isClick = ref(false)
const isClickItem = ref(null)

const displayedItems = computed(() => [...algorithms.value, ...algorithms.value])

function getCardWidth() {
  const card = document.querySelector('.quard-item')
  return card ? card.offsetWidth : 100
}
function getMargin() {
  // quard-item margin-right: .5rem
  return parseFloat(getComputedStyle(document.documentElement).fontSize) * 0.5
}
function getContainerWidth() {
  const container = document.querySelector('.marquee-container')
  return container ? container.offsetWidth : 0
}
function getItemsLen() {
  return algorithms.value.length
}
const centerItemIndex = computed(() => {
  const cardWidth = getCardWidth()
  const margin = getMargin()
  const itemsLen = getItemsLen()
  const containerWidth = getContainerWidth()
  return Math.floor((translateX.value + (containerWidth / 2)) / (cardWidth + margin)) % itemsLen
})
function startMarquee() {
  clearInterval(intervalId)
  intervalId = setInterval(() => {
    translateX.value += speed
    if (marqueeContent.value && translateX.value >= marqueeContent.value.scrollWidth / 2) {
      translateX.value = 0
    }
  }, 20)
}
function toggleImageSize(item) {
  console.log(3, item, selectedItem.value);

  if (selectedItem.value === item) {
    console.log(1); //再次
    isClick.value = false
    selectedItem.value = null
    startMarquee()
  } else {
    console.log(2);//首次
    isClick.value = true
    isClickItem.value = item
    selectedItem.value = item
    clearInterval(intervalId)
  }
}
function resetMarquee() {
  return
  console.log('重置')
  selectedItem.value = null
  isClickItem.value = null
  isClick.value = false
  startMarquee()
}
function isCenterImage(index) {
  const itemsLen = getItemsLen()
  return (index === centerItemIndex.value || index === centerItemIndex.value + itemsLen) && !selectedItem.value
}

// 获取算法列表
async function fetchAlgorithms() {
  loadingAlgorithms.value = true
  algorithmError.value = ''

  try {
    // 调用算法列表API  实际/backend/algorithms
    const response = await api.get('/backend/algorithms')
    if (response && response.results) {
      algorithms.value = response.results
      if (algorithms.value.length > 0) {
        currentAlgorithm.value = algorithms.value[0].id
      } else {
        currentAlgorithm.value = 'empty'
      }
    } else {
      throw new Error('获取数据格式错误')
    }
  } catch (error) {
    console.error('获取算法列表失败', error)
    // algorithmError.value = '获取算法列表失败：' + (error.response?.data?.message || error.message)

    // 设置一些演示数据（仅用于开发阶段）
    algorithms.value = [
      { id: 'v1', name: 'SSD算法', version: 'v1.0.2', image_url: algoImg },
      { id: 'v2', name: 'YOLO算法', version: 'v2.1.3', image_url: 'assets/images/version2.jpg' },
      { id: 'v3', name: 'R-CNN算法', version: 'v1.5.0', image_url: 'assets/images/version3.jpg' },
      { id: 'v4', name: 'BERT模型', version: 'v2.0.1', image_url: 'assets/images/version4.jpg' },
      { id: 'v4', name: 'BERT模型', version: 'v2.0.1', image_url: 'assets/images/version4.jpg' },
      { id: 'v4', name: 'BERT模型', version: 'v2.0.1', image_url: 'assets/images/version4.jpg' },
      { id: 'v4', name: 'BERT模型', version: 'v2.0.1', image_url: 'assets/images/version4.jpg' },
      { id: 'v4', name: 'BERT模型', version: 'v2.0.1', image_url: 'assets/images/version4.jpg' },
      { id: 'v4', name: 'BERT模型', version: 'v2.0.1', image_url: 'assets/images/version4.jpg' },
      { id: 'v4', name: 'BERT模型', version: 'v2.0.1', image_url: 'assets/images/version4.jpg' },
    ]
    currentAlgorithm.value = 'v1'
    console.log('catch', algorithms.value)
  } finally {
    loadingAlgorithms.value = false
    // 设置一些演示数据（仅用于开发阶段）
    algorithms.value = [
      { id: 'v1', name: 'SSD算法', version: '协同控制', image_url: algoImg },
      { id: 'v2', name: 'YOLO算法', version: '北方自控', image_url: algoImg },
      { id: 'v3', name: 'R-CNN算法', version: '协同控制', image_url: algoImg },
      { id: 'v4', name: 'YoloV8算法', version: '北方自控', image_url: algoImg },
      { id: 'v4', name: 'YoloV8算法', version: '协同控制', image_url: algoImg },
      { id: 'v4', name: 'YoloV8算法', version: '北方自控', image_url: algoImg },
      { id: 'v4', name: 'YoloV8算法', version: '协同控制', image_url: algoImg },
      { id: 'v4', name: 'YoloV8算法', version: '北方自控', image_url: algoImg },
      { id: 'v4', name: 'YoloV8算法', version: '协同控制', image_url: algoImg },
      { id: 'v4', name: 'YoloV8算法', version: '北方自控', image_url: algoImg },
    ]
  }
}

// 获取训练任务列表
async function fetchTrainTasks() {
  loadingTasks.value = true
  taskError.value = ''

  try {
    // 构造查询参数
    const params = {
      page: taskPagination.value.page,
      page_size: taskPagination.value.rowsPerPage,
      ordering: taskPagination.value.descending ? '-' + taskPagination.value.sortBy : taskPagination.value.sortBy
    }

    // 调用训练任务列表API
    const response = await api.get('/backend/tasks/', { params })

    if (response && response.results) {
      trainTasks.value = response.results
      taskPagination.value.rowsNumber = response.count || 0
    } else {
      throw new Error('获取数据格式错误')
    }
  } catch (error) {
    console.error('获取训练任务列表失败', error)
    taskError.value = '获取训练任务列表失败：' + (error.response?.data?.message || error.message)
    // 设置一些演示数据（仅用于开发阶段）
    trainTasks.value = [
      { id: '001', name: 'SSD-22-06-13', status: 2, start_time: '2022-06-14 23:50:21', running_time: 3600, creater_name: '张三', task_id: 'task_001' },
      { id: '002', name: 'R-CNN-23-09-23', status: 4, start_time: '2023-09-23 20:00:57', running_time: 7200, creater_name: '李四', task_id: 'task_002' },
      { id: '003', name: 'Yolo4-24-11-06', status: 3, start_time: '2024-11-06 13:45:13', running_time: 1800, creater_name: '王五', task_id: 'task_003' },
      { id: '004', name: 'Yolo4-10-23', status: 2, start_time: '2024-10-23 05:27:06', running_time: 5400, creater_name: '赵六', task_id: 'task_004' },
      { id: '005', name: 'Yolo8-22-01-22', status: 11, start_time: '2022-01-22 17:01:15', running_time: 9000, creater_name: '钱七', task_id: 'task_005' },
    ]
  } finally {
    loadingTasks.value = false
  }
}

// 处理任务表格分页请求
function onTaskRequest(props) {
  const { page, rowsPerPage, sortBy, descending } = props.pagination
  taskPagination.value.page = page
  taskPagination.value.rowsPerPage = rowsPerPage
  taskPagination.value.sortBy = sortBy || 'start_time'
  taskPagination.value.descending = descending

  fetchTrainTasks()
}

// 表格列定义
const trainColumns = [
  { name: 'name', field: 'name', label: '训练名称', align: 'center' },
  {
    name: 'allocation',
    label: '资源配置',
    align: 'center',
    format: (val, row) => `${row.actor_num * row.actor_per_cpu + row.learner_num * row.learner_per_cpu || '未知'} CPU`
  },
  { name: 'creator', field: 'creater_name', label: '创建人员', align: 'center' },
  {
    name: 'startTime',
    field: 'start_time',
    label: '开始时间',
    align: 'center',
    sortable: true,
    format: val => typeof val === 'string' ? val : date.formatDate(val, 'YYYY-MM-DD HH:mm:ss')
  },
  {
    name: 'runningTime',
    field: 'running_time',
    label: '训练时长',
    align: 'center',
    sortable: true,
    format: val => convertSecondsToHMS(val)
  },
  { name: 'status', field: 'status', label: '训练状态', align: 'center', sortable: true },
  { name: 'action', label: '操作栏', align: 'center' }
]

// 训练任务操作函数
function showLog(id) {
  console.log('查看日志:', id)
  // 实际项目中应调用日志对话框
}

function suspendTraining(row) {
  dialog('确认中断吗？').onOk(() => {
    api
      .post(`backend/tasks/suspending/`, {
        task_id: row.task_id,
      })
      .then(() => {
        notify('中断成功', 'positive')
        fetchTrainTasks()
      })
      .catch(() => {
        notify('中断失败')
      })
  })
}

function resumeTraining(row) {
  dialog('确认恢复吗？').onOk(() => {
    api
      .post(`backend/tasks/resume/`, {
        task_id: row.task_id,
      })
      .then(() => {
        notify('恢复成功', 'positive')
        fetchTrainTasks()
      })
      .catch(() => {
        notify('恢复失败')
      })
  })
}

function onDelete(id) {
  dialog('确认删除吗？').onOk(() => {
    api
      .delete(`backend/tasks/${id}/`)
      .then(() => {
        notify('删除成功', 'positive')
        fetchTrainTasks()
      })
      .catch(() => {
        notify('删除失败')
      })
  })
}

// 初始化图表
function initCharts() {
  // 初始化雷达图
  if (radarChart.value) {
    radarChartInstance = echarts.init(radarChart.value)
    const radarOption = {
      title: {
        text: '',
      },
      tooltip: {
        // trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        textStyle: {
          fontSize: window.screen.width > 1536 ? '20' : '14',  // 文字大小设为14px
          color: '#333',  // 文字颜色设为#333
        }
      },
      legend: {
        show: false,
        data: ['模型性能'],
        textStyle: {
          color: '#fff',
          fontSize: 14,
        },
        top: '0px',
        left: '0px'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        top: '15%',
        containLabel: true
      },
      radar: {
        shape: 'polygon',
        // 全局设置指标文字大小为14px
        name: {
          textStyle: {
            // color: '#333', origin
            color: '#fff',
            padding: [3, 5]
          }
        },
        indicator: [
          { name: '累计奖励', max: 100 },
          { name: '稳定性', max: 100 },
          { name: '收敛速度', max: 100 },
          { name: '复杂度', max: 100 },
          { name: '样本效率', max: 100 },
          { name: '鲁棒性', max: 100 },
        ],
        axisLine: {
          lineStyle: {
            // color: '#ddd' origin
            color: '#fff'
          }
        },
        splitLine: {
          lineStyle: {
            color: '#fff',
          }
        },
        splitArea: {
          show: true,
          areaStyle: {
            color: ['#f2f3f5', '#121724']
          }
        }
      },
      textStyle: { fontSize: window.screen.width > 1536 ? '22' : '14' },
      series: [{
        name: '模型性能',
        type: 'radar',
        areaStyle: {
          normal: {
            opacity: 0.5,
            color: 'rgba(25, 183, 182, 0.4)' // 区域色
          }
        },
        lineStyle: {
          color: 'rgb(25, 183, 182)', // 区域线条
        },
        data: [
          {
            value: [85, 90, 78, 88, 92, 65],
            name: '模型性能'
          }
        ]
      }]
    }
    radarChartInstance.setOption(radarOption)
  }

  // 初始化柱状图
  if (barChart.value) {
    barChartInstance = echarts.init(barChart.value)
    const barOption = {
      title: {
        text: '总体评价',
        left: 'left',
        textStyle: {
          color: '#fff',
          fontSize: window.screen.width > 1536 ? '22' : '14'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        textStyle: {
          fontSize: window.screen.width > 1536 ? '20' : '14',  // 文字大小设为14px
          color: '#333',  // 文字颜色设为#333
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '0%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['SAC', 'PPO', 'A2C', 'A3C', 'TRPO'],
        axisLabel: {
          color: '#fff',
          formatter: '{value}',
          fontSize: window.screen.width > 1536 ? '20' : '12'
        },
        axisLine: {
          show: true,
          lineStyle: {
            type: 'solid',
            color: '#fff'
          }
        }
      },
      yAxis: {
        type: 'value',
        name: '',
        max: 100,
        splitLine: {
          lineStyle: {
            type: 'dashed',  // 设置为虚线
            color: '#ffffff', //坐标轴线颜色
            width: '2',
          },
        },
        axisLabel: {
          color: '#fff',
          formatter: '{value}',
          fontSize: window.screen.width > 1536 ? '20' : '12'
        },
        axisLine: {
          show: false,
        }
      },
      series: [
        {
          name: '评分',
          type: 'bar',
          barWidth: '40%',
          data: [78, 92, 65, 45, 55],
          itemStyle: {
            color: '#165dff'
          }
        }
      ]
    }
    barChartInstance.setOption(barOption)
  }
}

// 窗口大小改变时重置图表大小
function resizeCharts() {
  radarChartInstance?.resize()
  barChartInstance?.resize()
}

// 组件挂载时获取数据和初始化图表
onMounted(() => {
  fetchAlgorithms()
  fetchTrainTasks()
  initCharts()
  nextTick(() => {
    startMarquee() // 页面加载后立即启动跑马灯
    window.addEventListener('resize', resizeCharts)
  })
})

// 组件销毁前清理事件监听
onBeforeUnmount(() => {
  window.removeEventListener('resize', resizeCharts)
  radarChartInstance?.dispose()
  barChartInstance?.dispose()
  clearInterval(intervalId)
})

// 跳转到深度学习界面
function startTraining() {
  // 获取当前选中的算法
  const selectedAlgo = algorithms.value.find(algo => algo.id === currentAlgorithm.value)

  // 跳转到深度学习页面
  router.push({
    path: '/ai-model/deep-learning', // 跳转到DeepLearningPage
    query: {
      algorithm_id: selectedAlgo?.id,
      algorithm_name: selectedAlgo?.name,
      algorithm_version: selectedAlgo?.version
    }
  })
}

// 跳转到模型管理页面并打开上传模型对话框
function importModel() {
  // 跳转到模型管理页面
  router.push({
    path: '/system/model', // 正确的模型管理页面路径
    query: {
      openUploadDialog: 'true' // 传递参数，表示需要打开上传对话框
    }
  })
}

// 跳转到数据集管理页面并打开上传数据集对话框
function uploadDataset() {
  // 跳转到数据集管理页面
  router.push({
    path: '/system/dataset', // 数据集管理页面路径
    query: {
      openUploadDialog: 'true' // 传递参数，表示需要打开上传对话框
    }
  })
}

// 跳转到强化学习页面
function startValidation() {
  // 获取当前选中的算法
  const selectedAlgo = algorithms.value.find(algo => algo.id === currentAlgorithm.value)

  // 跳转到强化学习页面
  router.push({
    path: '/ai-model/reinforcement', // 强化学习页面路径
    query: {
      algorithm_id: selectedAlgo?.id,
      algorithm_name: selectedAlgo?.name,
      algorithm_version: selectedAlgo?.version,
      validation_mode: 'true' // 标记为验证模式
    }
  })
}

// 添加算法
function addAlgorithm() {
  showAddAlgorithmDialog.value = true
}

// 提交添加算法表单
async function onSubmitAlgorithm() {
  try {
    submitting.value = true
    const response = await api.post('/backend/algorithms/', newAlgorithm.value)

    if (response.data) {
      notify('添加算法成功', 'positive')
      showAddAlgorithmDialog.value = false
      // 重置表单
      newAlgorithm.value = {
        name: '',
        artifact_name: '',
        desc: ''
      }
      // 刷新算法列表
      fetchAlgorithms()
    }
  } catch (error) {
    console.error('添加算法失败:', error)
    notify('添加算法失败: ' + (error.response?.data?.message || error.message), 'negative')
  } finally {
    submitting.value = false
  }
}
</script>

<style lang="scss" scoped>
.q-page {
  background-color: #f5f7fa;
}

.algorithm-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  height: 100%;
  position: relative;
  width: 3.125rem;
  height: 1.875rem;

  .imgType {
    position: absolute;
    left: .125rem;
    top: .075rem;
    font-size: .15rem;
    color: $slideText;
    padding: .075rem;
    border: .0125rem solid #4ab4ff, ;
    border-radius: .0625rem;
    background: rgba(87, 88, 89, .6);
  }

  .imgName {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: .55rem;
    line-height: .55rem;
    font-size: .15rem;
    text-align: center;
    background: rgba(0, 0, 0, .45);
    color: $slideText;
  }
}

.algorithm-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.selected-algorithm {
  border-color: var(--q-primary);
  box-shadow: 0 0 10px rgba(var(--q-primary-rgb), 0.3);
}

/* 确保文本不会溢出 */
.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}


.myCard {
  background: transparent !important;
  display: flex;
  border: none;
}

.showBorder {
  background: $tableBg;
  border: 1px solid transparent;
  border-image: linear-gradient(to bottom, #64778a, #293f64) 1;
}

.line {
  margin-top: .25rem;
  position: absolute;
  left: 0;
  top: 40px;
  width: 100%;
}

.cardContent {
  height: 80px;
  width: 100%;
  // padding: 10px 0px 0px 20px;
}

.mr {
  margin: 0 10px;
}

.mb {
  margin-bottom: 10px;
}

.leftTag {
  position: absolute;
  left: 10px;
  top: 6px;
  border: 1px solid #4083bc;
  border-radius: 5px;
  background: rgba(87, 88, 89, .5);
}

.title {
  font-size: .3rem;
  font-weight: 600;
}

.content {
  font-size: .225rem;
}

.font16 {
  font-size: .2rem;
}

.font14 {
  font-size: .175rem;
}

.quard-item {
  width: 3.125rem;
  height: 1.875rem;
  margin-right: .5rem;
}

.q-table__container {
  margin-top: .25rem;
  border: none !important;
  padding: 0 !important;
  min-height: 4.9rem;
  padding-left: .1875rem !important;
  padding-right: .1875rem !important;
  // padding-top:0 !important;
  // padding-bottom: .5rem !important;
  // box-shadow: 0 .0125rem .0625rem #0003, 0 .025rem .025rem #00000024, 0 .0375rem .0125rem -0.025rem #0000001f;
}

.mb10 {
  margin-bottom: .125rem;
}

.marquee-container {
  width: 100%;
  height: 3rem;
  margin: 0 auto;
  overflow: hidden;
  position: relative;
  background: $tableBg;
  margin: .125rem 0;
}

.marquee-content {
  display: flex;
  white-space: nowrap;
  width: max-content;
  padding: 0;
  list-style-type: none;
  margin-top: .35rem;
}

.marquee-item {
  display: inline-block;
  transition: transform 0.3s;
}

.marquee-item:hover {
  cursor: pointer;
  transform: scale(1.1);
}

.marquee-item.enlarged {
  z-index: 2;
}

.marquee-item.enlarged .algorithm-card {
  transform: scale(1.1);
  transition: transform 0.3s;
  box-shadow: 0 0 .0625rem #fff;
}

.algotitle {
  padding-top: .2rem;
  padding-left: .2rem;
}

.botLine {
  position: relative;
  padding-left: .2rem;
  padding-right: .2rem;

  &::after {
    content: '';
    position: absolute;
    bottom: -0.125rem;
    left: 0;
    width: 100% !important;
    height: .0125rem;
    background: #2a3f63;
  }
}

.marquee-item .algorithm-card {
  width: 100%;
}

.cus-bg {
  border-radius: .0625rem;
  background: $primary;
  border: .0125rem solid transparent;
  border-radius: .0625rem;
  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  background-image: linear-gradient(to right, $primary, $primary), linear-gradient(to right, #64778a, #293f64);
}

.cusCard {
  padding-top:.25rem;
}
.textSize{
  font-size: .3rem;
}
</style>
