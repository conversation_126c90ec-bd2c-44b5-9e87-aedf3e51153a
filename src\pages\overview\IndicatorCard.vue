<template>
  <div class="flex q-gutter-x-xs">
    <div v-for="(item, idx) in items" :key="idx" @click="$router.push(item.to)" class="col cursor-pointer card">
      <div class="flex q-pa-sm content">
        <q-separator color="primary" size="4px" vertical />
        <div class="text-bold fs-24 q-ml-md">{{ item.name }}</div>
        <div class="text-primary self-end text-digital q-ml-auto digital">{{ item.count }}</div>
        <div class="text-black self-end q-ml-xs fs-20">个</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  indicator: Object,
})

const items = computed(() => [
  {
    name: '想定',
    to: '/emulation',
    count: props.indicator?.scenarios ?? 0,
  },
  {
    name: '训练',
    to: '/training',
    count: props.indicator?.training ?? 0,
  },
  {
    name: '模型',
    to: '/model',
    count: props.indicator?.models ?? 0,
  },
])
</script>

<style lang="scss" scoped>
.card {
  padding: 2px;
  border: 3px solid transparent;

  &:hover {
    border-color: currentColor;
  }

  .content {
    height: 100px;
    background: url(assets/images/svg/indicator-bg-1.svg) right no-repeat,
      url(assets/images/svg/indicator-bg-2.svg) right no-repeat #eaeaea;
  }

  .digital {
    font-size: 80px;
    line-height: 60px;
  }
}

.body--dark .card .content {
  background-color: rgba(255, 255, 255, 0.2);
}
</style>
