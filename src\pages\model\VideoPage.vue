<template>
  <div class="flex items-center">
    <q-btn to="/model" flat>
      <SvgBack />
      <div class="fs-18 q-ml-xs">返回</div>
    </q-btn>
    <q-separator color="primary" size="3px" class="rounded-borders q-mx-md" inset vertical />
    <div class="fs-20 text-bold text-primary">{{ detail.name }}</div>
  </div>

  <div class="item-card q-mt-xs">
    <!-- <div class="fs-16 text-primary">策略推演</div> -->

    <div class="flex q-mt-md q-mt-md justify-between">
      <video id="myVideo" class="q-ml-md" height="600" width="1000" autoplay>
        <source v-if="vedioUrl" :src="vedioUrl" type="video/mp4">
        Your browser does not support the video tag.
      </video>
    </div>

  </div>

  <!-- <div class="q-mt-sm item-card playback-card">
    <PlaybackCard :model-id="detail.modelId" />
  </div> -->

  <q-inner-loading :showing="loading" />
</template>

<script setup>
import { ref } from 'vue'
import { date } from 'quasar'
import { api } from 'boot/axios'
import PlaybackCard from './PlaybackCard.vue'
import { formatFileSize } from 'assets/utils'
import SvgBack from 'assets/images/svg/back.svg'

const props = defineProps(['id'])

const loading = ref(false)
const detail = ref({})

const vedioUrl = ref('')

getDetail()

function getDetail() {
  loading.value = true
  api
    .get(`backend/models/${props.id}/`)
    .then(res => {
      loading.value = false
      const {
        name,
        file
      } = res.data

      detail.value = {
        name,
        file,
      }

      vedioUrl.value = `${window.location.origin}/common/videos/${file}`
      console.log(vedioUrl.value)
    })
    .catch(() => {
      loading.value = false
    })
}
</script>

<style lang="scss" scoped>
.item-card {
  padding: 20px;
  font-size: 18px;
  font-weight: bold;
  background: rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(15px);

  .label {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.8);
    margin-top: 25px;
    font-weight: normal;
  }
}

.playback-card {
  height: 500px;
}

.body--dark {
  .item-card {
    background-color: rgba(255, 255, 255, 0.1);

    .label {
      color: rgba(255, 255, 255, 0.5);
    }
  }
}
</style>
